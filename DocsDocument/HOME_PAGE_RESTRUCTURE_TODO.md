# 📋 HOME PAGE RESTRUCTURE TODO LIST

## 🎯 **TỔNG QUAN DỰ ÁN**
- **<PERSON><PERSON><PERSON> tiêu**: T<PERSON><PERSON> cấu trúc trang home đúng chuẩn HTML5, SEO và UI/UX
- **Thời gian dự kiến**: 2-3 tuần
- **Nguyên tắc**: Không ảnh hưởng đến trang đang hoạt động

---

## 📅 **PHASE 1: HTML SEMANTIC STRUCTURE (Week 1)**

### 🔥 **Day 1-2: Setup Foundation**

#### ✅ **Task 1.1: Create Enhanced Layout Components**
**Priority**: 🚨 Critical

**Files to create:**
```
/src/shared/components/layout/
├── page-wrapper/
│   ├── v2/
│   │   ├── PageWrapper.tsx
│   │   ├── styles/page-wrapper.css
│   │   └── index.ts
│   └── index.ts
├── main-content/
│   ├── v1/
│   │   ├── MainContent.tsx
│   │   ├── styles/main-content.css
│   │   └── index.ts
│   └── index.ts
├── section-wrapper/
│   ├── v1/
│   │   ├── SectionWrapper.tsx
│   │   ├── styles/section-wrapper.css
│   │   └── index.ts
│   └── index.ts
└── skip-link/
    ├── v1/
    │   ├── SkipLink.tsx
    │   ├── styles/skip-link.css
    │   └── index.ts
    └── index.ts
```

**Implementation Details:**

1. **PageWrapper Component** - `/src/shared/components/layout/page-wrapper/v2/PageWrapper.tsx`
```tsx
interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
  skipLinkTarget?: string;
  lang?: string;
}

const PageWrapper: React.FC<PageWrapperProps> = ({
  children,
  className = '',
  skipLinkTarget = '#main-content',
  lang = 'en'
}) => (
  <div className={`min-h-screen flex flex-col ${className}`} lang={lang}>
    <SkipLink href={skipLinkTarget} />
    {children}
  </div>
);
```

2. **MainContent Component** - `/src/shared/components/layout/main-content/v1/MainContent.tsx`
```tsx
interface MainContentProps {
  children: React.ReactNode;
  id?: string;
  className?: string;
}

const MainContent: React.FC<MainContentProps> = ({
  children,
  id = 'main-content',
  className = ''
}) => (
  <main 
    id={id}
    role="main"
    className={`flex-1 relative z-10 ${className}`}
    tabIndex={-1}
  >
    {children}
  </main>
);
```

3. **SectionWrapper Component** - `/src/shared/components/layout/section-wrapper/v1/SectionWrapper.tsx`
```tsx
interface SectionWrapperProps {
  children: React.ReactNode;
  id?: string;
  ariaLabelledBy?: string;
  ariaLabel?: string;
  variant?: 'hero' | 'content' | 'feature' | 'highlight';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const SectionWrapper: React.FC<SectionWrapperProps> = ({
  children,
  id,
  ariaLabelledBy,
  ariaLabel,
  variant = 'content',
  spacing = 'md',
  className = ''
}) => {
  const variantClasses = {
    hero: 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900',
    content: 'bg-transparent',
    feature: 'bg-slate-800/10 backdrop-blur-sm',
    highlight: 'bg-gradient-to-r from-blue-500/10 to-purple-500/10'
  };
  
  const spacingClasses = {
    none: '',
    sm: 'py-8',
    md: 'py-12',
    lg: 'py-16',
    xl: 'py-20'
  };
  
  return (
    <section
      id={id}
      aria-labelledby={ariaLabelledBy}
      aria-label={ariaLabel}
      className={`${variantClasses[variant]} ${spacingClasses[spacing]} ${className}`}
    >
      <div className="container mx-auto px-6">
        {children}
      </div>
    </section>
  );
};
```

4. **SkipLink Component** - `/src/shared/components/layout/skip-link/v1/SkipLink.tsx`
```tsx
interface SkipLinkProps {
  href: string;
  children?: React.ReactNode;
}

const SkipLink: React.FC<SkipLinkProps> = ({
  href,
  children = 'Skip to main content'
}) => (
  <a
    href={href}
    className="skip-link absolute top-0 left-0 z-[9999] bg-blue-600 text-white px-4 py-2 rounded-br transform -translate-y-full focus:translate-y-0 transition-transform"
  >
    {children}
  </a>
);
```

**Acceptance Criteria:**
- [ ] All layout components created with proper TypeScript interfaces
- [ ] CSS files organized in respective `styles/` folders
- [ ] Proper export structure with `index.ts` files
- [ ] Components follow existing architecture patterns

---

#### ✅ **Task 1.2: Create HomePage v2 with Semantic HTML**
**Priority**: 🚨 Critical

**Files to create:**
```
/src/features/home/<USER>/v2/
├── Page.tsx
├── styles/home-page-v2.css
└── index.ts
```

**Implementation:** `/src/features/home/<USER>/v2/Page.tsx`
```tsx
import Header from '@/shared/components/layout/header';
import Footer from '@/shared/components/layout/footer';
import MainContent from '@/shared/components/layout/main-content';
import SectionWrapper from '@/shared/components/layout/section-wrapper';
import PageWrapper from '@/shared/components/layout/page-wrapper';
import Breadcrumbs from '@/shared/components/ui/breadcrumbs';
import { Hero, FeaturedMatches, LastNews, LiveStatistics } from '../../components';
import './styles/home-page-v2.css';

const HomePageV2 = () => {
  const breadcrumbItems = [
    { name: 'Home', href: '/', current: true }
  ];

  return (
    <PageWrapper className="home-page-v2">
      <Header />
      
      <MainContent>
        {/* Breadcrumbs for SEO */}
        <Breadcrumbs items={breadcrumbItems} />
        
        {/* Hero Section */}
        <SectionWrapper
          id="hero-section"
          variant="hero"
          spacing="none"
          ariaLabel="Main dashboard and featured content"
        >
          <div className="hero-content">
            <h1 className="sr-only">
              Sports Command Center - Live Sports Dashboard and Analytics
            </h1>
            <Hero />
          </div>
        </SectionWrapper>

        {/* Featured Matches Section */}
        <SectionWrapper
          id="featured-matches-section"
          variant="content"
          spacing="lg"
          ariaLabelledBy="featured-matches-heading"
        >
          <div className="section-header">
            <h2 id="featured-matches-heading" className="section-title">
              Featured Matches
            </h2>
            <p className="section-description">
              Live and upcoming matches from top leagues worldwide
            </p>
          </div>
          <FeaturedMatches />
        </SectionWrapper>

        {/* Latest News Section */}
        <SectionWrapper
          id="latest-news-section"
          variant="feature"
          spacing="lg"
          ariaLabelledBy="latest-news-heading"
        >
          <div className="section-header">
            <h2 id="latest-news-heading" className="section-title">
              Latest Sports News
            </h2>
            <p className="section-description">
              Stay updated with the latest news and insights from the sports world
            </p>
          </div>
          <LastNews />
        </SectionWrapper>

        {/* Live Statistics Section (Optional) */}
        {/* 
        <SectionWrapper
          id="live-statistics-section"
          variant="highlight"
          spacing="lg"
          ariaLabelledBy="live-statistics-heading"
        >
          <div className="section-header">
            <h2 id="live-statistics-heading" className="section-title">
              Live Statistics Dashboard
            </h2>
          </div>
          <LiveStatistics />
        </SectionWrapper>
        */}
      </MainContent>
      
      <Footer />
    </PageWrapper>
  );
};

export default HomePageV2;
```

**CSS File:** `/src/features/home/<USER>/v2/styles/home-page-v2.css`
```css
/* Home Page V2 - Semantic Structure Styles */
.home-page-v2 {
  min-height: 100vh;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  @apply text-3xl lg:text-4xl font-bold text-white mb-4;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  @apply text-lg text-gray-300 max-w-2xl mx-auto;
}

/* Skip Link Styles */
.skip-link {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  text-decoration: none;
  border-radius: 0 0 0.5rem 0;
  transform: translateY(-100%);
  transition: transform 0.2s ease;
}

.skip-link:focus {
  transform: translateY(0);
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Hero Content */
.hero-content {
  position: relative;
  z-index: 10;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-title {
    @apply text-2xl lg:text-3xl;
  }
  
  .section-description {
    @apply text-base;
  }
}
```

**Acceptance Criteria:**
- [ ] Proper HTML5 semantic structure with `<main>`, `<section>`, `<h1>`, `<h2>`
- [ ] Skip link for accessibility
- [ ] Proper heading hierarchy (h1 -> h2)
- [ ] ARIA labels and descriptions
- [ ] Screen reader friendly content
- [ ] Responsive CSS implementation

---

#### ✅ **Task 1.3: Create Breadcrumbs Component**
**Priority**: ⚠️ High

**Files to create:**
```
/src/shared/components/ui/breadcrumbs/
├── v1/
│   ├── Breadcrumbs.tsx
│   ├── styles/breadcrumbs.css
│   └── index.ts
└── index.ts
```

**Implementation:** `/src/shared/components/ui/breadcrumbs/v1/Breadcrumbs.tsx`
```tsx
interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className = '',
  showHome = true
}) => {
  const allItems = showHome && items[0]?.href !== '/' 
    ? [{ name: 'Home', href: '/' }, ...items]
    : items;

  return (
    <nav aria-label="Breadcrumb" className={`breadcrumbs ${className}`}>
      <ol 
        className="breadcrumb-list"
        itemScope 
        itemType="https://schema.org/BreadcrumbList"
      >
        {allItems.map((item, index) => (
          <li
            key={item.href}
            className={`breadcrumb-item ${item.current ? 'current' : ''}`}
            itemProp="itemListElement"
            itemScope
            itemType="https://schema.org/ListItem"
          >
            {item.current ? (
              <span 
                className="breadcrumb-current"
                aria-current="page"
                itemProp="name"
              >
                {item.name}
              </span>
            ) : (
              <a 
                href={item.href}
                className="breadcrumb-link"
                itemProp="item"
              >
                <span itemProp="name">{item.name}</span>
              </a>
            )}
            <meta itemProp="position" content={String(index + 1)} />
            
            {index < allItems.length - 1 && (
              <span className="breadcrumb-separator" aria-hidden="true">
                /
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
```

**CSS:** `/src/shared/components/ui/breadcrumbs/v1/styles/breadcrumbs.css`
```css
/* Breadcrumbs Component Styles */
.breadcrumbs {
  padding: 1rem 0;
  background: transparent;
}

.breadcrumb-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 0.875rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-link {
  color: #94a3b8; /* text-slate-400 */
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: #3b82f6; /* text-blue-500 */
}

.breadcrumb-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.25rem;
}

.breadcrumb-current {
  color: #ffffff; /* text-white */
  font-weight: 500;
}

.breadcrumb-separator {
  color: #64748b; /* text-slate-500 */
  font-weight: 300;
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .breadcrumbs {
    padding: 0.5rem 0;
  }
  
  .breadcrumb-list {
    font-size: 0.75rem;
    gap: 0.25rem;
  }
  
  .breadcrumb-item {
    gap: 0.25rem;
  }
}
```

**Acceptance Criteria:**
- [ ] Schema.org structured data for SEO
- [ ] Proper ARIA labels for accessibility
- [ ] Responsive design
- [ ] Keyboard navigation support
- [ ] Current page indication

---

### 🔥 **Day 3-4: Update Main Page Structure**

#### ✅ **Task 1.4: Update Home Page Index to Use v2**
**Priority**: 🚨 Critical

**Files to modify:**
- `/src/features/home/<USER>/index.ts`
- `/src/features/home/<USER>

**Changes:**

1. **Update pages index:** `/src/features/home/<USER>/index.ts`
```tsx
// Export v2 as default while keeping v1 available
export { default } from './v2';

// Named exports for version switching
export { default as HomePageV1 } from './v1';
export { default as HomePageV2 } from './v2';
```

2. **Update config:** `/src/features/home/<USER>
```tsx
export const homeFeatureConfig = {
  pages: {
    homePage: 'v2', // Changed from v1 to v2
  },
  components: {
    hero: 'v3',
    featuredMatches: 'v2',
    lastNews: 'v6',
    liveStatistics: 'v1'
  }
} as const;
```

**Acceptance Criteria:**
- [ ] Homepage now uses v2 with semantic structure
- [ ] v1 still available for rollback
- [ ] Configuration properly updated
- [ ] No breaking changes to existing functionality

---

#### ✅ **Task 1.5: Enhance Header with Proper Navigation Semantics**
**Priority**: ⚠️ High

**Files to modify:**
- `/src/shared/components/layout/header/v3/Header.tsx`

**Changes to implement:**
```tsx
// Add proper navigation structure
<header role="banner" className="header-v3 ...">
  {/* Top bar */}
  <div className="header-v3-topbar ...">
    {/* ... existing content ... */}
  </div>
  
  {/* Main navigation */}
  <div className="header-v3-main ...">
    <div className="container mx-auto ...">
      {/* Logo */}
      <div className="flex items-center ...">
        <a href="/" aria-label="Sports Command Center - Go to homepage">
          <div className="logo ...">
            <span className="text-white text-xl">⚽</span>
          </div>
        </a>
      </div>
      
      {/* Main Navigation */}
      <nav role="navigation" aria-label="Main navigation">
        <ul role="menubar" className="nav-wrapper ...">
          {navItems.map((item) => (
            <li key={item.href} role="none">
              <a
                href={item.href}
                role="menuitem"
                aria-current={item.isActive ? 'page' : undefined}
                className={`nav-item-v3 ...`}
              >
                {/* ... existing content ... */}
              </a>
            </li>
          ))}
        </ul>
      </nav>
      
      {/* Search */}
      <div role="search" aria-label="Site search">
        <input
          type="search"
          placeholder="Search..."
          aria-label="Search sports content, teams, and matches"
          className="search-input-v3 ..."
        />
      </div>
    </div>
  </div>
</header>
```

**Acceptance Criteria:**
- [ ] Proper `role` attributes for navigation
- [ ] `aria-label` for accessibility
- [ ] `aria-current` for active navigation
- [ ] Semantic `<nav>` and `<ul>` structure
- [ ] Descriptive `aria-label` for search

---

### 🔥 **Day 5-7: SEO Enhancement**

#### ✅ **Task 1.6: Add Structured Data**
**Priority**: 🚨 Critical

**Files to create:**
```
/src/lib/seo/
├── structured-data.ts
├── meta-tags.ts
└── index.ts
```

**Implementation:** `/src/lib/seo/structured-data.ts`
```tsx
export interface MatchStructuredData {
  name: string;
  startDate: string;
  endDate?: string;
  location: {
    name: string;
    address?: string;
  };
  homeTeam: {
    name: string;
    logo?: string;
  };
  awayTeam: {
    name: string;
    logo?: string;
  };
  league: string;
}

export const generateSportsOrganizationLD = () => ({
  "@context": "https://schema.org",
  "@type": "SportsOrganization",
  "name": "Sports Command Center",
  "description": "Advanced sports management platform with real-time match tracking, live scores, and comprehensive analytics.",
  "url": process.env.NEXT_PUBLIC_SITE_URL || "https://localhost:3000",
  "logo": `${process.env.NEXT_PUBLIC_SITE_URL}/logo.png`,
  "sameAs": [
    "https://twitter.com/sportscommand",
    "https://facebook.com/sportscommand"
  ],
  "sport": ["Football", "Soccer", "Basketball", "Tennis"],
  "foundingDate": "2025",
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "email": "<EMAIL>"
  }
});

export const generateSportsEventLD = (match: MatchStructuredData) => ({
  "@context": "https://schema.org",
  "@type": "SportsEvent",
  "name": `${match.homeTeam.name} vs ${match.awayTeam.name}`,
  "description": `${match.league} match between ${match.homeTeam.name} and ${match.awayTeam.name}`,
  "startDate": match.startDate,
  "endDate": match.endDate,
  "location": {
    "@type": "Place",
    "name": match.location.name,
    "address": match.location.address
  },
  "homeTeam": {
    "@type": "SportsTeam",
    "name": match.homeTeam.name,
    "logo": match.homeTeam.logo
  },
  "awayTeam": {
    "@type": "SportsTeam", 
    "name": match.awayTeam.name,
    "logo": match.awayTeam.logo
  },
  "sport": "Football",
  "organizer": {
    "@type": "SportsOrganization",
    "name": match.league
  }
});

export const generateWebPageLD = (pageData: {
  title: string;
  description: string;
  url: string;
  image?: string;
  breadcrumbs?: Array<{ name: string; url: string }>;
}) => ({
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": pageData.title,
  "description": pageData.description,
  "url": pageData.url,
  "image": pageData.image,
  "inLanguage": "en",
  "isPartOf": {
    "@type": "WebSite",
    "name": "Sports Command Center",
    "url": process.env.NEXT_PUBLIC_SITE_URL
  },
  "breadcrumb": pageData.breadcrumbs ? {
    "@type": "BreadcrumbList",
    "itemListElement": pageData.breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  } : undefined
});
```

**Usage in HomePage v2:**
```tsx
// In Page.tsx, add JSON-LD script
import { generateSportsOrganizationLD, generateWebPageLD } from '@/lib/seo';

const HomePageV2 = () => {
  const structuredData = {
    organization: generateSportsOrganizationLD(),
    webpage: generateWebPageLD({
      title: "Sports Command Center - Live Sports Dashboard",
      description: "Advanced sports management platform with real-time match tracking and analytics",
      url: "/",
      image: "/og-image.jpg"
    })
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            structuredData.organization,
            structuredData.webpage
          ])
        }}
      />
      <PageWrapper>
        {/* ... rest of component */}
      </PageWrapper>
    </>
  );
};
```

**Acceptance Criteria:**
- [ ] Schema.org structured data for sports organization
- [ ] Match/event structured data
- [ ] WebPage structured data with breadcrumbs
- [ ] Proper JSON-LD format
- [ ] Environment-based URL configuration

---

## 📅 **PHASE 2: SEO & META OPTIMIZATION (Week 2)**

### ✅ **Task 2.1: Enhanced Metadata System**
**Priority**: 🚨 Critical

**Files to create/modify:**
- `/src/app/layout.tsx`
- `/src/lib/seo/meta-tags.ts`

**Implementation:** `/src/lib/seo/meta-tags.ts`
```tsx
export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
}

export const defaultSEO: SEOConfig = {
  title: 'Sports Command Center - Live Sports Dashboard & Analytics',
  description: 'Advanced sports management platform with real-time match tracking, live scores, comprehensive analytics, and latest sports news. Follow your favorite teams and leagues.',
  keywords: [
    'sports dashboard',
    'live sports scores',
    'football matches',
    'soccer analytics',
    'sports news',
    'match tracking',
    'sports statistics',
    'premier league',
    'champions league',
    'sports management'
  ],
  image: '/og-image.jpg',
  url: '/',
  type: 'website'
};

export const generateMetadata = (config: Partial<SEOConfig> = {}): any => {
  const seo = { ...defaultSEO, ...config };
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://localhost:3000';
  const fullUrl = `${baseUrl}${seo.url}`;
  const fullImageUrl = seo.image?.startsWith('http') ? seo.image : `${baseUrl}${seo.image}`;

  return {
    title: seo.title,
    description: seo.description,
    keywords: seo.keywords?.join(', '),
    authors: seo.author ? [{ name: seo.author }] : [{ name: 'Sports Command Center' }],
    creator: 'Sports Command Center',
    publisher: 'Sports Command Center',
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: fullUrl,
    },
    openGraph: {
      title: seo.title,
      description: seo.description,
      url: fullUrl,
      siteName: 'Sports Command Center',
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: seo.title,
        },
      ],
      locale: 'en_US',
      type: seo.type,
      publishedTime: seo.publishedTime,
      modifiedTime: seo.modifiedTime,
      section: seo.section,
    },
    twitter: {
      card: 'summary_large_image',
      title: seo.title,
      description: seo.description,
      images: [fullImageUrl],
      creator: '@sportscommand',
    },
    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: process.env.GOOGLE_VERIFICATION,
      yandex: process.env.YANDEX_VERIFICATION,
      yahoo: process.env.YAHOO_VERIFICATION,
    },
  };
};
```

**Update Layout:** `/src/app/layout.tsx`
```tsx
import { generateMetadata as generateSEOMetadata } from '@/lib/seo/meta-tags';

export const metadata = generateSEOMetadata({
  title: 'Sports Command Center - Live Sports Dashboard & Analytics',
  description: 'Advanced sports management platform with real-time match tracking, live scores, comprehensive analytics, and latest sports news.',
  keywords: [
    'sports dashboard',
    'live sports scores', 
    'football matches',
    'soccer analytics',
    'sports news',
    'match tracking'
  ]
});

// Rest of layout component...
```

**Acceptance Criteria:**
- [ ] Comprehensive meta tags for SEO
- [ ] Open Graph tags for social sharing
- [ ] Twitter Card tags
- [ ] Proper robots.txt directives
- [ ] Environment-based configuration
- [ ] Google/search engine verification tags

---

### ✅ **Task 2.2: Create robots.txt and sitemap.xml**
**Priority**: ⚠️ High

**Files to create:**
- `/src/app/robots.ts` (already exists, enhance it)
- `/src/app/sitemap.ts` (already exists, enhance it)

**Enhanced robots.ts:**
```tsx
import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://localhost:3000';
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/static/',
          '/*.json$',
          '/private/'
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: ['/api/', '/admin/'],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
```

**Enhanced sitemap.ts:**
```tsx
import { MetadataRoute } from 'next';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://localhost:3000';
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/news`,
      lastModified: new Date(),
      changeFrequency: 'hourly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/matches`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/teams`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
  ];

  // Dynamic pages (you can fetch from API later)
  const dynamicPages: MetadataRoute.Sitemap = [
    // Add match pages, news articles, team pages etc.
  ];

  return [...staticPages, ...dynamicPages];
}
```

**Acceptance Criteria:**
- [ ] Proper robots.txt with search engine directives
- [ ] Comprehensive sitemap with all pages
- [ ] Priority and change frequency set correctly
- [ ] Environment-based URL configuration

---

## 📅 **PHASE 3: COMPONENT ENHANCEMENT (Week 2-3)**

### ✅ **Task 3.1: Enhance Components with Semantic HTML**
**Priority**: ⚠️ High

**Components to update:**
1. **FeaturedMatches Component**
2. **LastNews Component** 
3. **Hero Component**

**Example for FeaturedMatches:**
```tsx
// Add semantic HTML structure
const FeaturedMatches = () => (
  <section aria-labelledby="featured-matches-title">
    <header className="section-header">
      <h2 id="featured-matches-title">Featured Matches</h2>
      <p>Live and upcoming matches from top leagues</p>
    </header>
    
    <div className="matches-grid" role="list">
      {matches.map((match) => (
        <article 
          key={match.id}
          className="match-card"
          role="listitem"
          itemScope
          itemType="https://schema.org/SportsEvent"
        >
          <header className="match-header">
            <h3 itemProp="name">
              {match.homeTeam} vs {match.awayTeam}
            </h3>
            <time 
              dateTime={match.dateTime}
              itemProp="startDate"
            >
              {match.displayTime}
            </time>
          </header>
          
          <div className="match-details">
            <div className="teams">
              <div className="team" itemProp="homeTeam" itemScope itemType="https://schema.org/SportsTeam">
                <img src={match.homeTeam.logo} alt={`${match.homeTeam.name} logo`} />
                <span itemProp="name">{match.homeTeam.name}</span>
              </div>
              <div className="team" itemProp="awayTeam" itemScope itemType="https://schema.org/SportsTeam">
                <img src={match.awayTeam.logo} alt={`${match.awayTeam.name} logo`} />
                <span itemProp="name">{match.awayTeam.name}</span>
              </div>
            </div>
          </div>
          
          <footer className="match-footer">
            <a 
              href={`/matches/${match.id}`}
              aria-label={`View details for ${match.homeTeam} vs ${match.awayTeam} match`}
            >
              View Match Details
            </a>
          </footer>
        </article>
      ))}
    </div>
  </section>
);
```

**Acceptance Criteria:**
- [ ] Proper semantic HTML (`<article>`, `<header>`, `<footer>`)
- [ ] Schema.org microdata
- [ ] ARIA labels and roles
- [ ] Accessible images with alt text
- [ ] Proper heading hierarchy

---

## 📅 **PHASE 4: TESTING & DEPLOYMENT (Week 3)**

### ✅ **Task 4.1: SEO Testing**
**Priority**: ⚠️ High

**Testing checklist:**
- [ ] Google Lighthouse SEO score (target: 95+)
- [ ] Rich snippets validation (Google Rich Results Test)
- [ ] Meta tags validation (Facebook Debugger, Twitter Card Validator)
- [ ] Structured data validation (Google Structured Data Testing Tool)
- [ ] Mobile-friendly test (Google Mobile-Friendly Test)
- [ ] Page speed insights (target: 90+)

**Tools to use:**
- Google Lighthouse
- Google Search Console
- Facebook Sharing Debugger
- Twitter Card Validator
- Schema.org Validator

### ✅ **Task 4.2: Accessibility Testing**
**Priority**: ⚠️ High

**Testing checklist:**
- [ ] Screen reader testing (NVDA, JAWS)
- [ ] Keyboard navigation testing
- [ ] Color contrast validation (WCAG AA)
- [ ] Focus management testing
- [ ] ARIA labels validation
- [ ] Semantic HTML validation

### ✅ **Task 4.3: Cross-browser Testing**
**Priority**: 📝 Medium

**Browsers to test:**
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile Safari (iOS)
- [ ] Chrome Mobile (Android)

### ✅ **Task 4.4: Performance Testing**
**Priority**: 📝 Medium

**Metrics to test:**
- [ ] First Contentful Paint (FCP) < 1.8s
- [ ] Largest Contentful Paint (LCP) < 2.5s
- [ ] First Input Delay (FID) < 100ms
- [ ] Cumulative Layout Shift (CLS) < 0.1
- [ ] Time to Interactive (TTI) < 3.8s

---

## 🚀 **DEPLOYMENT STRATEGY**

### **A/B Testing Approach**
1. **Deploy v2 as feature flag**: Allow switching between v1 and v2
2. **Gradual rollout**: Start with 10% traffic to v2
3. **Monitor metrics**: SEO rankings, user engagement, performance
4. **Full rollout**: If metrics improve, switch to 100% v2

### **Rollback Plan**
- Keep v1 available for immediate rollback
- Monitor error rates and user feedback
- Have automated tests for critical user journeys

### **Success Metrics**
- **SEO Score**: Improve from ~60% to 95%+
- **Accessibility Score**: Improve from ~70% to 95%+
- **Performance**: Improve Core Web Vitals by 20-30%
- **User Engagement**: Monitor bounce rate, time on page
- **Search Rankings**: Track organic search performance

---

## 📊 **TIMELINE SUMMARY**

| Week | Phase | Key Deliverables | Priority |
|------|-------|------------------|----------|
| 1 | HTML Structure | Layout components, HomePage v2, Semantic HTML | 🚨 Critical |
| 2 | SEO Enhancement | Structured data, Meta tags, Sitemap | 🚨 Critical |
| 2-3 | Component Enhancement | Semantic components, ARIA labels | ⚠️ High |
| 3 | Testing & Deployment | SEO testing, Accessibility, Performance | ⚠️ High |

---

## 🎯 **NEXT STEPS**

1. **Start with Task 1.1**: Create layout components foundation
2. **Get approval**: Review Task 1.1 implementation before proceeding
3. **Implement incrementally**: Complete each task before moving to next
4. **Test continuously**: Run tests after each major change
5. **Document progress**: Update this TODO as tasks are completed

---

**💡 Remember**: This restructure maintains the existing functionality while dramatically improving SEO, accessibility, and maintainability. Each phase builds on the previous one, ensuring a smooth transition without breaking the current site.
