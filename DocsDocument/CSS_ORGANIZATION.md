# CSS Organization

This document outlines the CSS organization pattern used in the APISportsGamev2-FeEnduser-templater3 project.

## Structure

All component-specific CSS files are now organized in dedicated `styles` folders within each component's directory. This organization provides several benefits:

1. **Better Separation of Concerns**: CSS files are clearly separated from component logic
2. **Improved Maintainability**: CSS files related to a specific component are grouped together
3. **Easier Navigation**: Predictable file structure makes it easier to find related CSS files
4. **Cleaner Component Directories**: Component folders are less cluttered

## CSS Import Pattern

When importing CSS files in components, use the following pattern:

```tsx
// For direct component imports
import './styles/component-name.css';

// For global CSS imports (in app/styles/components-versions.css)
@import '../path/to/component/styles/component-name.css';
```

## CSS Files Organization

- **Component-specific CSS**: Located in `[component-path]/styles/`
- **Global CSS**: Located in `src/app/styles/`
- **CSS fixes/enhancements**: Kept in the same `styles` folder with descriptive names

## Naming Convention

- Main component CSS: `[component-name]-v[version].css` (e.g., `header-v3.css`)
- CSS fixes: `[component-name]-[fix-description].css` (e.g., `header-tooltip-fix.css`)

## Next Steps

1. Consider implementing CSS modules for better CSS encapsulation
2. Explore using a CSS-in-JS solution for component-specific styling
3. Consider implementing a design system with consistent variables and themes
