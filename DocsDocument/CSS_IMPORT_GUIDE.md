# CSS Import Guide

## Performance Optimization for CSS Imports

This guide outlines the optimized approach for CSS imports in our project, which helps improve performance by loading only the CSS that is needed for each component.

## Key Principles

1. **Component-Level CSS Imports**: Each component should import its own CSS files directly.
2. **No Global Component CSS Imports**: The central `components-versions.css` file no longer imports all component CSS files.
3. **Only Shared Styles Centrally**: Only truly shared styles, variables, and utilities should be in the central CSS files.

## Correct Import Pattern

### For Components:

```tsx
// Direct component CSS import - this is the recommended approach
import './styles/component-name.css';
```

### For Global Styles:

The main `globals.css` still imports `components-versions.css`, but `components-versions.css` now only contains shared variables and utilities, not component-specific styles.

```css
/* In globals.css */
@import "./components-versions.css";
```

## Benefits

1. **Better Performance**: Only the CSS needed for components on the current page will be loaded
2. **Reduced CSS Bundle Size**: Prevents loading unused CSS for components not present on the page
3. **Faster Page Load**: Smaller CSS payloads mean faster initial page rendering
4. **Better Caching**: Component-specific CSS can be cached independently

## Implementation Details

1. All component-specific CSS files are located in the component's `styles` folder
2. Each component imports its own CSS directly
3. The `components-versions.css` file only contains:
   - Version-specific CSS variables
   - Shared utility classes
   - Common responsive utilities

## Tips for CSS Management

1. Keep component CSS focused on only what that component needs
2. Use CSS variables for theme values that might change
3. Consider using CSS modules for even better CSS encapsulation in the future
4. Make sure all new components follow this pattern
