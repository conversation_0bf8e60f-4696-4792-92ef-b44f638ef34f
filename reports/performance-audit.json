{"timestamp": "2025-06-07T11:45:20.133Z", "url": "http://localhost:5000", "metrics": {"domContentLoaded": 0, "loadComplete": 0, "firstByte": 103, "firstPaint": 364, "firstContentfulPaint": 364, "pageLoadTime": 1749296725293}, "seo": {"title": "Sports Game API - Modern Sports Dashboard & API Platform", "metaDescription": "Advanced Sports Management Dashboard with Real-time Analytics, Sports API, and comprehensive sports data platform for developers and sports enthusiasts.", "headings": [{"tag": "H1", "text": "FOOTBALLTRACKER", "id": ""}, {"tag": "H1", "text": "Sports Command Center - Live Football Dashboard", "id": "hero-heading"}, {"tag": "H1", "text": "Sân Vận Động", "id": ""}, {"tag": "H2", "text": "🔥Trận <PERSON><PERSON><PERSON>", "id": ""}, {"tag": "H2", "text": "Home Page Matches", "id": "matches-heading"}, {"tag": "H2", "text": "Featured Matches", "id": "featured-matches-heading"}, {"tag": "H2", "text": "Latest Sports News", "id": "news-heading"}, {"tag": "H2", "text": "<PERSON>ổ<PERSON> B<PERSON>", "id": "latest-news-heading"}, {"tag": "H2", "text": "Manchester City Completes Record-Breaking Midfielder Transfer", "id": ""}, {"tag": "H2", "text": "Liverpool Crushes Manchester United in Epic Derby Showdown", "id": ""}, {"tag": "H2", "text": "<PERSON><PERSON>g Haaland Shatters Premier League Goal-Scoring Records", "id": ""}, {"tag": "H2", "text": "Revolutionary VAR Technology Transforms Premier League Decision-Making", "id": ""}, {"tag": "H2", "text": "Arsenal Targets Brazilian Wonderkid in €80M Summer Swoop", "id": ""}, {"tag": "H2", "text": "Champions League Semi-Finals Set for Explosive Encounters", "id": ""}, {"tag": "H3", "text": "Manchester City", "id": ""}, {"tag": "H3", "text": "Liverpool", "id": ""}, {"tag": "H3", "text": "Live Stats", "id": ""}, {"tag": "H3", "text": "📅Upcoming Matches", "id": ""}, {"tag": "H3", "text": "SPORTS COMMAND", "id": "brand-heading"}, {"tag": "H4", "text": "Navigation", "id": "nav-heading"}, {"tag": "H4", "text": "Live Statistics", "id": "stats-heading"}, {"tag": "H4", "text": "Connect With Us", "id": "social-heading"}, {"tag": "H5", "text": "Stay Updated", "id": ""}], "images": [{"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1551698618-1dfe5d97d256%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=128&q=75", "alt": "Manchester City logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1574629810360-7efbbe195018%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=128&q=75", "alt": "Liverpool logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=64&q=75", "alt": "Real Madrid logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1431324155629-1a6deb1dec8d%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=64&q=75", "alt": "Barcelona logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D75&w=64&q=75", "alt": "Bayern Munich logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1431324155629-1a6deb1dec8d%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D75&w=64&q=75", "alt": "Borussia Dortmund logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D75&w=64&q=75", "alt": "Juventus logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1431324155629-1a6deb1dec8d%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D75&w=64&q=75", "alt": "AC Milan logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D75&w=64&q=75", "alt": "PSG logo", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1431324155629-1a6deb1dec8d%3Fw%3D80%26h%3D80%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D75&w=64&q=75", "alt": "Marseille logo", "hasAlt": true}, {"src": "http://*************/public/images/teams/85.png", "alt": "Paris Saint Germain", "hasAlt": true}, {"src": "http://*************/public/images/teams/91.png", "alt": "Monaco", "hasAlt": true}, {"src": "http://*************/public/images/teams/157.png", "alt": "Bayern Munich", "hasAlt": true}, {"src": "http://*************/public/images/teams/165.png", "alt": "Borussia Dortmund", "hasAlt": true}, {"src": "http://*************/public/images/teams/541.png", "alt": "Real Madrid", "hasAlt": true}, {"src": "http://*************/public/images/teams/529.png", "alt": "Barcelona", "hasAlt": true}, {"src": "http://*************/public/images/teams/33.png", "alt": "Manchester United", "hasAlt": true}, {"src": "http://*************/public/images/teams/40.png", "alt": "Liverpool", "hasAlt": true}, {"src": "http://*************/public/images/teams/505.png", "alt": "Inter", "hasAlt": true}, {"src": "http://*************/public/images/teams/496.png", "alt": "Juventus", "hasAlt": true}, {"src": "http://*************/public/images/teams/1983.png", "alt": "I<PERSON>bur<PERSON>", "hasAlt": true}, {"src": "http://*************/public/images/teams/10112.png", "alt": "Chacaritas", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dfaces%26auto%3Dformat%26q%3D85&w=3840&q=75", "alt": "Manchester City football transfer announcement", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1431324155629-1a6deb1dec8d%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=3840&q=75", "alt": "Liverpool vs Manchester United football match action", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1543326727-cf6c39e8f84c%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=3840&q=75", "alt": "Football player celebrating spectacular goal", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1574629810360-7efbbe195018%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=3840&q=75", "alt": "Advanced VAR technology in modern football stadium", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1553778263-73a83bab9b0c%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=3840&q=75", "alt": "Young Brazilian football talent in action", "hasAlt": true}, {"src": "http://localhost:5000/_next/image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1540747913346-19e32dc3e97e%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=3840&q=75", "alt": "Champions League trophy in magnificent stadium", "hasAlt": true}], "links": [{"href": "http://localhost:5000/#main-content", "text": "Skip to main content", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/#main-content", "text": "Skip to main content", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/", "text": "⚽FOOTBALLTRACKER", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/", "text": "🏠Home", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/live", "text": "🔴Live3", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/fixtures", "text": "📅Fixtures", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/results", "text": "⚽Results", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/highlights", "text": "🎥HighlightsNEW", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/news", "text": "📰News", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/teams", "text": "🏆Teams", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/standings", "text": "📊Standings", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/leagues/premier-league", "text": "🏴󠁧󠁢󠁥󠁮󠁧󠁿", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/leagues/champions-league", "text": "🏆", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/leagues/world-cup", "text": "🌍", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/", "text": "🏠Dashboard", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/fixtures", "text": "⚽Live Matches", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/analytics", "text": "📊Analytics", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/teams", "text": "👥Teams", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/news", "text": "📰News", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/#", "text": "💬Discord", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/#", "text": "🐦Twitter", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/#", "text": "⚡GitHub", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/#", "text": "💼LinkedIn", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/privacy", "text": "Privacy Policy", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/terms", "text": "Terms of Service", "hasTitle": false, "external": false}, {"href": "http://localhost:5000/api", "text": "API Docs", "hasTitle": false, "external": false}], "structuredData": [{"@context": "https://schema.org", "@type": "WebSite", "name": "Sports Command Center", "description": "Advanced sports management platform with real-time analytics, live match tracking, and comprehensive team statistics", "url": "https://sportscommand.com", "potentialAction": {"@type": "SearchAction", "target": {"@type": "EntryPoint", "urlTemplate": "https://sportscommand.com/search?q={search_term_string}"}, "query-input": "required name=search_term_string"}}, {"@context": "https://schema.org", "@type": "WebSite", "name": "Sports Command Center", "description": "Advanced sports management platform with real-time analytics, live match tracking, and comprehensive team statistics", "url": "https://sportscommand.com", "potentialAction": {"@type": "SearchAction", "target": {"@type": "EntryPoint", "urlTemplate": "https://sportscommand.com/search?q={search_term_string}"}, "query-input": "required name=search_term_string"}}, {"@context": "https://schema.org", "@type": "WebSite", "name": "Sports Command Center", "description": "Advanced sports management platform with real-time analytics, live match tracking, and comprehensive team statistics", "url": "https://sportscommand.com", "potentialAction": {"@type": "SearchAction", "target": {"@type": "EntryPoint", "urlTemplate": "https://sportscommand.com/search?q={search_term_string}"}, "query-input": "required name=search_term_string"}}, {"@context": "https://schema.org", "@type": "BreadcrumbList", "itemListElement": [{"@type": "ListItem", "position": 1, "name": "Home", "item": "/"}]}, {"@context": "https://schema.org", "@type": "SportsEvent", "name": "Paris Saint Germain vs Monaco", "description": "Ligue 1 match between Paris Saint Germain and Monaco", "startDate": "2025-06-07T19:00:00.000Z", "location": {"@type": "Place", "name": "Parc des Princes", "address": {"@type": "PostalAddress", "addressLocality": "Paris"}}, "homeTeam": {"@type": "SportsTeam", "name": "Paris Saint Germain"}, "awayTeam": {"@type": "SportsTeam", "name": "Monaco"}, "sport": "Football"}, {"@context": "https://schema.org", "@type": "SportsEvent", "name": "Bayern Munich vs Borussia Dortmund", "description": "Bundesliga match between Bayern Munich and Borussia Dortmund", "startDate": "2025-06-07T16:30:00.000Z", "location": {"@type": "Place", "name": "Allianz Arena", "address": {"@type": "PostalAddress", "addressLocality": "München"}}, "homeTeam": {"@type": "SportsTeam", "name": "Bayern Munich"}, "awayTeam": {"@type": "SportsTeam", "name": "Borussia Dortmund"}, "sport": "Football"}, {"@context": "https://schema.org", "@type": "SportsEvent", "name": "Real Madrid vs Barcelona", "description": "La Liga match between Real Madrid and Barcelona", "startDate": "2025-06-06T19:00:00.000Z", "location": {"@type": "Place", "name": "Santiago Bernabéu", "address": {"@type": "PostalAddress", "addressLocality": "Madrid"}}, "homeTeam": {"@type": "SportsTeam", "name": "Real Madrid"}, "awayTeam": {"@type": "SportsTeam", "name": "Barcelona"}, "sport": "Football"}, {"@context": "https://schema.org", "@type": "SportsEvent", "name": "Manchester United vs Liverpool", "description": "Premier League match between Manchester United and Liverpool", "startDate": "2025-06-06T14:00:00.000Z", "location": {"@type": "Place", "name": "Old Trafford", "address": {"@type": "PostalAddress", "addressLocality": "Manchester"}}, "homeTeam": {"@type": "SportsTeam", "name": "Manchester United"}, "awayTeam": {"@type": "SportsTeam", "name": "Liverpool"}, "sport": "Football"}, {"@context": "https://schema.org", "@type": "SportsEvent", "name": "Inter vs Juventus", "description": "Serie A match between Inter and Juventus", "startDate": "2025-06-05T18:45:00.000Z", "location": {"@type": "Place", "name": "Stadi<PERSON>", "address": {"@type": "PostalAddress", "addressLocality": "Milano"}}, "homeTeam": {"@type": "SportsTeam", "name": "Inter"}, "awayTeam": {"@type": "SportsTeam", "name": "Juventus"}, "sport": "Football"}, {"@context": "https://schema.org", "@type": "SportsEvent", "name": "Imbabura vs Chacaritas", "description": "Liga Pro Serie B match between Imbabura and Chacaritas", "startDate": "2025-06-04T00:00:00.000Z", "location": {"@type": "Place", "name": "Estadio Olímpico de Ibarra", "address": {"@type": "PostalAddress", "addressLocality": "<PERSON><PERSON><PERSON>"}}, "homeTeam": {"@type": "SportsTeam", "name": "I<PERSON>bur<PERSON>"}, "awayTeam": {"@type": "SportsTeam", "name": "Chacaritas"}, "sport": "Football"}], "score": 95, "issues": ["Found 3 H1 tags, should be exactly 1"]}, "accessibility": {"skipLinks": [{"href": "http://localhost:5000/#main-content", "text": "Skip to main content"}, {"href": "http://localhost:5000/#main-content", "text": "Skip to main content"}], "ariaLabels": 132, "landmarks": [{"tag": "header", "role": "banner", "label": ""}, {"tag": "nav", "role": "navigation", "label": "Main navigation"}, {"tag": "main", "role": "", "label": "Main content"}, {"tag": "nav", "role": "", "label": "Breadcrumb"}, {"tag": "section", "role": "", "label": "hero-heading"}, {"tag": "section", "role": "", "label": "Hero section with live football matches and statistics"}, {"tag": "header", "role": "", "label": "Live football matches overview"}, {"tag": "nav", "role": "", "label": "Football league selection"}, {"tag": "section", "role": "", "label": "live-stats-heading"}, {"tag": "section", "role": "", "label": "upcoming-matches-heading"}, {"tag": "section", "role": "", "label": "Home page football matches section"}, {"tag": "section", "role": "region", "label": "Featured football matches section with live scores and upcoming fixtures"}, {"tag": "header", "role": "", "label": ""}, {"tag": "header", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "header", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "header", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "header", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "header", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "header", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "footer", "role": "", "label": ""}, {"tag": "section", "role": "", "label": "news-heading"}, {"tag": "section", "role": "region", "label": "Latest sports news and articles section with Vietnamese content"}, {"tag": "header", "role": "", "label": ""}, {"tag": "nav", "role": "", "label": "Football leagues navigation"}, {"tag": "footer", "role": "contentinfo", "label": "Site footer with company information and navigation"}, {"tag": "section", "role": "", "label": "brand-heading"}, {"tag": "header", "role": "", "label": ""}, {"tag": "section", "role": "", "label": "nav-heading"}, {"tag": "nav", "role": "", "label": "Footer navigation menu"}, {"tag": "section", "role": "", "label": "stats-heading"}, {"tag": "section", "role": "", "label": "social-heading"}, {"tag": "nav", "role": "", "label": "Social media links"}, {"tag": "nav", "role": "", "label": "Legal and documentation links"}], "focusableElements": 73, "issues": ["32 buttons without accessible labels"], "score": 97}, "errors": [], "structuredData": [{"index": 0, "valid": true, "type": "WebSite", "context": "https://schema.org", "errors": []}, {"index": 1, "valid": true, "type": "WebSite", "context": "https://schema.org", "errors": []}, {"index": 2, "valid": true, "type": "WebSite", "context": "https://schema.org", "errors": []}, {"index": 3, "valid": true, "type": "BreadcrumbList", "context": "https://schema.org", "errors": []}, {"index": 4, "valid": true, "type": "SportsEvent", "context": "https://schema.org", "errors": []}, {"index": 5, "valid": true, "type": "SportsEvent", "context": "https://schema.org", "errors": []}, {"index": 6, "valid": true, "type": "SportsEvent", "context": "https://schema.org", "errors": []}, {"index": 7, "valid": true, "type": "SportsEvent", "context": "https://schema.org", "errors": []}, {"index": 8, "valid": true, "type": "SportsEvent", "context": "https://schema.org", "errors": []}, {"index": 9, "valid": true, "type": "SportsEvent", "context": "https://schema.org", "errors": []}], "resources": {"total": 31, "totalSize": 2173, "images": 15, "scripts": 3, "styles": 8, "largestResources": [{"name": "main-app.js?v=1749296720762", "size": "1366 KB"}, {"name": "page.js", "size": "305 KB"}, {"name": "image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dfaces%26auto%3Dformat%26q%3D85&w=828&q=75", "size": "77 KB"}, {"name": "image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1522778119026-d647f0596c20%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dfaces%26auto%3Dformat%26q%3D85&w=828&q=75", "size": "77 KB"}, {"name": "app-pages-internals.js", "size": "52 KB"}, {"name": "image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1543326727-cf6c39e8f84c%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=828&q=75", "size": "40 KB"}, {"name": "image?url=https%3A%2F%2Fimages.unsplash.com%2Fphoto-1543326727-cf6c39e8f84c%3Fw%3D800%26h%3D400%26fit%3Dcrop%26crop%3Dcenter%26auto%3Dformat%26q%3D85&w=828&q=75", "size": "40 KB"}, {"name": "93f479601ee12b01-s.p.woff2", "size": "31 KB"}, {"name": "569ce4b8f30dc480-s.p.woff2", "size": "28 KB"}, {"name": "geist-latin.woff2", "size": "28 KB"}]}, "overallScore": 97, "summary": {"seoScore": 95, "accessibilityScore": 97, "performanceGrade": "Good", "structuredDataValid": true}}