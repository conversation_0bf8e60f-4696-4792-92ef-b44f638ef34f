// NewsPage v1 - Optimized News Page with Modern UI/UX and Performance Best Practices
"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { SectionWrapper } from '@/shared/components/layout/section-wrapper';
import PageWrapper from '@/shared/components/layout/page-wrapper';
import { NewsHero, NewsGrid, NewsFilters, NewsSkeleton } from '../../../components';
import type { NewsArticle } from '../../../components';

export function NewsPage() {
        const [articles, setArticles] = useState<NewsArticle[]>([]);
        const [loading, setLoading] = useState(true);
        const [selectedCategory, setSelectedCategory] = useState<string>('all');
        const [searchQuery, setSearchQuery] = useState<string>('');

        // Simulate data fetching with optimized loading
        useEffect(() => {
                const loadArticles = async () => {
                        setLoading(true);

                        // Simulate API call with realistic delay
                        await new Promise(resolve => setTimeout(resolve, 800));

                        // Mock data - in real app this would come from API
                        const mockArticles: NewsArticle[] = [
                                {
                                        id: '1',
                                        title: 'Champions League Finals Set for Epic Showdown at Wembley Stadium',
                                        excerpt: 'Two football giants prepare for the ultimate clash in European football as Manchester City faces Real Madrid in what promises to be a historic final.',
                                        content: 'Full article content here...',
                                        author: 'John Smith',
                                        publishedAt: '2025-06-07T10:00:00Z',
                                        category: 'Champions League',
                                        tags: ['football', 'champions-league', 'final', 'wembley'],
                                        imageUrl: 'https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=800&h=400&fit=crop',
                                        slug: 'champions-league-finals-epic-showdown',
                                        readTime: 5,
                                        featured: true
                                },
                                {
                                        id: '2',
                                        title: 'Transfer Window Update: Liverpool Targets Brazilian Wonderkid',
                                        excerpt: 'Liverpool FC is reportedly preparing a €80 million bid for São Paulo\'s 18-year-old sensation who has taken Brazilian football by storm.',
                                        content: 'Full article content here...',
                                        author: 'Emma Rodriguez',
                                        publishedAt: '2025-06-07T08:30:00Z',
                                        category: 'Transfers',
                                        tags: ['transfer', 'liverpool', 'brazil', 'young-talent'],
                                        imageUrl: 'https://images.unsplash.com/photo-1553778263-73a83bab9b0c?w=800&h=400&fit=crop',
                                        slug: 'liverpool-targets-brazilian-wonderkid',
                                        readTime: 3,
                                        featured: false
                                },
                                {
                                        id: '3',
                                        title: 'Premier League Race Heats Up as Arsenal Takes the Lead',
                                        excerpt: 'Arsenal\'s stunning 3-1 victory over Manchester United puts them at the top of the Premier League table with just 5 games remaining.',
                                        content: 'Full article content here...',
                                        author: 'Michael Johnson',
                                        publishedAt: '2025-06-06T19:45:00Z',
                                        category: 'Premier League',
                                        tags: ['premier-league', 'arsenal', 'manchester-united', 'title-race'],
                                        imageUrl: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800&h=400&fit=crop',
                                        slug: 'premier-league-race-arsenal-takes-lead',
                                        readTime: 4,
                                        featured: true
                                },
                                {
                                        id: '4',
                                        title: 'La Liga Clasico: Barcelona vs Real Madrid Preview',
                                        excerpt: 'The biggest match in Spanish football is just days away as Barcelona prepares to host Real Madrid in what could be a season-defining encounter.',
                                        content: 'Full article content here...',
                                        author: 'Sofia Martinez',
                                        publishedAt: '2025-06-06T14:20:00Z',
                                        category: 'La Liga',
                                        tags: ['la-liga', 'barcelona', 'real-madrid', 'clasico'],
                                        imageUrl: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=400&fit=crop',
                                        slug: 'la-liga-clasico-barcelona-real-madrid-preview',
                                        readTime: 6,
                                        featured: false
                                },
                                {
                                        id: '5',
                                        title: 'Serie A Update: Juventus Comeback Story Continues',
                                        excerpt: 'After a difficult start to the season, Juventus has won 8 of their last 10 matches, climbing back into European competition spots.',
                                        content: 'Full article content here...',
                                        author: 'Marco Rossi',
                                        publishedAt: '2025-06-06T11:30:00Z',
                                        category: 'Serie A',
                                        tags: ['serie-a', 'juventus', 'comeback', 'european-spots'],
                                        imageUrl: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=800&h=400&fit=crop',
                                        slug: 'serie-a-juventus-comeback-story-continues',
                                        readTime: 4,
                                        featured: false
                                },
                                {
                                        id: '6',
                                        title: 'Bundesliga Spotlight: Bayern Munich\'s Youth Revolution',
                                        excerpt: 'Bayern Munich\'s decision to invest heavily in youth development is paying dividends as several academy graduates make their mark in the first team.',
                                        content: 'Full article content here...',
                                        author: 'Hans Mueller',
                                        publishedAt: '2025-06-05T16:15:00Z',
                                        category: 'Bundesliga',
                                        tags: ['bundesliga', 'bayern-munich', 'youth-development', 'academy'],
                                        imageUrl: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=800&h=400&fit=crop',
                                        slug: 'bundesliga-bayern-munich-youth-revolution',
                                        readTime: 5,
                                        featured: false
                                }
                        ];

                        setArticles(mockArticles);
                        setLoading(false);
                };

                loadArticles();
        }, []);

        // Filter articles based on category and search
        const filteredArticles = articles.filter(article => {
                const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
                const matchesSearch = searchQuery === '' ||
                        article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        article.excerpt.toLowerCase().includes(searchQuery.toLowerCase());

                return matchesCategory && matchesSearch;
        });

        return (
                <PageWrapper>
                        {/* News Hero Section */}
                        <SectionWrapper
                                id="news-hero"
                                ariaLabelledBy="news-hero-heading"
                                variant="hero"
                                spacing="lg"
                                className="professional-section-bg"
                        >
                                <NewsHero />
                        </SectionWrapper>

                        {/* News Filters Section */}
                        <SectionWrapper
                                id="news-filters"
                                ariaLabelledBy="news-filters-heading"
                                variant="content"
                                spacing="sm"
                        >
                                <NewsFilters
                                        selectedCategory={selectedCategory}
                                        onCategoryChange={setSelectedCategory}
                                        searchQuery={searchQuery}
                                        onSearchChange={setSearchQuery}
                                        articlesCount={filteredArticles.length}
                                />
                        </SectionWrapper>

                        {/* News Grid Section */}
                        <SectionWrapper
                                id="news-grid"
                                ariaLabelledBy="news-grid-heading"
                                variant="content"
                                spacing="lg"
                        >
                                <Suspense fallback={<NewsSkeleton />}>
                                        {loading ? (
                                                <NewsSkeleton />
                                        ) : (
                                                <NewsGrid articles={filteredArticles} />
                                        )}
                                </Suspense>
                        </SectionWrapper>
                </PageWrapper>
        );
}

export default NewsPage;
