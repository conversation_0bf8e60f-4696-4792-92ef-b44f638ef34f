// NewsPage.tsx - Optimized News Page with Modern UI/UX and Performance Best Practices
"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { SectionWrapper } from '@/shared/components/layout/section-wrapper';
import { PageWrapper } from '@/shared/components/layout/page-wrapper/v1/PageWrapper';
import NewsHero from '../components/NewsHero';
import NewsGrid from '../components/NewsGrid';
import NewsFilters from '../components/NewsFilters';
import NewsSkeleton from '../components/NewsSkeleton';

// Types for News
interface NewsArticle {
        id: string;
        title: string;
        excerpt: string;
        content: string;
        author: string;
        publishedAt: string;
        category: string;
        tags: string[];
        imageUrl: string;
        slug: string;
        readTime: number;
        featured: boolean;
}

export function NewsPage() {
        const [articles, setArticles] = useState<NewsArticle[]>([]);
        const [loading, setLoading] = useState(true);
        const [selectedCategory, setSelectedCategory] = useState<string>('all');
        const [searchQuery, setSearchQuery] = useState<string>('');

        // Simulate data fetching with optimized loading
        useEffect(() => {
                const loadArticles = async () => {
                        setLoading(true);

                        // Simulate API call with realistic delay
                        await new Promise(resolve => setTimeout(resolve, 800));

                        // Mock data - in real app this would come from API
                        const mockArticles: NewsArticle[] = [
                                {
                                        id: '1',
                                        title: 'Champions League Finals Set for Epic Showdown at Wembley Stadium',
                                        excerpt: 'Two football giants prepare for the ultimate clash in European football as Manchester City faces Real Madrid in what promises to be a historic final.',
                                        content: 'Full article content here...',
                                        author: 'John Smith',
                                        publishedAt: '2025-06-07T10:00:00Z',
                                        category: 'Champions League',
                                        tags: ['football', 'champions-league', 'final', 'wembley'],
                                        imageUrl: 'https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=800&h=400&fit=crop',
                                        slug: 'champions-league-finals-epic-showdown',
                                        readTime: 5,
                                        featured: true
                                },
                                {
                                        id: '2',
                                        title: 'Transfer Window Update: Liverpool Targets Brazilian Wonderkid',
                                        excerpt: 'Liverpool FC is reportedly preparing a €80 million bid for São Paulo\'s 18-year-old sensation who has taken Brazilian football by storm.',
                                        content: 'Full article content here...',
                                        author: 'Emma Rodriguez',
                                        publishedAt: '2025-06-07T08:30:00Z',
                                        category: 'Transfers',
                                        tags: ['transfer', 'liverpool', 'brazil', 'young-talent'],
                                        imageUrl: 'https://images.unsplash.com/photo-1553778263-73a83bab9b0c?w=800&h=400&fit=crop',
                                        slug: 'liverpool-targets-brazilian-wonderkid',
                                        readTime: 3,
                                        featured: false
                                },
                                // Add more mock articles...
                        ];

                        setArticles(mockArticles);
                        setLoading(false);
                };

                loadArticles();
        }, []);

        // Filter articles based on category and search
        const filteredArticles = articles.filter(article => {
                const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
                const matchesSearch = searchQuery === '' ||
                        article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        article.excerpt.toLowerCase().includes(searchQuery.toLowerCase());

                return matchesCategory && matchesSearch;
        });

        return (
                <PageWrapper>
                        {/* News Hero Section */}
                        <SectionWrapper
                                id="news-hero"
                                ariaLabelledBy="news-hero-heading"
                                variant="hero"
                                spacing="lg"
                                className="professional-section-bg"
                        >
                                <NewsHero />
                        </SectionWrapper>

                        {/* News Filters Section */}
                        <SectionWrapper
                                id="news-filters"
                                ariaLabelledBy="news-filters-heading"
                                variant="content"
                                spacing="sm"
                        >
                                <NewsFilters
                                        selectedCategory={selectedCategory}
                                        onCategoryChange={setSelectedCategory}
                                        searchQuery={searchQuery}
                                        onSearchChange={setSearchQuery}
                                        articlesCount={filteredArticles.length}
                                />
                        </SectionWrapper>

                        {/* News Grid Section */}
                        <SectionWrapper
                                id="news-grid"
                                ariaLabelledBy="news-grid-heading"
                                variant="content"
                                spacing="lg"
                        >
                                <Suspense fallback={<NewsSkeleton />}>
                                        {loading ? (
                                                <NewsSkeleton />
                                        ) : (
                                                <NewsGrid articles={filteredArticles} />
                                        )}
                                </Suspense>
                        </SectionWrapper>
                </PageWrapper>
        );
}
