// News Feature Configuration - Versioned Architecture
export const newsFeatureConfig = {
  pages: {
    newsPage: 'v1',
    newsCategoryPage: 'v1',
    newsDetailPage: 'v1'
  },
  components: {
    newsCard: 'v1',
    newsGrid: 'v1',
    categoryFilter: 'v1',
    featuredNews: 'v1',
    newsHero: 'v1',
    newsSearch: 'v1'
  }
} as const;

// Export type for TypeScript support
export type NewsFeatureConfig = typeof newsFeatureConfig;
