"use client";

import React from 'react';
import './styles/news-filters.css';

interface NewsFiltersProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  articlesCount: number;
  className?: string;
}

const categories = [
  { id: 'all', label: 'All News', count: null },
  { id: 'Champions League', label: 'Champions League', count: null },
  { id: 'Transfers', label: 'Transfers', count: null },
  { id: 'Premier League', label: 'Premier League', count: null },
  { id: 'La Liga', label: 'La Liga', count: null },
  { id: 'Serie A', label: 'Serie A', count: null },
  { id: 'Bundesliga', label: 'Bundesliga', count: null },
];

export function NewsFilters({
  selectedCategory,
  onCategoryChange,
  searchQuery,
  onSearchChange,
  articlesCount,
  className = ''
}: NewsFiltersProps) {
  return (
    <div className={`news-filters ${className}`}>
      <div className="news-filters-content">
        <div className="news-filters-header">
          <h2 id="news-filters-heading" className="news-filters-title">
            Filter News
          </h2>
          <div className="news-filters-count">
            {articlesCount} {articlesCount === 1 ? 'article' : 'articles'} found
          </div>
        </div>

        {/* Search Bar */}
        <div className="news-search-container">
          <div className="news-search-wrapper">
            <svg className="news-search-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
            <input
              type="text"
              placeholder="Search news articles..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="news-search-input"
              aria-label="Search news articles"
            />
            {searchQuery && (
              <button
                onClick={() => onSearchChange('')}
                className="news-search-clear"
                aria-label="Clear search"
              >
                <svg viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Category Filters */}
        <div className="news-category-filters">
          <div className="news-category-buttons">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => onCategoryChange(category.id)}
                className={`news-category-button ${selectedCategory === category.id ? 'active' : ''}`}
                aria-pressed={selectedCategory === category.id}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default NewsFilters;
