
.news-filters {
  margin-bottom: 2rem;
}

.news-filters-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.news-filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.news-filters-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.news-filters-count {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Search Bar */
.news-search-container {
  margin-bottom: 2rem;
}

.news-search-wrapper {
  position: relative;
  max-width: 400px;
}

.news-search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.news-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.news-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.news-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

.news-search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.news-search-clear:hover {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
}

.news-search-clear svg {
  width: 16px;
  height: 16px;
}

/* Category Filters */
.news-category-filters {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
}

.news-category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.news-category-button {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.news-category-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.news-category-button.active {
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  border-color: transparent;
  color: white;
  font-weight: 600;
}

.news-category-button.active:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Responsive Design */
@media (max-width: 768px) {
  .news-filters-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .news-search-wrapper {
    max-width: 100%;
  }
  
  .news-category-buttons {
    gap: 0.5rem;
  }
  
  .news-category-button {
    padding: 6px 12px;
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .news-filters-content {
    padding: 0 0.5rem;
  }
  
  .news-filters-title {
    font-size: 1.25rem;
  }
}
