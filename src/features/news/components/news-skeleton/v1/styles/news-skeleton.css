
.news-skeleton-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Base skeleton animation */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.news-skeleton [class*="news-skeleton-"] {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 25%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0.1) 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

/* Section Titles */
.news-skeleton-section-title {
  height: 32px;
  width: 200px;
  margin-bottom: 1.5rem;
  border-radius: 4px;
}

/* Featured Section */
.news-skeleton-featured {
  margin-bottom: 3rem;
}

.news-skeleton-featured-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.news-skeleton-featured-item.primary {
  grid-row: span 2;
}

/* Regular Section */
.news-skeleton-regular-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Card Skeletons */
.news-skeleton-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Image Skeletons */
.news-skeleton-image {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
}

.news-skeleton-card.standard .news-skeleton-image {
  height: 200px;
}

.news-skeleton-card.medium .news-skeleton-image {
  height: 250px;
}

.news-skeleton-card.large .news-skeleton-image {
  height: 400px;
}

/* Content Skeletons */
.news-skeleton-card-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.news-skeleton-card.large .news-skeleton-card-content {
  padding: 2rem;
}

/* Meta Skeletons */
.news-skeleton-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.news-skeleton-category {
  height: 20px;
  width: 100px;
  border-radius: 10px;
}

.news-skeleton-date {
  height: 16px;
  width: 80px;
  border-radius: 4px;
}

/* Title Skeletons */
.news-skeleton-title {
  border-radius: 4px;
}

.news-skeleton-title.standard {
  height: 24px;
  width: 85%;
}

.news-skeleton-title.medium {
  height: 28px;
  width: 90%;
}

.news-skeleton-title.large {
  height: 36px;
  width: 95%;
}

/* Excerpt Skeletons */
.news-skeleton-excerpt {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.news-skeleton-line {
  height: 16px;
  border-radius: 4px;
}

.news-skeleton-line.short {
  width: 70%;
}

/* Footer Skeletons */
.news-skeleton-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-top: auto;
}

.news-skeleton-author {
  height: 16px;
  width: 100px;
  border-radius: 4px;
}

.news-skeleton-read-time {
  height: 16px;
  width: 80px;
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .news-skeleton-featured-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .news-skeleton-featured-item.primary {
    grid-row: span 1;
  }
  
  .news-skeleton-regular-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .news-skeleton-content {
    padding: 0 0.5rem;
  }
  
  .news-skeleton-regular-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .news-skeleton-featured {
    margin-bottom: 2rem;
  }
  
  .news-skeleton-card-content {
    padding: 1rem;
  }
  
  .news-skeleton-card.large .news-skeleton-card-content {
    padding: 1.5rem;
  }
  
  .news-skeleton-card.large .news-skeleton-image {
    height: 300px;
  }
  
  .news-skeleton-card.medium .news-skeleton-image {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .news-skeleton-card.large .news-skeleton-image {
    height: 250px;
  }
  
  .news-skeleton-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}
