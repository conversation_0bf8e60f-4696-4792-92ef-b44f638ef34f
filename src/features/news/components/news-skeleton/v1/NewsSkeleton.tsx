"use client";

import React from 'react';
import './styles/news-skeleton.css';

interface NewsSkeletonProps {
  className?: string;
}

export function NewsSkeleton({ className = '' }: NewsSkeletonProps) {
  return (
    <div className={`news-skeleton ${className}`}>
      <div className="news-skeleton-content">
        {/* Featured Articles Skeleton */}
        <section className="news-skeleton-featured">
          <div className="news-skeleton-section-title"></div>
          <div className="news-skeleton-featured-container">
            <div className="news-skeleton-featured-item primary">
              <div className="news-skeleton-card large">
                <div className="news-skeleton-image"></div>
                <div className="news-skeleton-card-content">
                  <div className="news-skeleton-meta">
                    <div className="news-skeleton-category"></div>
                    <div className="news-skeleton-date"></div>
                  </div>
                  <div className="news-skeleton-title large"></div>
                  <div className="news-skeleton-excerpt">
                    <div className="news-skeleton-line"></div>
                    <div className="news-skeleton-line"></div>
                    <div className="news-skeleton-line short"></div>
                  </div>
                  <div className="news-skeleton-footer">
                    <div className="news-skeleton-author"></div>
                    <div className="news-skeleton-read-time"></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="news-skeleton-featured-item secondary">
              <div className="news-skeleton-card medium">
                <div className="news-skeleton-image"></div>
                <div className="news-skeleton-card-content">
                  <div className="news-skeleton-meta">
                    <div className="news-skeleton-category"></div>
                    <div className="news-skeleton-date"></div>
                  </div>
                  <div className="news-skeleton-title medium"></div>
                  <div className="news-skeleton-excerpt">
                    <div className="news-skeleton-line"></div>
                    <div className="news-skeleton-line short"></div>
                  </div>
                  <div className="news-skeleton-footer">
                    <div className="news-skeleton-author"></div>
                    <div className="news-skeleton-read-time"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Regular Articles Skeleton */}
        <section className="news-skeleton-regular">
          <div className="news-skeleton-section-title"></div>
          <div className="news-skeleton-regular-container">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="news-skeleton-regular-item">
                <div className="news-skeleton-card standard">
                  <div className="news-skeleton-image"></div>
                  <div className="news-skeleton-card-content">
                    <div className="news-skeleton-meta">
                      <div className="news-skeleton-category"></div>
                      <div className="news-skeleton-date"></div>
                    </div>
                    <div className="news-skeleton-title standard"></div>
                    <div className="news-skeleton-excerpt">
                      <div className="news-skeleton-line"></div>
                      <div className="news-skeleton-line"></div>
                      <div className="news-skeleton-line short"></div>
                    </div>
                    <div className="news-skeleton-footer">
                      <div className="news-skeleton-author"></div>
                      <div className="news-skeleton-read-time"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}

export default NewsSkeleton;
