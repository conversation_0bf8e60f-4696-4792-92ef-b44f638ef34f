"use client";

import React from 'react';
import { NewsCard } from '../../news-card';
import './styles/news-grid.css';

export interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  category: string;
  tags: string[];
  imageUrl: string;
  slug: string;
  readTime: number;
  featured: boolean;
}

interface NewsGridProps {
  articles: NewsArticle[];
  className?: string;
}

export function NewsGrid({ articles, className = '' }: NewsGridProps) {
  if (articles.length === 0) {
    return (
      <div className={`news-grid-empty ${className}`}>
        <div className="news-grid-empty-content">
          <div className="news-grid-empty-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-7-4v2m6 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m8 0V9a2 2 0 012 2v9a2 2 0 01-2 2z"/>
            </svg>
          </div>
          <h3 className="news-grid-empty-title">No Articles Found</h3>
          <p className="news-grid-empty-description">
            Try adjusting your filters or search terms to find the content you're looking for.
          </p>
        </div>
      </div>
    );
  }

  // Separate featured and regular articles
  const featuredArticles = articles.filter(article => article.featured);
  const regularArticles = articles.filter(article => !article.featured);

  return (
    <div className={`news-grid ${className}`}>
      <div className="news-grid-content">
        <h2 id="news-grid-heading" className="visually-hidden">News Articles</h2>
        
        {/* Featured Articles Section */}
        {featuredArticles.length > 0 && (
          <section className="news-grid-featured" aria-labelledby="featured-news-heading">
            <h3 id="featured-news-heading" className="news-grid-section-title">
              Featured News
            </h3>
            <div className="news-grid-featured-container">
              {featuredArticles.slice(0, 2).map((article, index) => (
                <div key={article.id} className={`news-grid-featured-item ${index === 0 ? 'primary' : 'secondary'}`}>
                  <NewsCard 
                    article={article} 
                    variant={index === 0 ? 'large' : 'medium'}
                    featured={true}
                  />
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Regular Articles Section */}
        {regularArticles.length > 0 && (
          <section className="news-grid-regular" aria-labelledby="regular-news-heading">
            <h3 id="regular-news-heading" className="news-grid-section-title">
              {featuredArticles.length > 0 ? 'More News' : 'Latest News'}
            </h3>
            <div className="news-grid-regular-container">
              {regularArticles.map((article) => (
                <div key={article.id} className="news-grid-regular-item">
                  <NewsCard article={article} variant="standard" />
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}

export default NewsGrid;
