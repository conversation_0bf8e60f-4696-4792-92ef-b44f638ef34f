
.news-grid-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.news-grid-section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
  display: inline-block;
}

/* Featured Articles */
.news-grid-featured {
  margin-bottom: 3rem;
}

.news-grid-featured-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.news-grid-featured-item.primary {
  grid-row: span 2;
}

/* Regular Articles */
.news-grid-regular-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Empty State */
.news-grid-empty {
  padding: 4rem 0;
}

.news-grid-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.news-grid-empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.3);
}

.news-grid-empty-icon svg {
  width: 100%;
  height: 100%;
}

.news-grid-empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.news-grid-empty-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.6;
  margin: 0;
}

/* Utility Class */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .news-grid-featured-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .news-grid-featured-item.primary {
    grid-row: span 1;
  }
  
  .news-grid-regular-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .news-grid-content {
    padding: 0 0.5rem;
  }
  
  .news-grid-section-title {
    font-size: 1.25rem;
  }
  
  .news-grid-regular-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .news-grid-featured {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .news-grid-empty {
    padding: 2rem 0;
  }
  
  .news-grid-empty-icon {
    width: 60px;
    height: 60px;
  }
  
  .news-grid-empty-title {
    font-size: 1.25rem;
  }
}
