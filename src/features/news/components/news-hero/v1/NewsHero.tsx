"use client";

import React from 'react';
import './styles/news-hero.css';

interface NewsHeroProps {
  className?: string;
}

export function NewsHero({ className = '' }: NewsHeroProps) {
  return (
    <div className={`news-hero ${className}`}>
      <div className="news-hero-content">
        <div className="news-hero-text">
          <h1 id="news-hero-heading" className="news-hero-title">
            Latest Sports News
          </h1>
          <p className="news-hero-subtitle">
            Stay updated with the latest happenings in the world of sports. 
            From breaking news to in-depth analysis, we've got you covered.
          </p>
        </div>
        <div className="news-hero-stats">
          <div className="news-stat">
            <span className="news-stat-number">500+</span>
            <span className="news-stat-label">Articles</span>
          </div>
          <div className="news-stat">
            <span className="news-stat-number">24/7</span>
            <span className="news-stat-label">Coverage</span>
          </div>
          <div className="news-stat">
            <span className="news-stat-number">50+</span>
            <span className="news-stat-label">Sports</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NewsHero;
