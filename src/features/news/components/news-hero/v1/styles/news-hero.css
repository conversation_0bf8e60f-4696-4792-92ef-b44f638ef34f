
.news-hero {
  padding: 4rem 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(147, 51, 234, 0.1) 100%);
  border-radius: 16px;
  margin-bottom: 2rem;
}

.news-hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.news-hero-text {
  max-width: 800px;
}

.news-hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.news-hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.news-hero-stats {
  display: flex;
  gap: 4rem;
  flex-wrap: wrap;
  justify-content: center;
}

.news-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.news-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
}

.news-stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .news-hero {
    padding: 3rem 0;
  }
  
  .news-hero-title {
    font-size: 2.5rem;
  }
  
  .news-hero-subtitle {
    font-size: 1.125rem;
  }
  
  .news-hero-stats {
    gap: 2rem;
  }
  
  .news-stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .news-hero-title {
    font-size: 2rem;
  }
  
  .news-hero-content {
    gap: 2rem;
  }
}
