
.news-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(59, 130, 246, 0.3);
}

.news-card-link {
  display: flex;
  flex-direction: column;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

/* Image Container */
.news-card-image-container {
  position: relative;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.news-card-standard .news-card-image-container {
  height: 200px;
}

.news-card-medium .news-card-image-container {
  height: 250px;
}

.news-card-large .news-card-image-container {
  height: 400px;
}

.news-card-image {
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-card-image {
  transform: scale(1.05);
}

.news-card-image-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

/* Featured Badge */
.news-card-featured-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 20px;
  z-index: 2;
}

.news-card-featured-badge svg {
  width: 12px;
  height: 12px;
}

/* Content */
.news-card-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.news-card-large .news-card-content {
  padding: 2rem;
}

/* Meta Information */
.news-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.news-card-category {
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.news-card-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Title */
.news-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.4;
  color: white;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card-large .news-card-title {
  font-size: 1.75rem;
  -webkit-line-clamp: 3;
  margin-bottom: 1.5rem;
}

.news-card-medium .news-card-title {
  font-size: 1.375rem;
}

/* Excerpt */
.news-card-excerpt {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card-large .news-card-excerpt {
  font-size: 1rem;
  -webkit-line-clamp: 4;
}

/* Footer */
.news-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.news-card-author-name {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8125rem;
  font-weight: 500;
}

.news-card-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.news-card-read-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8125rem;
  font-weight: 500;
}

.news-card-read-time svg {
  width: 14px;
  height: 14px;
}

/* Tags */
.news-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: auto;
}

.news-card-tag {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .news-card-content {
    padding: 1rem;
  }
  
  .news-card-large .news-card-content {
    padding: 1.5rem;
  }
  
  .news-card-title {
    font-size: 1.125rem;
  }
  
  .news-card-large .news-card-title {
    font-size: 1.5rem;
  }
  
  .news-card-medium .news-card-title {
    font-size: 1.25rem;
  }
  
  .news-card-large .news-card-image-container {
    height: 300px;
  }
  
  .news-card-medium .news-card-image-container {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .news-card-large .news-card-image-container {
    height: 250px;
  }
  
  .news-card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}
