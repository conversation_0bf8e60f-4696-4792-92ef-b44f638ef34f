"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import type { NewsArticle } from '../../news-grid';
import './styles/news-card.css';

export type NewsCardVariant = 'standard' | 'medium' | 'large';

interface NewsCardProps {
  article: NewsArticle;
  variant?: NewsCardVariant;
  featured?: boolean;
  className?: string;
}

export function NewsCard({ 
  article, 
  variant = 'standard', 
  featured = false,
  className = '' 
}: NewsCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const cardClasses = [
    'news-card',
    `news-card-${variant}`,
    featured ? 'news-card-featured' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <article className={cardClasses}>
      <Link href={`/news/${article.slug}`} className="news-card-link">
        <div className="news-card-image-container">
          <Image
            src={article.imageUrl}
            alt={article.title}
            fill
            className="news-card-image"
            sizes={
              variant === 'large' 
                ? '(max-width: 768px) 100vw, 66vw'
                : variant === 'medium'
                ? '(max-width: 768px) 100vw, 33vw'
                : '(max-width: 768px) 100vw, 350px'
            }
            priority={featured}
          />
          <div className="news-card-image-overlay" />
          {featured && (
            <div className="news-card-featured-badge">
              <svg viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              Featured
            </div>
          )}
        </div>

        <div className="news-card-content">
          <div className="news-card-meta">
            <span className="news-card-category">{article.category}</span>
            <span className="news-card-date">{formatDate(article.publishedAt)}</span>
          </div>

          <h3 className="news-card-title">{article.title}</h3>
          
          <p className="news-card-excerpt">{article.excerpt}</p>

          <div className="news-card-footer">
            <div className="news-card-author">
              <span className="news-card-author-name">By {article.author}</span>
            </div>

            <div className="news-card-stats">
              <span className="news-card-read-time">
                <svg viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                {article.readTime} min read
              </span>
            </div>
          </div>

          {article.tags.length > 0 && (
            <div className="news-card-tags">
              {article.tags.slice(0, 3).map((tag) => (
                <span key={tag} className="news-card-tag">
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </Link>
    </article>
  );
}

export default NewsCard;
