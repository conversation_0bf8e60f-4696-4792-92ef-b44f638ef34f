// FeaturedMatches.tsx (v1) - Featured Matches Display Component
"use client";

import React from "react";

const FeaturedMatches: React.FC = () => {
        return (
                <section>
                        <div className="flex items-center justify-between mb-8">
                                <div>
                                        <h2 className="text-3xl font-bold gradient-text mb-2">Featured Matches</h2>
                                        <p className="text-gray-400">Today's most anticipated games</p>
                                </div>
                                <button className="futuristic-button px-6 py-3 rounded-xl text-sm font-medium neon-glow-purple">
                                        View All Matches
                                </button>
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                                {/* Match Card 1 - Live Match */}
                                <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                        <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                        <span className="text-red-400 text-sm font-bold">LIVE</span>
                                                </div>
                                                <span className="text-gray-400 text-sm">45' + 2</span>
                                        </div>

                                        <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                                <span className="text-white font-bold text-xs">MC</span>
                                                        </div>
                                                        <div>
                                                                <p className="font-bold text-gray-200">Man City</p>
                                                                <p className="text-xs text-gray-400">Premier League</p>
                                                        </div>
                                                </div>
                                                <div className="text-center">
                                                        <p className="text-2xl font-bold gradient-text">2</p>
                                                </div>
                                        </div>

                                        <div className="flex items-center justify-between mb-6">
                                                <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                                                                <span className="text-white font-bold text-xs">LIV</span>
                                                        </div>
                                                        <div>
                                                                <p className="font-bold text-gray-200">Liverpool</p>
                                                                <p className="text-xs text-gray-400">Premier League</p>
                                                        </div>
                                                </div>
                                                <div className="text-center">
                                                        <p className="text-2xl font-bold gradient-text">1</p>
                                                </div>
                                        </div>

                                        <div className="flex gap-2">
                                                <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">Watch Live</button>
                                                <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">Match Stats</button>
                                        </div>
                                </div>

                                {/* Match Card 2 - Upcoming Match */}
                                <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                        <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center gap-2">
                                                        <span className="text-yellow-400 text-sm font-bold">18:00</span>
                                                </div>
                                                <span className="text-gray-400 text-sm">Today</span>
                                        </div>

                                        <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                                                <span className="text-white font-bold text-xs">PSG</span>
                                                        </div>
                                                        <div>
                                                                <p className="font-bold text-gray-200">PSG</p>
                                                                <p className="text-xs text-gray-400">Ligue 1</p>
                                                        </div>
                                                </div>
                                                <div className="text-center">
                                                        <p className="text-lg text-gray-400">vs</p>
                                                </div>
                                        </div>

                                        <div className="flex items-center justify-between mb-6">
                                                <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                                                <span className="text-white font-bold text-xs">OL</span>
                                                        </div>
                                                        <div>
                                                                <p className="font-bold text-gray-200">Lyon</p>
                                                                <p className="text-xs text-gray-400">Ligue 1</p>
                                                        </div>
                                                </div>
                                                <div className="text-center">
                                                        <p className="text-lg text-gray-400">vs</p>
                                                </div>
                                        </div>

                                        <div className="flex gap-2">
                                                <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">Set Reminder</button>
                                                <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">View Preview</button>
                                        </div>
                                </div>

                                {/* Match Card 3 - Finished Match */}
                                <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                        <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center gap-2">
                                                        <span className="text-green-400 text-sm font-bold">FT</span>
                                                </div>
                                                <span className="text-gray-400 text-sm">Earlier</span>
                                        </div>

                                        <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                                                                <span className="text-white font-bold text-xs">BVB</span>
                                                        </div>
                                                        <div>
                                                                <p className="font-bold text-gray-200">Dortmund</p>
                                                                <p className="text-xs text-gray-400">Bundesliga</p>
                                                        </div>
                                                </div>
                                                <div className="text-center">
                                                        <p className="text-2xl font-bold gradient-text">3</p>
                                                </div>
                                        </div>

                                        <div className="flex items-center justify-between mb-6">
                                                <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                                                                <span className="text-white font-bold text-xs">BAY</span>
                                                        </div>
                                                        <div>
                                                                <p className="font-bold text-gray-200">Bayern</p>
                                                                <p className="text-xs text-gray-400">Bundesliga</p>
                                                        </div>
                                                </div>
                                                <div className="text-center">
                                                        <p className="text-2xl font-bold gradient-text">1</p>
                                                </div>
                                        </div>

                                        <div className="flex gap-2">
                                                <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">Watch Highlights</button>
                                                <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">Full Report</button>
                                        </div>
                                </div>
                        </div>
                </section>
        );
};

export default FeaturedMatches;
