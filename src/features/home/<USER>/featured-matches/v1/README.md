# FeaturedMatches Component V1

## ⚽ Overview
FeaturedMatches V1 is a modular sports dashboard component that displays today's most anticipated football matches with interactive match cards.

## ✨ Features
- **3 Match Types**: Live, Upcoming, and Finished matches
- **Interactive Cards**: Hover effects with scale animation
- **Match Details**: Team logos, scores, league info, and match status
- **Action Buttons**: Context-sensitive buttons for each match type
- **Responsive Grid**: Adaptive layout (1/2/3 columns)
- **Modern Design**: Neomorphism cards with gradient team colors

## 🏆 Match Card Types

### 1. **Live Match** (Red indicator)
- Real-time score display
- Animated pulse indicator
- Match time (e.g., "45' + 2")
- Action buttons: "Watch Live" & "Match Stats"

### 2. **Upcoming Match** (Yellow time)
- Scheduled time display
- "vs" indicator between teams
- Match date info
- Action buttons: "Set Reminder" & "View Preview"

### 3. **Finished Match** (Green FT)
- Final scores display
- "FT" (Full Time) indicator
- Match completion status
- Action buttons: "Watch Highlights" & "Full Report"

## 🎨 Team Display
- **Team Logos**: Gradient colored badges with team abbreviations
- **Team Names**: Full team names with league information
- **Score Display**: Large gradient text for scores
- **League Info**: Small gray text showing competition

## 📱 Responsive Design
- **Mobile**: Single column layout
- **Large**: 2 columns
- **Extra Large**: 3 columns
- **Gap**: Consistent 6-unit spacing

## 🎯 Usage
```tsx
import FeaturedMatches from '@/shared/components/layout/body/featured-matches';

// In your component
<FeaturedMatches />
```

## 🎨 Styling Features
- **Neomorphism Cards**: Modern card design with depth
- **Gradient Text**: Eye-catching score displays
- **Hover Effects**: Scale transform on card hover
- **Team Colors**: Blue (Man City), Red (Liverpool), Purple (PSG), Green (Lyon), Yellow (Dortmund), Red (Bayern)
- **Status Indicators**: Color-coded match statuses

## 🔧 Match Data Structure
Each match card contains:
- Match status (LIVE/Time/FT)
- Team 1: Logo, Name, League
- Team 2: Logo, Name, League
- Score/Status indicator
- Context-appropriate action buttons

## 📊 Technical Specs
- **Lines of Code**: ~170
- **Dependencies**: React (no additional dependencies)
- **CSS Classes**: TailwindCSS with custom design system
- **Performance**: Static component, no state management

## 🎨 Design System
Uses consistent design tokens:
- `neomorphism-card`: Card styling
- `gradient-text`: Score text effects
- `futuristic-button`: Action buttons
- `neon-glow-purple`: Button glow effects
- Team-specific gradient backgrounds

## 🔧 Customization
The component can be easily extended with:
- Dynamic match data from API
- Additional match types
- Custom team logos
- Different layouts
- Real-time score updates

---
**Status**: ✅ Production Ready
**Version**: v1
**Match Types**: Live, Upcoming, Finished
**Last Updated**: June 5, 2025
