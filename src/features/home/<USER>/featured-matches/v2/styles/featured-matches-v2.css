/* Featured Matches v2 Styles */

/* Full-width Featured Matches - Similar to Hero v3 approach */
.featured-matches-fullwidth {
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
        width: 100vw;
        max-width: none;
        position: relative;
        overflow-x: hidden;
}

/* Professional Section Background - Seamless transition from Hero v3 */
.professional-section-bg {
        /* Primary gradient background that seamlessly continues from Hero v3's slate-800 end */
        background: linear-gradient(135deg,
                        /* Start with slate-800 to perfectly match Hero v3's ending gradient */
                        rgb(30, 41, 59) 0%,
                        /* slate-800 */
                        rgba(30, 41, 59, 0.95) 8%,
                        /* fade to transparent slate-800 */
                        rgba(15, 23, 42, 0.9) 20%,
                        /* slate-900 with transparency */
                        /* Transition through our main background color */
                        var(--background) 45%,
                        /* Add subtle depth with darker accent matching <PERSON>'s stadium tones */
                        rgba(17, 24, 39, 0.98) 75%,
                        /* gray-900 to match <PERSON>'s via-gray-900 */
                        /* End with solid background for content sections below */
                        var(--background) 100%);

        /* Additional overlay for depth and professional appearance */
        position: relative;
        overflow: hidden;

        /* UI/UX Expert calculated spacing for optimal Hero adjacency */
        padding: 3rem 0 4rem 0;
        /* Reduced from 4rem/6rem for better flow */
        margin-top: -1rem;
        /* Subtle overlap for seamless transition */
}

.professional-section-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        /* Blue stadium lighting overlays to harmonize with Hero v3 */
        background: radial-gradient(ellipse at 20% 30%,
                        rgba(37, 99, 235, 0.08) 0%,
                        transparent 50%),
                radial-gradient(ellipse at 80% 70%,
                        rgba(29, 78, 216, 0.06) 0%,
                        transparent 50%);
        pointer-events: none;
        z-index: 0;
}

.professional-section-bg>* {
        position: relative;
        z-index: 1;
}

/* Inner container for content within full-width section */
.featured-matches-inner-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
}

/* Responsive padding adjustments - Optimized for Hero adjacency */
@media (max-width: 768px) {
        .professional-section-bg {
                padding: 2rem 0 3rem 0;
                /* Reduced for mobile */
                margin-top: -0.75rem;
        }

        .featured-matches-inner-container {
                padding: 0 0.75rem;
        }
}

@media (max-width: 480px) {
        .professional-section-bg {
                padding: 1.5rem 0 2.5rem 0;
                /* Minimal spacing for small screens */
                margin-top: -0.5rem;
        }

        .featured-matches-inner-container {
                padding: 0 0.5rem;
        }
}

/* Gradient text for headings */
.gradient-text {
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
}

/* Neomorphic card design */
.neomorphism-card {
        background: linear-gradient(145deg, rgba(31, 41, 55, 0.7), rgba(17, 24, 39, 0.8));
        border-radius: 16px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.05);
        box-shadow:
                0 10px 15px -3px rgba(0, 0, 0, 0.2),
                0 4px 6px -2px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

/* Futuristic button styles */
.futuristic-button {
        background: linear-gradient(145deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0.8));
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.05);
        box-shadow:
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
}

.futuristic-button:hover {
        background: linear-gradient(145deg, rgba(75, 85, 99, 0.7), rgba(55, 65, 81, 0.8));
        transform: translateY(-1px);
        box-shadow:
                0 10px 15px -3px rgba(0, 0, 0, 0.2),
                0 4px 6px -2px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

.futuristic-button:active {
        transform: translateY(1px);
}

.futuristic-button-outline {
        background: transparent;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
}

.futuristic-button-outline:hover {
        background: rgba(91, 33, 182, 0.1);
        transform: translateY(-1px);
        box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
}

/* Purple glow effect */
.neon-glow-purple {
        box-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
        border: 1px solid rgba(168, 85, 247, 0.3);
}

.neon-glow-purple:hover {
        box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
        border: 1px solid rgba(168, 85, 247, 0.5);
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống 0.02s */
}

.featured-matches-v2 .grid>div:nth-child(3) {
        animation-delay: 0.03s;
        /* Giảm từ 0.06s xuống 0.03s */
}

.featured-matches-v2 .grid>div:nth-child(4) {
        animation-delay: 0.04s;
        /* Giảm từ 0.08s xuống 0.04s */
}

.featured-matches-v2 .grid>div:nth-child(5) {
        animation-delay: 0.05s;
        /* Giảm từ 0.1s xuống 0.05s */
}

.featured-matches-v2 .grid>div:nth-child(6) {
        animation-delay: 0.06s;
        /* Giảm từ 0.12s xuống 0.06s */
}

/* Filter bar hover effects */
.featured-matches-v2 .filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Enhance hover effects with instant feedback */
.sort-option:hover,
.filter-bar button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition-duration: 0.1s;
        /* Make hover effect feel instant */
}

/* Add active state feedback */
.sort-option:active,
.filter-bar button:active {
        transform: translateY(0);
        transition-duration: 0.05s;
        /* Make active state feel responsive */
}

/* Optimize card hover effect */
.featured-matches-v2 .grid>div:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition-duration: 0.15s;
        z-index: 1;
}

/* Add button loading state */
.btn-loading {
        position: relative;
        pointer-events: none;
}

.btn-loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: white;
        animation: spin 0.6s linear infinite;
}

@keyframes spin {
        to {
                transform: rotate(360deg);
        }
}

/* Animation for live indicator */
@keyframes pulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.5;
                transform: scale(1.1);
        }
}

.animate-pulse {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Live match card animation */
@keyframes liveCardPulse {

        0%,
        100% {
                box-shadow: 0 0 0 rgba(239, 68, 68, 0.1);
        }

        50% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
}

.live-match-card {
        animation: liveCardPulse 2s ease-in-out infinite;
        border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Favorite match card style */
.favorite-match-card {
        border: 1px solid rgba(250, 204, 21, 0.2);
        box-shadow: 0 0 15px rgba(250, 204, 21, 0.1);
}

/* Date group header style */
.date-group-header {
        font-size: 0.875rem;
        color: #d1d5db;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, rgba(55, 65, 81, 0.7), rgba(31, 41, 55, 0));
        border-left: 3px solid #818cf8;
        border-radius: 0.25rem;
}

/* League badge style */
.league-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: rgba(31, 41, 55, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sort and filter transitions */
.featured-matches-v2 {
        transition: opacity 0.3s ease;
}

.featured-matches-v2.loading {
        opacity: 0.7;
}

/* Sort options animations */
.sort-option {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.sort-option:hover {
        transform: translateY(-2px);
}

/* Active sort option effect */
.sort-option.active-sort {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
}

/* Add ripple effect for button clicks */
.sort-option::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: inherit;
        top: 0;
        left: 0;
        opacity: 0;
        transform: scale(0);
        transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.sort-option:active::after {
        transform: scale(2);
        opacity: 1;
        transition: 0s;
}

/* Removing the underline border for better UX */
/* 
.sort-option.active-sort::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: linear-gradient(90deg, #a855f7, #3b82f6);
        border-radius: 1px;
}
*/

/* Animation for sort change */
@keyframes sortChangeIndicator {
        0% {
                opacity: 0;
                transform: translateY(3px);
        }

        100% {
                opacity: 1;
                transform: translateY(0);
        }
}

.sort-info {
        animation: sortChangeIndicator 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        /* Giảm thời gian từ 0.3s xuống 0.2s */
}

/* Match card transition when sorting */
.featured-matches-v2 .grid {
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: opacity, transform;
}

.featured-matches-v2.loading .grid {
        opacity: 0.8;
        /* Tăng từ 0.8 lên 0.9 để hiệu ứng nhẹ nhàng hơn nữa */
        transform: scale(0.99);
        /* Giảm từ 0.99 lên 0.995 cho hiệu ứng tinh tế hơn */
}

/* Add enhanced transition for sort changes */
.featured-matches-v2 .grid.sort-transition-complete {
        animation: sortComplete 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sortComplete {
        0% {
                opacity: 0.95;
                transform: scale(0.995);
        }

        70% {
                opacity: 1;
                transform: scale(1.002);
        }

        100% {
                opacity: 1;
                transform: scale(1);
        }
}

/* Add staggered animation for match cards */
@keyframes cardFadeIn {
        from {
                opacity: 0.7;
                /* Tăng từ 0.5 lên 0.7 để chuyển động mượt hơn */
                transform: translateY(3px);
                /* Giảm từ 5px xuống 0px để ít chuyển động hơn */
        }

        to {
                opacity: 1;
                transform: translateY(0);
        }
}

.featured-matches-v2 .grid>div {
        animation: cardFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        /* Giảm từ 0.3s xuống 0.2s để cảm giác nhanh hơn */
        will-change: opacity, transform;
}

/* Create a staggered animation effect for cards - giảm thời gian delay */
.featured-matches-v2 .grid>div:nth-child(1) {
        animation-delay: 0.01s;
        /* Giảm từ 0.02s xuống 0.01s */
}

.featured-matches-v2 .grid>div:nth-child(2) {
        animation-delay: 0.02s;
        /* Giảm từ 0.04s xuống0.02s */
}