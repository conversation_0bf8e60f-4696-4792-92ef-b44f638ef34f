// FilterBar.tsx - Component for filtering matches
"use client";

import React from "react";
import { optimizeButtonForTouch } from '@/shared/utils/touchUtils';

export interface FilterBarProps {
        activeLeague: string;
        onLeagueChange: (league: string) => void;
        availableLeagues: string[];
        matchCount: number;
        activeSortBy?: string;
        onSortChange?: (sortBy: string) => void;
        onPreloadLeague?: (league: string) => void;
        onPreloadSort?: (sortOption: string) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({
        activeLeague,
        onLeagueChange,
        availableLeagues,
        matchCount,
        activeSortBy = "date",
        onSortChange,
        onPreloadLeague,
        onPreloadSort
}) => {
        return (
                <div className="bg-gray-800/50 rounded-xl p-4 mb-6 filter-bar relative">
                        {/* Leagues Filter Section */}
                        <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
                                <div className="flex flex-wrap items-center gap-3 overflow-x-auto pb-1 md:pb-0 scrollbar-hide">
                                        <button
                                                onClick={() => onLeagueChange("all")}
                                                onMouseEnter={() => onPreloadLeague && onPreloadLeague("all")}
                                                className={optimizeButtonForTouch(`px-4 py-2 rounded-lg text-sm transition-all whitespace-nowrap ${activeLeague === "all"
                                                        ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium"
                                                        : "hover:bg-gray-700/50 text-gray-300"
                                                        }`)}
                                        >
                                                All Leagues
                                        </button>

                                        {availableLeagues.map((league) => (
                                                <button
                                                        key={league}
                                                        onClick={() => onLeagueChange(league)}
                                                        onMouseEnter={() => onPreloadLeague && onPreloadLeague(league)}
                                                        className={optimizeButtonForTouch(`px-4 py-2 rounded-lg text-sm transition-all whitespace-nowrap ${activeLeague === league
                                                                ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium"
                                                                : "hover:bg-gray-700/50 text-gray-300"
                                                                }`)}
                                                >
                                                        {league}
                                                </button>
                                        ))}
                                </div>

                                <div className="text-gray-400 text-sm">
                                        <span className="bg-gray-700/50 rounded-full px-3 py-1">
                                                {matchCount} matches
                                        </span>
                                </div>
                        </div>

                        {/* Sort Options Section */}
                        {onSortChange && (
                                <div className="border-t border-gray-700/50 pt-4">
                                        <div className="flex flex-wrap items-center justify-between gap-2">
                                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
                                                        <span className="text-gray-400 text-sm whitespace-nowrap">Sort by:</span>
                                                        <div className="flex flex-wrap gap-2 overflow-x-auto scrollbar-hide pb-1 w-full sm:w-auto">
                                                                <button
                                                                        onClick={() => onSortChange("date")}
                                                                        onMouseEnter={() => onPreloadSort && onPreloadSort("date")}
                                                                        data-sort="date"
                                                                        className={`sort-option px-3 py-1 rounded-lg text-xs transition-all whitespace-nowrap ${activeSortBy === "date"
                                                                                ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium active-sort"
                                                                                : "bg-gray-700/50 hover:bg-gray-700 text-gray-300"
                                                                                }`}
                                                                >
                                                                        <span className="hidden sm:inline">📅 </span>Date
                                                                </button>
                                                                <button
                                                                        onClick={() => onSortChange("live")}
                                                                        onMouseEnter={() => onPreloadSort && onPreloadSort("live")}
                                                                        data-sort="live"
                                                                        className={`sort-option px-3 py-1 rounded-lg text-xs transition-all whitespace-nowrap ${activeSortBy === "live"
                                                                                ? "bg-gradient-to-r from-red-600 to-orange-600 text-white font-medium active-sort"
                                                                                : "bg-gray-700/50 hover:bg-gray-700 text-gray-300"
                                                                                }`}
                                                                >
                                                                        <span className="hidden sm:inline">🔴 </span>Live First
                                                                </button>
                                                                <button
                                                                        onClick={() => onSortChange("hot")}
                                                                        onMouseEnter={() => onPreloadSort && onPreloadSort("hot")}
                                                                        data-sort="hot"
                                                                        className={`sort-option px-3 py-1 rounded-lg text-xs transition-all whitespace-nowrap ${activeSortBy === "hot"
                                                                                ? "bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium active-sort"
                                                                                : "bg-gray-700/50 hover:bg-gray-700 text-gray-300"
                                                                                }`}
                                                                >
                                                                        <span className="hidden sm:inline">🔥 </span>Hot Matches
                                                                </button>
                                                                <button
                                                                        onClick={() => onSortChange("league")}
                                                                        onMouseEnter={() => onPreloadSort && onPreloadSort("league")}
                                                                        data-sort="league"
                                                                        className={`sort-option px-3 py-1 rounded-lg text-xs transition-all whitespace-nowrap ${activeSortBy === "league"
                                                                                ? "bg-gradient-to-r from-green-600 to-teal-600 text-white font-medium active-sort"
                                                                                : "bg-gray-700/50 hover:bg-gray-700 text-gray-300"
                                                                                }`}
                                                                >
                                                                        <span className="hidden sm:inline">🏆 </span>League
                                                                </button>
                                                                <button
                                                                        onClick={() => onSortChange("score")}
                                                                        onMouseEnter={() => onPreloadSort && onPreloadSort("score")}
                                                                        data-sort="score"
                                                                        className={`sort-option px-3 py-1 rounded-lg text-xs transition-all whitespace-nowrap ${activeSortBy === "score"
                                                                                ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium active-sort"
                                                                                : "bg-gray-700/50 hover:bg-gray-700 text-gray-300"
                                                                                }`}
                                                                >
                                                                        <span className="hidden sm:inline">⚽ </span>Highest Score
                                                                </button>
                                                                <button
                                                                        onClick={() => onSortChange("team")}
                                                                        onMouseEnter={() => onPreloadSort && onPreloadSort("team")}
                                                                        data-sort="team"
                                                                        className={`sort-option px-3 py-1 rounded-lg text-xs transition-all whitespace-nowrap ${activeSortBy === "team"
                                                                                ? "bg-gradient-to-r from-cyan-600 to-blue-600 text-white font-medium active-sort"
                                                                                : "bg-gray-700/50 hover:bg-gray-700 text-gray-300"
                                                                                }`}
                                                                >
                                                                        <span className="hidden sm:inline">👕 </span>Team Name
                                                                </button>
                                                        </div>
                                                </div>
                                                <div className="sort-info text-xs text-gray-400 bg-gray-800/50 px-3 py-1 rounded-lg mt-2 sm:mt-0 w-full sm:w-auto text-center sm:text-left">
                                                        {activeSortBy === "date" && "Showing newest matches first"}
                                                        {activeSortBy === "live" && "Prioritizing live matches"}
                                                        {activeSortBy === "hot" && "Showing hot matches first"}
                                                        {activeSortBy === "league" && "Sorted alphabetically by league"}
                                                        {activeSortBy === "score" && "Matches with highest total goals first"}
                                                        {activeSortBy === "team" && "Sorted alphabetically by team name"}
                                                </div>
                                        </div>
                                </div>
                        )}
                </div>
        );
};

export default FilterBar;
