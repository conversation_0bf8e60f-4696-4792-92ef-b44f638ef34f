// MatchCard.tsx - Individual match card component
"use client";

import React from "react";
import Image from "next/image";
import { buildImageUrl, handleImageError } from "@/shared/utils/imageUtils";

// For debugging - Log CDN domain
console.log('CDN Domain:', process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || "Not set");

export interface MatchCardProps {
        match: {
                id: number;
                externalId: number;
                leagueId: number;
                leagueName: string;
                isHot: boolean;
                season: number;
                round: string;
                homeTeamId: number;
                homeTeamName: string;
                homeTeamLogo: string;
                awayTeamId: number;
                awayTeamName: string;
                awayTeamLogo: string;
                slug: string;
                date: string;
                venue: {
                        id: number;
                        name: string;
                        city: string;
                };
                referee: string;
                status: string;
                statusLong: string;
                statusExtra: number;
                elapsed: number;
                goalsHome: number;
                goalsAway: number;
                scoreHalftimeHome: number;
                scoreHalftimeAway: number;
                scoreFulltimeHome: number;
                scoreFulltimeAway: number;
                periods: {
                        first: number;
                        second: number;
                };
                timestamp: string;
                thumb: string | null;
        };
        onViewDetails?: (matchId: number) => void;
}

const MatchCard: React.FC<MatchCardProps> = ({ match, onViewDetails }) => {
        const isLive = match.status === "LIVE" || match.status === "1H" || match.status === "2H" || match.status === "HT";
        const isFinished = match.status === "FT" || match.status === "AET" || match.status === "PEN";
        const isUpcoming = !isLive && !isFinished;

        // Format date to display
        const matchDate = new Date(match.date);
        const formattedDate = isUpcoming
                ? matchDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })
                : isFinished
                        ? "FT"
                        : `${match.elapsed}'`;

        const dateBadgeColor = isLive
                ? "text-red-400"
                : isFinished
                        ? "text-green-400"
                        : "text-yellow-400";

        // Format match day relative to today (today, tomorrow, etc.)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const matchDay = new Date(matchDate);
        matchDay.setHours(0, 0, 0, 0);

        const dayDiff = Math.floor((matchDay.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        let matchDayText;
        if (dayDiff === 0) {
                matchDayText = "Today";
        } else if (dayDiff === 1) {
                matchDayText = "Tomorrow";
        } else if (dayDiff === -1) {
                matchDayText = "Yesterday";
        } else {
                matchDayText = matchDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }

        const handleViewDetails = () => {
                if (onViewDetails) {
                        onViewDetails(match.id);
                }
        };

        // Generate Schema.org SportsEvent structured data
        const structuredData = {
                "@context": "https://schema.org",
                "@type": "SportsEvent",
                "name": `${match.homeTeamName} vs ${match.awayTeamName}`,
                "description": `${match.leagueName} match between ${match.homeTeamName} and ${match.awayTeamName}`,
                "startDate": match.date,
                "location": {
                        "@type": "Place",
                        "name": match.venue.name,
                        "address": {
                                "@type": "PostalAddress",
                                "addressLocality": match.venue.city
                        }
                },
                "homeTeam": {
                        "@type": "SportsTeam",
                        "name": match.homeTeamName
                },
                "awayTeam": {
                        "@type": "SportsTeam",
                        "name": match.awayTeamName
                },
                "sport": "Football"
        };

        return (
                <div
                        className={`neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 relative overflow-hidden ${isLive ? 'live-match-card' : ''}`}
                        aria-label={`Match: ${match.homeTeamName} vs ${match.awayTeamName}, ${match.leagueName}, ${isLive ? 'Live' : isFinished ? 'Finished' : matchDayText}`}
                >
                        {/* Schema.org structured data */}
                        <script
                                type="application/ld+json"
                                dangerouslySetInnerHTML={{
                                        __html: JSON.stringify(structuredData)
                                }}
                        />

                        {/* Hot badge */}
                        {match.isHot && (
                                <div className="absolute top-0 right-0" aria-label="Hot match">
                                        <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2 py-1 transform rotate-45 translate-x-2 -translate-y-1">
                                                HOT
                                        </div>
                                </div>
                        )}

                        {/* Match header */}
                        <header className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-2">
                                        {isLive && <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" aria-label="Live indicator"></div>}
                                        <span className={`${dateBadgeColor} text-sm font-bold`} role="status">
                                                {isLive ? "LIVE" : formattedDate}
                                        </span>
                                </div>
                                <time
                                        className="text-gray-400 text-sm"
                                        dateTime={match.date}
                                        aria-label={`Match time: ${isLive ? `${match.elapsed} minutes elapsed` : matchDayText}`}
                                >
                                        {isLive ? `${match.elapsed}'` : matchDayText}
                                </time>
                        </header>

                        {/* League info */}
                        <div className="flex items-center mb-4">
                                <div className="league-badge" role="text" aria-label={`League: ${match.leagueName}, Round: ${match.round}`}>
                                        <span className="mr-1">🏆</span> {match.leagueName} • {match.round}
                                </div>
                        </div>

                        {/* Home team */}
                        <div className="flex items-center justify-between mb-4" role="group" aria-label="Home team">
                                <div className="flex items-center gap-3">
                                        <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center overflow-hidden">
                                                <Image
                                                        src={buildImageUrl(match.homeTeamLogo)}
                                                        alt={match.homeTeamName}
                                                        width={32}
                                                        height={32}
                                                        className="object-contain"
                                                        onError={(e) => handleImageError(e, match.homeTeamName)}
                                                        unoptimized={true} // Skip Next.js image optimization
                                                />
                                        </div>
                                        <div>
                                                <p className="font-bold text-gray-200">{match.homeTeamName}</p>
                                                <p className="text-xs text-gray-400">Home</p>
                                        </div>
                                </div>
                                <div className="text-center">
                                        <p className="text-2xl font-bold gradient-text" aria-label={`Home team score: ${match.goalsHome}`}>{match.goalsHome}</p>
                                </div>
                        </div>

                        {/* Away team */}
                        <div className="flex items-center justify-between mb-6" role="group" aria-label="Away team">
                                <div className="flex items-center gap-3">
                                        <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center overflow-hidden">
                                                <Image
                                                        src={buildImageUrl(match.awayTeamLogo)}
                                                        alt={match.awayTeamName}
                                                        width={32}
                                                        height={32}
                                                        className="object-contain"
                                                        onError={(e) => handleImageError(e, match.awayTeamName)}
                                                        unoptimized={true} // Skip Next.js image optimization
                                                />
                                        </div>
                                        <div>
                                                <p className="font-bold text-gray-200">{match.awayTeamName}</p>
                                                <p className="text-xs text-gray-400">Away</p>
                                        </div>
                                </div>
                                <div className="text-center">
                                        <p className="text-2xl font-bold gradient-text" aria-label={`Away team score: ${match.goalsAway}`}>{match.goalsAway}</p>
                                </div>
                        </div>

                        {/* Stadium info */}
                        <address className="mb-4 text-xs text-gray-400 flex items-center gap-1 not-italic">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {match.venue.name}, {match.venue.city}
                        </address>

                        {/* Action buttons */}
                        <footer className="flex gap-2">
                                {isLive && (
                                        <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700">
                                                Watch Live
                                        </button>
                                )}
                                {isFinished && (
                                        <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">
                                                Watch Highlights
                                        </button>
                                )}
                                {isUpcoming && (
                                        <button className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium">
                                                Set Reminder
                                        </button>
                                )}
                                <button
                                        onClick={handleViewDetails}
                                        className="flex-1 futuristic-button py-2 rounded-lg text-xs font-medium"
                                >
                                        Match Details
                                </button>
                        </footer>
                </div>
        );
};

export default MatchCard;
