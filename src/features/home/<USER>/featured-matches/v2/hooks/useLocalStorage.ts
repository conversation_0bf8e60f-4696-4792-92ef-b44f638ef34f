// useLocalStorage.ts - Hook for persisting data in localStorage
"use client";

import { useState, useEffect } from 'react';

function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] {
        // Get from local storage then
        // parse stored json or return initialValue
        const readValue = (): T => {
                // Prevent build error "window is undefined" but keep working
                if (typeof window === 'undefined') {
                        return initialValue;
                }

                try {
                        const item = window.localStorage.getItem(key);
                        return item ? (JSON.parse(item) as T) : initialValue;
                } catch (error) {
                        console.warn(`Error reading localStorage key "${key}":`, error);
                        return initialValue;
                }
        };

        // State to store our value
        // Pass initial state function to useState so logic is only executed once
        const [storedValue, setStoredValue] = useState<T>(readValue);

        // Return a wrapped version of useState's setter function that
        // persists the new value to localStorage.
        const setValue = (value: T | ((val: T) => T)) => {
                try {
                        // Allow value to be a function so we have same API as useState
                        const valueToStore =
                                value instanceof Function ? value(storedValue) : value;

                        // Save state
                        setStoredValue(valueToStore);

                        // Save to local storage
                        if (typeof window !== 'undefined') {
                                window.localStorage.setItem(key, JSON.stringify(valueToStore));
                        }
                } catch (error) {
                        console.warn(`Error setting localStorage key "${key}":`, error);
                }
        };

        useEffect(() => {
                setStoredValue(readValue());
                // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        return [storedValue, setValue];
}

export default useLocalStorage;