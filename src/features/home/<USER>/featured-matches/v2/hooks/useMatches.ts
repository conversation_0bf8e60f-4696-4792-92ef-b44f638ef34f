// useMatches - Custom hook for fetching and managing match data
"use client";

import { useState, useEffect, useCallback } from 'react';
import matchesService, { Match, MatchesResponse } from '../services/matchesService';

interface UseMatchesOptions {
        initialLimit?: number;
        initialPage?: number;
        initialLeagueId?: number | null;
        initialIsLive?: boolean;
}

interface UseMatchesReturn {
        matches: Match[];
        isLoading: boolean;
        error: Error | null;
        totalMatches: number;
        hasMore: boolean;
        leagueId: number | null;
        isLive: boolean;
        page: number;
        limit: number;
        setLeagueId: (leagueId: number | null) => void;
        setIsLive: (isLive: boolean) => void;
        loadMore: () => Promise<void>;
        refreshMatches: () => Promise<void>;
}

/**
 * Custom hook for managing matches data
 */
const useMatches = (options: UseMatchesOptions = {}): UseMatchesReturn => {
        const {
                initialLimit = 6,
                initialPage = 1,
                initialLeagueId = null,
                initialIsLive = false,
        } = options;

        const [matches, setMatches] = useState<Match[]>([]);
        const [isLoading, setIsLoading] = useState<boolean>(true);
        const [error, setError] = useState<Error | null>(null);
        const [totalMatches, setTotalMatches] = useState<number>(0);
        const [hasMore, setHasMore] = useState<boolean>(false);
        const [leagueId, setLeagueId] = useState<number | null>(initialLeagueId);
        const [isLive, setIsLive] = useState<boolean>(initialIsLive);
        const [page, setPage] = useState<number>(initialPage);
        const [limit] = useState<number>(initialLimit);

        // Function to fetch matches based on current state
        const fetchMatches = useCallback(async (currentPage: number, append = false) => {
                setIsLoading(true);
                setError(null);

                try {
                        let response: MatchesResponse;

                        if (isLive) {
                                response = await matchesService.getLiveMatches(limit, currentPage);
                        } else if (leagueId) {
                                response = await matchesService.getMatchesByLeague(leagueId, limit, currentPage);
                        } else {
                                response = await matchesService.getFeaturedMatches(limit, currentPage);
                        }

                        setTotalMatches(response.total);
                        setHasMore(response.hasMore);

                        if (append) {
                                setMatches(prev => [...prev, ...response.matches]);
                        } else {
                                setMatches(response.matches);
                        }
                } catch (err) {
                        setError(err instanceof Error ? err : new Error('Failed to fetch matches'));
                        // For demo, fallback to sample data
                        if (process.env.NODE_ENV === 'development') {
                                console.warn('Using sample data as fallback in development');
                                // Sample data would go here
                        }
                } finally {
                        setIsLoading(false);
                }
        }, [limit, leagueId, isLive]);

        // Initial fetch and when dependencies change
        useEffect(() => {
                setPage(initialPage);
                fetchMatches(initialPage, false);
        }, [fetchMatches, leagueId, isLive, initialPage]);

        // Function to load more matches
        const loadMore = useCallback(async () => {
                if (!hasMore || isLoading) return;
                const nextPage = page + 1;
                setPage(nextPage);
                await fetchMatches(nextPage, true);
        }, [fetchMatches, hasMore, isLoading, page]);

        // Function to refresh matches
        const refreshMatches = useCallback(async () => {
                setPage(initialPage);
                await fetchMatches(initialPage, false);
        }, [fetchMatches, initialPage]);

        return {
                matches,
                isLoading,
                error,
                totalMatches,
                hasMore,
                leagueId,
                isLive,
                page,
                limit,
                setLeagueId,
                setIsLive,
                loadMore,
                refreshMatches,
        };
};

export default useMatches;
