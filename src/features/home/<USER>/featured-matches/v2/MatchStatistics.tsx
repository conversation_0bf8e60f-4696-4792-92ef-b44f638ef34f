// MatchStatistics.tsx - Component to display match statistics
"use client";

import React from 'react';

export interface MatchStatisticsProps {
        homeTeam: {
                shots: number;
                possession: number;
                corners: number;
        };
        awayTeam: {
                shots: number;
                possession: number;
                corners: number;
        };
        compact?: boolean;
}

const MatchStatistics: React.FC<MatchStatisticsProps> = ({
        homeTeam,
        awayTeam,
        compact = false
}) => {
        return (
                <div className={`${compact ? 'mt-2' : 'mt-4'} mb-4 bg-gray-800/40 p-2 rounded-lg grid grid-cols-3 text-xs text-center`}>
                        <div className="border-r border-gray-700/50">
                                <div className="text-gray-400 mb-1">Shots</div>
                                <div className="font-medium text-white">
                                        {homeTeam.shots} - {awayTeam.shots}
                                </div>
                        </div>
                        <div className="border-r border-gray-700/50">
                                <div className="text-gray-400 mb-1">Possession</div>
                                <div className="font-medium text-white">
                                        {homeTeam.possession}% - {awayTeam.possession}%
                                </div>
                        </div>
                        <div>
                                <div className="text-gray-400 mb-1">Corners</div>
                                <div className="font-medium text-white">
                                        {homeTeam.corners} - {awayTeam.corners}
                                </div>
                        </div>
                </div>
        );
};

export default MatchStatistics;