# Featured Matches v2

Enhanced version of the Featured Matches component with better UI/UX and modular architecture.

## Features

- Modular component architecture for better maintainability
- Responsive layout with grid system for different screen sizes
- Filter functionality to show matches by league
- Support for match statuses (live, upcoming, finished)
- Enhanced visual design with better use of space
- Proper error handling for team logos
- Live match animations with visual indicators
- "Hot" match highlighting for important fixtures
- Relative date formatting (Today, Tomorrow, etc.)
- Match statistics display for live and finished matches

## Components

- **FeaturedMatches**: Main component that orchestrates the entire feature
- **MatchCard**: Individual card component for displaying match information
- **FilterBar**: Filter bar for sorting and filtering matches

## Usage

```tsx
import FeaturedMatches from 'path/to/featured-matches';

// In your component
const YourComponent = () => {
  return (
    <div>
      <FeaturedMatches />
    </div>
  );
};
```

## Validation

The component has been manually validated to ensure:
- Proper rendering on various screen sizes
- Correct filtering functionality
- Appropriate display of match states (live, upcoming, finished)
- Error handling for image loading

## Data Structure

The component expects match data in the following format:

```typescript
{
  id: number;
  externalId: number;
  leagueId: number;
  leagueName: string;
  isHot: boolean;
  season: number;
  round: string;
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  slug: string;
  date: string;
  venue: {
    id: number;
    name: string;
    city: string;
  };
  referee: string;
  status: string;
  statusLong: string;
  statusExtra: number;
  elapsed: number;
  goalsHome: number;
  goalsAway: number;
  scoreHalftimeHome: number;
  scoreHalftimeAway: number;
  scoreFulltimeHome: number;
  scoreFulltimeAway: number;
  periods: {
    first: number;
    second: number;
  };
  timestamp: string;
  thumb: string | null;
}
```

## API Integration

The component can be integrated with a real API using the included service and hooks:

### Using the API Service

```typescript
import matchesService from './services/matchesService';

// Fetch featured matches
const featuredMatches = await matchesService.getFeaturedMatches();

// Fetch matches by league
const premierLeagueMatches = await matchesService.getMatchesByLeague(39); // Premier League ID

// Fetch live matches
const liveMatches = await matchesService.getLiveMatches();
```

### Using the Custom Hook

```typescript
import useMatches from './hooks/useMatches';

const YourComponent = () => {
  const {
    matches,
    isLoading,
    error,
    hasMore,
    loadMore,
    setLeagueId
  } = useMatches();

  // Filter by league
  const handleLeagueChange = (leagueId: number | null) => {
    setLeagueId(leagueId);
  };

  return (
    <div>
      {isLoading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorMessage error={error} />
      ) : (
        <>
          {/* Render matches */}
          {hasMore && (
            <button onClick={loadMore}>Load More</button>
          )}
        </>
      )}
    </div>
  );
};
```
