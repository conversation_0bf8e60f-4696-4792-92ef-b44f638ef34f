// favoritesService.ts - Service for managing favorite matches
"use client";

import useLocalStorage from '../hooks/useLocalStorage';

/**
 * Favorites Service - Manages user favorite matches with localStorage persistence
 */
export const useFavoritesService = () => {
        const [favorites, setFavorites] = useLocalStorage<number[]>('featured-matches-favorites', []);

        /**
         * Add a match to favorites
         * @param matchId ID of the match to add to favorites
         */
        const addFavorite = (matchId: number) => {
                if (!favorites.includes(matchId)) {
                        setFavorites([...favorites, matchId]);
                }
        };

        /**
         * Remove a match from favorites
         * @param matchId ID of the match to remove from favorites
         */
        const removeFavorite = (matchId: number) => {
                setFavorites(favorites.filter(id => id !== matchId));
        };

        /**
         * Toggle favorite status of a match
         * @param matchId ID of the match to toggle
         * @returns boolean indicating if the match is now a favorite
         */
        const toggleFavorite = (matchId: number): boolean => {
                if (favorites.includes(matchId)) {
                        removeFavorite(matchId);
                        return false;
                } else {
                        addFavorite(matchId);
                        return true;
                }
        };

        /**
         * Check if a match is in favorites
         * @param matchId ID of the match to check
         * @returns boolean indicating if the match is a favorite
         */
        const isFavorite = (matchId: number): boolean => {
                return favorites.includes(matchId);
        };

        return {
                favorites,
                addFavorite,
                removeFavorite,
                toggleFavorite,
                isFavorite
        };
};

export default useFavoritesService;