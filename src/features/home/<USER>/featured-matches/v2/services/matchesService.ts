// MatchesService - Service for fetching match data

export interface Venue {
        id: number;
        name: string;
        city: string;
}

export interface MatchPeriods {
        first: number;
        second: number;
}

export interface Match {
        id: number;
        externalId: number;
        leagueId: number;
        leagueName: string;
        isHot: boolean;
        season: number;
        round: string;
        homeTeamId: number;
        homeTeamName: string;
        homeTeamLogo: string;
        awayTeamId: number;
        awayTeamName: string;
        awayTeamLogo: string;
        slug: string;
        date: string;
        venue: Venue;
        referee: string;
        status: string;
        statusLong: string;
        statusExtra: number;
        elapsed: number;
        goalsHome: number;
        goalsAway: number;
        scoreHalftimeHome: number;
        scoreHalftimeAway: number;
        scoreFulltimeHome: number;
        scoreFulltimeAway: number;
        periods: MatchPeriods;
        timestamp: string;
        thumb: string | null;
}

export interface MatchesResponse {
        matches: Match[];
        total: number;
        hasMore: boolean;
}

class MatchesService {
        private apiBaseUrl: string;

        constructor() {
                this.apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.example.com';
        }

        /**
         * Fetch featured matches
         * @param limit Number of matches to fetch
         * @param page Page number for pagination
         * @param leagueId Optional league ID to filter by
         * @returns Promise with matches data
         */
        async getFeaturedMatches(limit = 6, page = 1, leagueId?: number): Promise<MatchesResponse> {
                try {
                        let url = `${this.apiBaseUrl}/matches/featured?limit=${limit}&page=${page}`;

                        if (leagueId) {
                                url += `&leagueId=${leagueId}`;
                        }

                        const response = await fetch(url);

                        if (!response.ok) {
                                throw new Error(`API error: ${response.status}`);
                        }

                        return await response.json();
                } catch (error) {
                        console.error('Error fetching featured matches:', error);
                        throw error;
                }
        }

        /**
         * Fetch live matches
         * @param limit Number of matches to fetch
         * @param page Page number for pagination
         * @returns Promise with live matches data
         */
        async getLiveMatches(limit = 6, page = 1): Promise<MatchesResponse> {
                try {
                        const url = `${this.apiBaseUrl}/matches/live?limit=${limit}&page=${page}`;
                        const response = await fetch(url);

                        if (!response.ok) {
                                throw new Error(`API error: ${response.status}`);
                        }

                        return await response.json();
                } catch (error) {
                        console.error('Error fetching live matches:', error);
                        throw error;
                }
        }

        /**
         * Fetch matches by league
         * @param leagueId League ID to filter by
         * @param limit Number of matches to fetch
         * @param page Page number for pagination
         * @returns Promise with matches data for the specified league
         */
        async getMatchesByLeague(leagueId: number, limit = 6, page = 1): Promise<MatchesResponse> {
                try {
                        const url = `${this.apiBaseUrl}/leagues/${leagueId}/matches?limit=${limit}&page=${page}`;
                        const response = await fetch(url);

                        if (!response.ok) {
                                throw new Error(`API error: ${response.status}`);
                        }

                        return await response.json();
                } catch (error) {
                        console.error(`Error fetching matches for league ${leagueId}:`, error);
                        throw error;
                }
        }

        /**
         * Fetch match details by ID
         * @param matchId Match ID
         * @returns Promise with match details
         */
        async getMatchDetails(matchId: number): Promise<Match> {
                try {
                        const url = `${this.apiBaseUrl}/matches/${matchId}`;
                        const response = await fetch(url);

                        if (!response.ok) {
                                throw new Error(`API error: ${response.status}`);
                        }

                        return await response.json();
                } catch (error) {
                        console.error(`Error fetching match details for match ${matchId}:`, error);
                        throw error;
                }
        }
}

// Export as singleton
export const matchesService = new MatchesService();
export default matchesService;
