// FeaturedMatches.tsx (v2) - Enhanced Featured Matches Display Component
"use client";

import React, { useState, useEffect, useRef } from "react";
import MatchCard from "./MatchCard";
import FilterBar from "./FilterBar";
import useMatches from "./hooks/useMatches";
import "./styles/featured-matches-v2.css";

// Placeholder data - in a real application this would come from an API
const DEMO_MATCHES = [
        {
                id: 1,
                externalId: 1354647,
                leagueId: 243,
                leagueName: "Liga Pro Serie B",
                isHot: true,
                season: 2025,
                round: "Regular Season - 12",
                homeTeamId: 1983,
                homeTeamName: "Imbabura",
                homeTeamLogo: "public/images/teams/1983.png",
                awayTeamId: 10112,
                awayTeamName: "Chacaritas",
                awayTeamLogo: "public/images/teams/10112.png",
                slug: "imbabura-vs-chacaritas-2025-06-04",
                date: "2025-06-04T00:00:00.000Z",
                venue: {
                        id: 2683,
                        name: "Estadio Olímpico de Ibarra",
                        city: "Ibarra"
                },
                referee: "",
                status: "FT",
                statusLong: "Match Finished",
                statusExtra: 4,
                elapsed: 90,
                goalsHome: 4,
                goalsAway: 1,
                scoreHalftimeHome: 1,
                scoreHalftimeAway: 1,
                scoreFulltimeHome: 4,
                scoreFulltimeAway: 1,
                periods: {
                        first: 1748995200,
                        second: 1748998800
                },
                timestamp: "1748995200",
                thumb: null
        },
        {
                id: 2,
                externalId: 1354648,
                leagueId: 140,
                leagueName: "La Liga",
                isHot: false,
                season: 2025,
                round: "Regular Season - 32",
                homeTeamId: 541,
                homeTeamName: "Real Madrid",
                homeTeamLogo: "public/images/teams/541.png",
                awayTeamId: 529,
                awayTeamName: "Barcelona",
                awayTeamLogo: "public/images/teams/529.png",
                slug: "real-madrid-vs-barcelona-2025-06-06",
                date: "2025-06-06T19:00:00.000Z",
                venue: {
                        id: 1456,
                        name: "Santiago Bernabéu",
                        city: "Madrid"
                },
                referee: "Mateu Lahoz",
                status: "NS",
                statusLong: "Not Started",
                statusExtra: 0,
                elapsed: 0,
                goalsHome: 0,
                goalsAway: 0,
                scoreHalftimeHome: 0,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 0,
                scoreFulltimeAway: 0,
                periods: {
                        first: 0,
                        second: 0
                },
                timestamp: "1749409200",
                thumb: null
        },
        {
                id: 3,
                externalId: 1354649,
                leagueId: 39,
                leagueName: "Premier League",
                isHot: true,
                season: 2025,
                round: "Regular Season - 36",
                homeTeamId: 33,
                homeTeamName: "Manchester United",
                homeTeamLogo: "public/images/teams/33.png",
                awayTeamId: 40,
                awayTeamName: "Liverpool",
                awayTeamLogo: "public/images/teams/40.png",
                slug: "manchester-united-vs-liverpool-2025-06-06",
                date: "2025-06-06T14:00:00.000Z",
                venue: {
                        id: 556,
                        name: "Old Trafford",
                        city: "Manchester"
                },
                referee: "Michael Oliver",
                status: "LIVE",
                statusLong: "Live",
                statusExtra: 2,
                elapsed: 37,
                goalsHome: 1,
                goalsAway: 2,
                scoreHalftimeHome: 0,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 0,
                scoreFulltimeAway: 0,
                periods: {
                        first: 1749409200,
                        second: 0
                },
                timestamp: "1749409200",
                thumb: null
        },
        {
                id: 4,
                externalId: 1354650,
                leagueId: 135,
                leagueName: "Serie A",
                isHot: false,
                season: 2025,
                round: "Regular Season - 36",
                homeTeamId: 505,
                homeTeamName: "Inter",
                homeTeamLogo: "public/images/teams/505.png",
                awayTeamId: 496,
                awayTeamName: "Juventus",
                awayTeamLogo: "public/images/teams/496.png",
                slug: "inter-vs-juventus-2025-06-05",
                date: "2025-06-05T18:45:00.000Z",
                venue: {
                        id: 907,
                        name: "Stadio Giuseppe Meazza",
                        city: "Milano"
                },
                referee: "Paolo Valeri",
                status: "FT",
                statusLong: "Match Finished",
                statusExtra: 4,
                elapsed: 90,
                goalsHome: 2,
                goalsAway: 2,
                scoreHalftimeHome: 1,
                scoreHalftimeAway: 1,
                scoreFulltimeHome: 2,
                scoreFulltimeAway: 2,
                periods: {
                        first: 1749322800,
                        second: 1749326400
                },
                timestamp: "1749322800",
                thumb: null
        },
        {
                id: 5,
                externalId: 1354651,
                leagueId: 61,
                leagueName: "Ligue 1",
                isHot: false,
                season: 2025,
                round: "Regular Season - 36",
                homeTeamId: 85,
                homeTeamName: "Paris Saint Germain",
                homeTeamLogo: "public/images/teams/85.png",
                awayTeamId: 91,
                awayTeamName: "Monaco",
                awayTeamLogo: "public/images/teams/91.png",
                slug: "psg-vs-monaco-2025-06-07",
                date: "2025-06-07T19:00:00.000Z",
                venue: {
                        id: 671,
                        name: "Parc des Princes",
                        city: "Paris"
                },
                referee: "",
                status: "NS",
                statusLong: "Not Started",
                statusExtra: 0,
                elapsed: 0,
                goalsHome: 0,
                goalsAway: 0,
                scoreHalftimeHome: 0,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 0,
                scoreFulltimeAway: 0,
                periods: {
                        first: 0,
                        second: 0
                },
                timestamp: "1749582000",
                thumb: null
        },
        {
                id: 6,
                externalId: 1354652,
                leagueId: 78,
                leagueName: "Bundesliga",
                isHot: false,
                season: 2025,
                round: "Regular Season - 34",
                homeTeamId: 157,
                homeTeamName: "Bayern Munich",
                homeTeamLogo: "public/images/teams/157.png",
                awayTeamId: 165,
                awayTeamName: "Borussia Dortmund",
                awayTeamLogo: "public/images/teams/165.png",
                slug: "bayern-munich-vs-borussia-dortmund-2025-06-07",
                date: "2025-06-07T16:30:00.000Z",
                venue: {
                        id: 700,
                        name: "Allianz Arena",
                        city: "München"
                },
                referee: "",
                status: "NS",
                statusLong: "Not Started",
                statusExtra: 0,
                elapsed: 0,
                goalsHome: 0,
                goalsAway: 0,
                scoreHalftimeHome: 0,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 0,
                scoreFulltimeAway: 0,
                periods: {
                        first: 0,
                        second: 0
                },
                timestamp: "1749573000",
                thumb: null
        }
];

const FeaturedMatches: React.FC = () => {
        const sectionRef = useRef<HTMLElement>(null); // Thêm ref để lưu vị trí cuộn

        // Using the custom hook would normally fetch data from API
        // For demo purposes, we'll use local data but implement the hook structure
        const [activeLeague, setActiveLeague] = useState<string>("all");
        const [filteredMatches, setFilteredMatches] = useState(DEMO_MATCHES);
        const [isLoading, setIsLoading] = useState(false);
        const [error, setError] = useState<string | null>(null);
        const [sortBy, setSortBy] = useState<string>("date");

        // In a real application, we would use the custom hook like this:
        // const {
        //   matches,
        //   isLoading,
        //   error,
        //   hasMore,
        //   loadMore,
        //   setLeagueId
        // } = useMatches();

        // Extract unique league names
        const availableLeagues = Array.from(
                new Set(DEMO_MATCHES.map((match) => match.leagueName))
        );

        // Restore scroll position when component mounts
        useEffect(() => {
                if (sectionRef.current) {
                        const savedScrollPos = sessionStorage.getItem('featuredMatchesScrollPos');
                        if (savedScrollPos) {
                                sectionRef.current.scrollTop = parseInt(savedScrollPos);
                        }
                }
        }, []);

        // Filter and sort matches when dependencies change
        useEffect(() => {
                // Simulate API loading
                setIsLoading(true);

                // Giảm thời gian giả lập API delay xuống 150ms để có cảm giác nhanh hơn
                const timer = setTimeout(() => {
                        // First filter by league
                        let filtered = [...DEMO_MATCHES];
                        if (activeLeague !== "all") {
                                filtered = filtered.filter((match) => match.leagueName === activeLeague);
                        }

                        // Then sort based on selected sort option
                        filtered = sortMatches(filtered, sortBy);

                        setFilteredMatches(filtered);
                        setIsLoading(false);

                        // Save scroll position to improve UX
                        if (sectionRef.current) {
                                sessionStorage.setItem('featuredMatchesScrollPos', sectionRef.current.scrollTop.toString());
                        }
                }, 150); // Giảm thời gian từ 200ms xuống 150ms

                // Không cuộn lên đầu trang khi thay đổi bộ lọc để cải thiện UX
                // window.scrollTo({ top: 0, behavior: 'smooth' });

                return () => clearTimeout(timer);
        }, [activeLeague, sortBy]);

        // Function to sort matches based on different criteria
        const sortMatches = (matches: typeof DEMO_MATCHES, sortOption: string) => {
                const sorted = [...matches];
                switch (sortOption) {
                        case "date":
                                // Sort by date (newest first)
                                return sorted.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
                        case "league":
                                // Sort alphabetically by league name
                                return sorted.sort((a, b) => a.leagueName.localeCompare(b.leagueName));
                        case "hot":
                                // Sort by "hot" matches first
                                return sorted.sort((a, b) => (b.isHot ? 1 : 0) - (a.isHot ? 1 : 0));
                        case "live":
                                // Sort by live matches first, then upcoming, then finished
                                return sorted.sort((a, b) => {
                                        const aLive = a.status === "LIVE" || a.status === "1H" || a.status === "2H" || a.status === "HT";
                                        const bLive = b.status === "LIVE" || b.status === "1H" || b.status === "2H" || b.status === "HT";
                                        const aFinished = a.status === "FT" || a.status === "AET" || a.status === "PEN";
                                        const bFinished = b.status === "FT" || b.status === "AET" || b.status === "PEN";

                                        if (aLive && !bLive) return -1;
                                        if (!aLive && bLive) return 1;
                                        if (!aFinished && bFinished) return -1;
                                        if (aFinished && !bFinished) return 1;
                                        return new Date(a.date).getTime() - new Date(b.date).getTime();
                                });
                        case "score":
                                // Sort by total goals (highest first)
                                return sorted.sort((a, b) => {
                                        const totalGoalsA = a.goalsHome + a.goalsAway;
                                        const totalGoalsB = b.goalsHome + b.goalsAway;
                                        return totalGoalsB - totalGoalsA;
                                });
                        case "team":
                                // Sort alphabetically by home team name
                                return sorted.sort((a, b) => a.homeTeamName.localeCompare(b.homeTeamName));
                        default:
                                return sorted;
                }
        };

        const handleLeagueChange = (league: string) => {
                setActiveLeague(league);
        }; const handleSortChange = (sortOption: string) => {
                // Only set loading and change sort if it's actually different
                if (sortOption !== sortBy) {
                        // Lưu tùy chọn sắp xếp mới ngay lập tức
                        setSortBy(sortOption);

                        // Áp dụng trạng thái loading trong thời gian rất ngắn
                        setIsLoading(true);

                        // Giảm thời gian hiệu ứng loading xuống còn 100ms để cảm giác nhanh hơn nữa
                        setTimeout(() => {
                                setIsLoading(false);

                                // Thêm class cho hiệu ứng fade-in sau khi dữ liệu đã thay đổi
                                const gridElement = document.querySelector('.featured-matches-v2 .grid');
                                if (gridElement) {
                                        gridElement.classList.add('sort-transition-complete');

                                        // Xóa class sau khi hiệu ứng hoàn thành - giảm thời gian
                                        setTimeout(() => {
                                                gridElement.classList.remove('sort-transition-complete');
                                        }, 200); // Giảm thời gian hiệu ứng xuống từ 300ms còn 200ms
                                }
                        }, 100); // Giảm thời gian loading xuống từ 150ms còn 100ms
                }
        };

        // Add useEffect to show sorting notification
        useEffect(() => {
                if (sortBy !== "date") {
                        // Tạo thông báo đẹp hơn với icon phù hợp
                        const message = document.createElement('div');
                        message.className = 'sorting-message';

                        // Thêm icon tương ứng với tùy chọn sắp xếp
                        let icon = '';
                        let sortText = '';

                        switch (sortBy) {
                                case 'live':
                                        icon = '🔴';
                                        sortText = 'Showing live matches first';
                                        break;
                                case 'hot':
                                        icon = '🔥';
                                        sortText = 'Showing hot matches first';
                                        break;
                                case 'league':
                                        icon = '🏆';
                                        sortText = 'Sorted by league name';
                                        break;
                                case 'score':
                                        icon = '⚽';
                                        sortText = 'Highest scoring matches first';
                                        break;
                                case 'team':
                                        icon = '👕';
                                        sortText = 'Sorted by team name';
                                        break;
                                default:
                                        icon = '📊';
                                        sortText = `Sorting by ${sortBy}`;
                        }

                        message.innerHTML = `
                                <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="font-size: 1.2em;">${icon}</span>
                                        <span>${sortText}</span>
                                </div>
                        `;

                        // Cải thiện style cho thông báo
                        message.style.position = 'fixed';
                        message.style.bottom = '20px';
                        message.style.right = '20px';
                        message.style.padding = '12px 16px';
                        message.style.background = 'rgba(30, 41, 59, 0.9)';
                        message.style.color = 'white';
                        message.style.borderRadius = '8px';
                        message.style.zIndex = '1000';
                        message.style.opacity = '0';
                        message.style.transform = 'translateY(20px)';
                        message.style.transition = 'opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                        message.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                        message.style.border = '1px solid rgba(255, 255, 255, 0.1)';
                        message.style.backdropFilter = 'blur(8px)';
                        // Apply webkit prefix as a string to avoid TypeScript error
                        (message.style as any)['-webkit-backdrop-filter'] = 'blur(8px)';

                        document.body.appendChild(message);

                        // Show notification with slight delay for better UX - giảm xuống còn 10ms
                        setTimeout(() => {
                                message.style.opacity = '1';
                                message.style.transform = 'translateY(0)';
                        }, 10);

                        // Hide and remove notification after completion - giảm xuống còn 1000ms
                        setTimeout(() => {
                                message.style.opacity = '0';
                                message.style.transform = 'translateY(20px)';
                                setTimeout(() => {
                                        if (document.body.contains(message)) {
                                                document.body.removeChild(message);
                                        }
                                }, 200); // Giảm thời gian từ 300ms xuống 200ms
                        }, 1000); // Giảm thời gian từ 1200ms xuống 1000ms

                        // Cleanup function to remove the notification if component unmounts
                        return () => {
                                if (document.body.contains(message)) {
                                        document.body.removeChild(message);
                                }
                        };
                }
        }, [sortBy]);

        // Preload feature to improve perceived performance
        const preloadNextState = (nextLeague: string, nextSortBy: string) => {
                // Create a hidden copy of the data with the next state already processed
                // This makes the next state change appear instant
                let preloadedData = [...DEMO_MATCHES];

                if (nextLeague !== "all") {
                        preloadedData = preloadedData.filter((match) => match.leagueName === nextLeague);
                }

                return sortMatches(preloadedData, nextSortBy);
        };

        // Preload data when hovering over filter options
        const handlePreloadData = (league: string) => {
                if (league !== activeLeague) {
                        // Precompute data for potential filter change
                        preloadNextState(league, sortBy);
                }
        };

        // Preload data when hovering over sort options
        const handlePreloadSort = (sortOption: string) => {
                if (sortOption !== sortBy) {
                        // Precompute data for potential sort change
                        preloadNextState(activeLeague, sortOption);
                }
        };

        const handleViewMatchDetails = (matchId: number) => {
                // In a real application, this would navigate to a match details page
                console.log(`Viewing details for match ${matchId}`);
                alert(`This would navigate to match details for match ID: ${matchId}`);
        };

        const handleLoadMore = () => {
                // Simulate loading more matches
                setIsLoading(true);

                setTimeout(() => {
                        // For demo purposes, we'll just show an alert
                        // In a real app, this would fetch more matches from the API
                        alert("In a real application, this would load more matches from the API");
                        setIsLoading(false);
                }, 1000);
        };

        return (
                <section
                        ref={sectionRef as React.RefObject<HTMLElement>}
                        className={`featured-matches-v2 featured-matches-fullwidth professional-section-bg ${isLoading ? 'loading' : ''}`}
                        aria-labelledby="featured-matches-heading"
                        aria-label="Featured football matches section with live scores and upcoming fixtures"
                        role="region"
                >
                        <div className="featured-matches-inner-container">
                                <div className="flex items-center justify-between mb-8">
                                        <div>
                                                <h2 id="featured-matches-heading" className="text-3xl font-bold gradient-text mb-2">Featured Matches</h2>
                                                <p className="text-gray-400">Top fixtures from around the world</p>
                                        </div>
                                        <div className="flex gap-2">
                                                <button
                                                        className="futuristic-button px-6 py-3 rounded-xl text-sm font-medium neon-glow-purple"
                                                        aria-label="View all matches in detailed view"
                                                >
                                                        View All Matches
                                                </button>
                                        </div>
                                </div>

                                {/* Filter bar */}
                                <FilterBar
                                        activeLeague={activeLeague}
                                        onLeagueChange={handleLeagueChange}
                                        availableLeagues={availableLeagues}
                                        matchCount={filteredMatches.length}
                                        activeSortBy={sortBy}
                                        onSortChange={handleSortChange}
                                        onPreloadLeague={handlePreloadData}
                                        onPreloadSort={handlePreloadSort}
                                />

                                {/* Loading state - optimized to be less intrusive */}
                                {isLoading && (
                                        <div className="py-4 text-center" role="status" aria-live="polite" aria-label="Loading matches">
                                                <div className="inline-flex items-center justify-center space-x-2">
                                                        <div className="loading-spinner-mini inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-purple-500 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                                                                <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
                                                        </div>
                                                        <p className="text-gray-400 text-sm">Updating...</p>
                                                </div>
                                        </div>
                                )}

                                {/* Error state */}
                                {!isLoading && error && (
                                        <div className="col-span-full text-center py-16" role="alert" aria-live="assertive">
                                                <div className="text-red-500 mb-4">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 opacity-70" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                        <p className="text-xl font-medium">Error loading matches</p>
                                                        <p className="mt-2 text-gray-400">{error}</p>
                                                </div>
                                                <button
                                                        onClick={() => {
                                                                setError(null);
                                                                setActiveLeague("all");
                                                        }}
                                                        className="futuristic-button px-6 py-3 rounded-xl text-sm font-medium mt-4"
                                                        aria-label="Retry loading matches"
                                                >
                                                        Try Again
                                                </button>
                                        </div>
                                )}

                                {/* Match Cards Grid */}
                                {!isLoading && !error && (
                                        <div>
                                                {filteredMatches.length > 0 ? (
                                                        // Regular grid without date grouping
                                                        <div
                                                                className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
                                                                aria-label="Featured football matches grid"
                                                        >
                                                                {filteredMatches.map((match, index) => (
                                                                        <article
                                                                                key={match.id}
                                                                                className="match-card-item"
                                                                                aria-label={`Match ${index + 1}: ${match.homeTeamName} vs ${match.awayTeamName}`}
                                                                        >
                                                                                <MatchCard
                                                                                        match={match}
                                                                                        onViewDetails={handleViewMatchDetails}
                                                                                />
                                                                        </article>
                                                                ))}
                                                        </div>
                                                ) : (
                                                        <div className="col-span-full text-center py-16" role="status">
                                                                <div className="text-gray-400 mb-4">
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                                                        </svg>
                                                                        <p className="text-xl font-medium">No matches found</p>
                                                                        <p className="mt-2">Try selecting a different league or check back later</p>
                                                                </div>
                                                                <button
                                                                        onClick={() => setActiveLeague("all")}
                                                                        className="futuristic-button px-6 py-3 rounded-xl text-sm font-medium mt-4"
                                                                        aria-label="View all leagues to find matches"
                                                                >
                                                                        View All Leagues
                                                                </button>
                                                        </div>
                                                )}
                                        </div>
                                )}

                                {/* View More Button - only show if we have matches */}
                                {!isLoading && !error && filteredMatches.length > 0 && (
                                        <div className="text-center mt-8">
                                                <button
                                                        onClick={handleLoadMore}
                                                        className="futuristic-button-outline px-8 py-3 rounded-xl text-sm font-medium border border-purple-500/30 hover:border-purple-500/60 transition-all"
                                                        aria-label="Load more featured matches"
                                                >
                                                        Load More Matches
                                                </button>
                                        </div>
                                )}
                        </div>
                </section>
        );
};

export default FeaturedMatches;
