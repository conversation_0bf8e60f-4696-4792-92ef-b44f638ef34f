/* Hero v3 CSS - Championship Stadium Experience */

/* Full-width viewport utilities */
.hero-fullwidth {
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
        width: 100vw;
        max-width: none;
        position: relative;
}

/* Prevent horizontal overflow */
.hero-fullwidth-container {
        overflow-x: hidden;
}

/* Football Field Background Effects */
@keyframes fieldGlow {

        0%,
        100% {
                opacity: 0.7;
        }

        50% {
                opacity: 0.9;
        }
}

.field-glow {
        animation: fieldGlow 5s infinite ease-in-out;
}

/* Stadium Lights Animation */
@keyframes stadiumLights {

        0%,
        100% {
                opacity: 0.6;
                transform: scale(1) translateY(0);
                box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);
        }

        50% {
                opacity: 1;
                transform: scale(1.1) translateY(-5px);
                box-shadow: 0 0 30px rgba(255, 193, 7, 1), 0 0 45px rgba(255, 152, 0, 0.8);
        }
}

.stadium-light {
        animation: stadiumLights 3s ease-in-out infinite;
        transform-origin: top center;
}

/* Field Marking Styles */
.blend-overlay {
        mix-blend-mode: overlay;
}

/* Soccer Ball Animation */
@keyframes rotateBall {
        0% {
                transform: rotate(0deg) translateY(0);
                opacity: 0.8;
        }

        50% {
                transform: rotate(180deg) translateY(-10px);
                opacity: 1;
        }

        100% {
                transform: rotate(360deg) translateY(0);
                opacity: 0.8;
        }
}

.floating-ball {
        animation: rotateBall 8s infinite ease-in-out;
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

/* Floating Particles Animation */
@keyframes float {

        0%,
        100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.7;
        }

        25% {
                transform: translateY(-20px) translateX(10px);
                opacity: 1;
        }

        50% {
                transform: translateY(-40px) translateX(-5px);
                opacity: 0.8;
        }

        75% {
                transform: translateY(-20px) translateX(-10px);
                opacity: 0.9;
        }
}

.float-animation {
        animation: float 6s ease-in-out infinite;
}

/* Championship Title Glow */
@keyframes championshipGlow {

        0%,
        100% {
                text-shadow:
                        0 0 20px rgba(168, 85, 247, 0.8),
                        0 0 40px rgba(59, 130, 246, 0.6),
                        0 0 60px rgba(168, 85, 247, 0.4);
        }

        50% {
                text-shadow:
                        0 0 30px rgba(59, 130, 246, 0.8),
                        0 0 60px rgba(168, 85, 247, 0.6),
                        0 0 80px rgba(59, 130, 246, 0.4);
        }
}

.championship-title {
        animation: championshipGlow 3s ease-in-out infinite;
}

/* Live Pulse Animation */
@keyframes livePulse {

        0%,
        100% {
                opacity: 1;
                transform: scale(1);
        }

        50% {
                opacity: 0.7;
                transform: scale(1.1);
        }
}

.live-indicator {
        animation: livePulse 1.5s ease-in-out infinite;
}

/* 3D Card Hover Effect */
.match-card {
        transform-style: preserve-3d;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.match-card:hover {
        transform: rotateY(5deg) rotateX(2deg) translateZ(10px);
        box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(168, 85, 247, 0.3);
}

/* Digital Clock Animation */
@keyframes digitalFlicker {

        0%,
        98%,
        100% {
                opacity: 1;
                text-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
        }

        99% {
                opacity: 0.8;
                text-shadow: 0 0 5px rgba(59, 130, 246, 0.4);
        }
}

.digital-clock {
        animation: digitalFlicker 3s ease-in-out infinite;
}

/* League Tab Animation */
.league-tab {
        position: relative;
        overflow: hidden;
}

.league-tab::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
                        transparent,
                        rgba(255, 255, 255, 0.2),
                        transparent);
        transition: left 0.5s;
}

.league-tab:hover::before {
        left: 100%;
}

/* Stadium Atmosphere Particles */
@keyframes atmosphereFloat {
        0% {
                transform: translateY(100vh) translateX(0px) scale(0);
                opacity: 0;
        }

        10% {
                opacity: 1;
        }

        90% {
                opacity: 1;
        }

        100% {
                transform: translateY(-10vh) translateX(50px) scale(1);
                opacity: 0;
        }
}

.atmosphere-particle {
        animation: atmosphereFloat 8s ease-in-out infinite;
}

/* Countdown Timer Glow */
@keyframes countdownGlow {

        0%,
        100% {
                background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }

        50% {
                background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
                box-shadow: 0 0 30px rgba(239, 68, 68, 0.5);
        }
}

.countdown-container {
        animation: countdownGlow 2s ease-in-out infinite;
}

/* Team Logo Spin on Hover */
.team-logo {
        transition: all 0.3s ease-in-out;
}

.team-logo:hover {
        transform: rotate(360deg) scale(1.1);
}

/* Statistics Card Pulse */
@keyframes statsPulse {

        0%,
        100% {
                background: rgba(0, 0, 0, 0.4);
                border-color: rgba(107, 114, 128, 0.3);
        }

        50% {
                background: rgba(0, 0, 0, 0.6);
                border-color: rgba(107, 114, 128, 0.5);
        }
}

.stats-card {
        animation: statsPulse 3s ease-in-out infinite;
}

/* Championship Banner Effect */
@keyframes bannerWave {

        0%,
        100% {
                transform: translateY(0px) rotate(0deg);
        }

        25% {
                transform: translateY(-5px) rotate(1deg);
        }

        50% {
                transform: translateY(0px) rotate(0deg);
        }

        75% {
                transform: translateY(-3px) rotate(-1deg);
        }
}

.championship-banner {
        animation: bannerWave 4s ease-in-out infinite;
}

/* VS Badge Rotation */
@keyframes vsRotate {

        0%,
        100% {
                transform: rotateY(0deg) scale(1);
        }

        50% {
                transform: rotateY(180deg) scale(1.1);
        }
}

.vs-badge {
        animation: vsRotate 3s ease-in-out infinite;
}

/* Empty State Animations */
.empty-state-icon {
        animation: emptyStateFloat 3s ease-in-out infinite;
}

@keyframes emptyStateFloat {

        0%,
        100% {
                transform: translateY(0);
        }

        50% {
                transform: translateY(-10px);
        }
}

.empty-state-dots {
        animation: emptyStatePulse 2s ease-in-out infinite;
}

@keyframes emptyStatePulse {

        0%,
        100% {
                opacity: 0.5;
                transform: scale(1);
        }

        50% {
                opacity: 1;
                transform: scale(1.2);
        }
}

/* No Match State */
.no-match-indicator {
        animation: noMatchPulse 2s ease-in-out infinite;
}

@keyframes noMatchPulse {

        0%,
        100% {
                opacity: 0.6;
        }

        50% {
                opacity: 1;
        }
}

/* Neutral Trend Indicator */
.trend-neutral {
        color: #9CA3AF;
        opacity: 0.7;
}

/* Empty State Card */
.empty-state-card {
        background: linear-gradient(135deg, rgba(55, 65, 81, 0.8) 0%, rgba(31, 41, 55, 0.6) 100%);
        backdrop-filter: blur(16px);
        border: 1px solid rgba(107, 114, 128, 0.3);
        animation: emptyStateGlow 4s ease-in-out infinite;
}

@keyframes emptyStateGlow {

        0%,
        100% {
                box-shadow: 0 0 20px rgba(107, 114, 128, 0.1);
        }

        50% {
                box-shadow: 0 0 30px rgba(107, 114, 128, 0.2);
        }
}

/* Gradient Pulse Animation */
@keyframes gradientPulse {

        0%,
        100% {
                opacity: 0.5;
                transform: scale(1);
        }

        50% {
                opacity: 0.7;
                transform: scale(1.05);
        }
}

.gradient-pulse {
        animation: gradientPulse 8s infinite ease-in-out;
}

/* Field Pattern Animation */
@keyframes fieldPatternRotate {
        0% {
                transform: rotate(0deg);
                opacity: 0.05;
        }

        50% {
                opacity: 0.07;
        }

        100% {
                transform: rotate(360deg);
                opacity: 0.05;
        }
}

.field-pattern-rotate {
        animation: fieldPatternRotate 120s infinite linear;
}

/* Field Glow Animation */
@keyframes fieldGlow {

        0%,
        100% {
                opacity: 0.15;
                box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
        }

        50% {
                opacity: 0.25;
                box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
        }
}

.field-glow {
        animation: fieldGlow 5s infinite ease-in-out;
}

/* Enhanced Ball Animation */
@keyframes enhancedBallFloat {
        0% {
                transform: translateY(0) rotate(0deg);
                filter: drop-shadow(0 5px 15px rgba(255, 255, 255, 0.3));
        }

        25% {
                transform: translateY(-15px) rotate(90deg);
                filter: drop-shadow(0 15px 20px rgba(255, 255, 255, 0.4));
        }

        50% {
                transform: translateY(0) rotate(180deg);
                filter: drop-shadow(0 5px 15px rgba(255, 255, 255, 0.3));
        }

        75% {
                transform: translateY(15px) rotate(270deg);
                filter: drop-shadow(0 15px 20px rgba(255, 255, 255, 0.4));
        }

        100% {
                transform: translateY(0) rotate(360deg);
                filter: drop-shadow(0 5px 15px rgba(255, 255, 255, 0.3));
        }
}

.enhanced-ball {
        animation: enhancedBallFloat 12s infinite ease-in-out;
        filter: drop-shadow(0 8px 15px rgba(255, 255, 255, 0.2));
}

/* Crowd Movement Animation */
@keyframes crowdMove {
        0% {
                transform: translateY(0);
                opacity: 0.6;
        }

        50% {
                transform: translateY(-2px);
                opacity: 1;
        }

        100% {
                transform: translateY(0);
                opacity: 0.7;
        }
}

/* Camera Flash Animation */
@keyframes cameraFlash {

        0%,
        85%,
        100% {
                opacity: 0;
        }

        90%,
        92% {
                opacity: 1;
                box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }
}

.camera-flash {
        animation: cameraFlash 8s infinite;
}

/* Light Scan Animation */
@keyframes lightScan {
        0% {
                transform: translateX(-100%);
                opacity: 0.2;
        }

        50% {
                opacity: 0.4;
        }

        100% {
                transform: translateX(200%);
                opacity: 0.2;
        }
}

.light-scan {
        animation: lightScan 20s infinite linear;
}

@media (max-width: 768px) {
        .match-card:hover {
                transform: none;
        }

        .championship-title {
                animation-duration: 4s;
        }

        .stadium-light {
                animation-duration: 3s;
        }
}