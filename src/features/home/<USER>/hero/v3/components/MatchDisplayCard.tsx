"use client";

import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

interface MatchData {
        id: number;
        homeTeam: string;
        awayTeam: string;
        homeLogo: string;
        awayLogo: string;
        date: string;
        time: string;
        venue: string;
        league: string;
        importance: string;
        isNextMatch: boolean;
        weather: string;
        // New fields for watch URL generation
        slug?: string;           // URL-friendly match identifier
        fixtureId?: string;      // Official fixture ID
}

interface CountdownTime {
        hours: number;
        minutes: number;
        seconds: number;
}

interface MatchDisplayCardProps {
        match: MatchData;
        countdown: CountdownTime;
}

export const MatchDisplayCard: React.FC<MatchDisplayCardProps> = ({
        match,
        countdown
}) => {
        const router = useRouter();

        // Function to generate URL slug from team names
        const generateSlug = (homeTeam: string, awayTeam: string): string => {
                const slugText = `${homeTeam}-vs-${awayTeam}`
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '');
                return slugText;
        };

        // Function to generate watch URL
        const generateWatchUrl = (match: MatchData): string => {
                const slug = match.slug || generateSlug(match.homeTeam, match.awayTeam);
                const fixtureId = match.fixtureId || `fixture-${match.id}`;
                return `/watch/${slug}/${fixtureId}`;
        };

        // Handle watch button click
        const handleWatchClick = () => {
                const watchUrl = generateWatchUrl(match);
                router.push(watchUrl);
        };

        return (
                <div className="match-card bg-gradient-to-br from-gray-900/80 to-slate-800/60 backdrop-blur-lg rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
                        <div className="flex items-center justify-between mb-6">
                                <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center space-x-3">
                                        <span className="live-indicator w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                                🔥
                                        </span>
                                        <span>Trận Đại Chiến Tiếp Theo</span>
                                </h2>
                                <div className="text-right">
                                        <p className="text-gray-400 text-sm">Countdown</p>
                                        <div className="flex space-x-2 text-white font-mono font-bold text-xl">
                                                <span>{countdown.hours.toString().padStart(2, '0')}</span>
                                                <span className="text-gray-500">:</span>
                                                <span>{countdown.minutes.toString().padStart(2, '0')}</span>
                                                <span className="text-gray-500">:</span>
                                                <span>{countdown.seconds.toString().padStart(2, '0')}</span>
                                        </div>
                                </div>
                        </div>

                        <div className="relative">
                                {/* Match Background Glow */}
                                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-purple-600/10 rounded-2xl blur-xl"></div>

                                <div className="relative bg-gradient-to-r from-gray-800/50 to-gray-900/50 rounded-2xl p-6 border border-gray-600/30">
                                        {/* Teams Display */}
                                        <div className="flex items-center justify-between mb-6">
                                                {/* Home Team */}
                                                <div className="flex items-center space-x-4 flex-1">
                                                        <div className="relative">
                                                                <Image
                                                                        src={match.homeLogo}
                                                                        alt={`${match.homeTeam} logo`}
                                                                        width={64}
                                                                        height={64}
                                                                        className="rounded-full border-2 border-white/20"
                                                                />
                                                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-800"></div>
                                                        </div>
                                                        <div>
                                                                <h3 className="text-xl md:text-2xl font-bold text-white vietnamese-text">
                                                                        {match.homeTeam}
                                                                </h3>
                                                                <p className="text-gray-400 text-sm">Home</p>
                                                        </div>
                                                </div>

                                                {/* VS Section */}
                                                <div className="text-center px-8 flex-shrink-0">
                                                        <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-2 shadow-lg shadow-purple-500/30 mx-auto">
                                                                <span className="text-white font-bold text-lg">VS</span>
                                                        </div>
                                                        <p className="text-gray-300 text-sm font-semibold">{match.time}</p>
                                                </div>

                                                {/* Away Team */}
                                                <div className="flex items-center justify-end space-x-4 flex-1">
                                                        <div className="text-right">
                                                                <h3 className="text-xl md:text-2xl font-bold text-white vietnamese-text">
                                                                        {match.awayTeam}
                                                                </h3>
                                                                <p className="text-gray-400 text-sm">Away</p>
                                                        </div>
                                                        <div className="relative">
                                                                <Image
                                                                        src={match.awayLogo}
                                                                        alt={`${match.awayTeam} logo`}
                                                                        width={64}
                                                                        height={64}
                                                                        className="rounded-full border-2 border-white/20"
                                                                />
                                                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-800"></div>
                                                        </div>
                                                </div>
                                        </div>

                                        {/* Match Details */}
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                                <div className="bg-black/30 rounded-lg p-3 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">Venue</p>
                                                        <p className="text-white font-semibold text-sm">{match.venue}</p>
                                                </div>
                                                <div className="bg-black/30 rounded-lg p-3 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">League</p>
                                                        <p className="text-white font-semibold text-sm">{match.league}</p>
                                                </div>
                                                <div className="bg-black/30 rounded-lg p-3 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">Weather</p>
                                                        <p className="text-white font-semibold text-sm">{match.weather}</p>
                                                </div>
                                                <div className="bg-black/30 rounded-lg p-3 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">Status</p>
                                                        <div className="flex items-center justify-center space-x-1">
                                                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                                                <p className="text-green-400 font-semibold text-sm">Ready</p>
                                                        </div>
                                                </div>
                                        </div>

                                        {/* Action Button */}
                                        <div className="mt-6 text-center">
                                                <button
                                                        onClick={handleWatchClick}
                                                        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/30"
                                                >
                                                        <span className="flex items-center space-x-2">
                                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                                </svg>
                                                                <span>Xem Trực Tiếp</span>
                                                        </span>
                                                </button>

                                                {/* Debug info - URL preview */}
                                                <div className="mt-2 text-xs text-gray-500">
                                                        <p>📺 {generateWatchUrl(match)}</p>
                                                </div>
                                        </div>
                                </div>
                        </div>
                </div>
        );
};
