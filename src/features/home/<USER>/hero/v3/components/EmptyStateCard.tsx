"use client";

import React from "react";

interface EmptyStateCardProps {
        onToggleMatches: () => void;
        hasMatches: boolean;
}

export const EmptyStateCard: React.FC<EmptyStateCardProps> = ({ 
        onToggleMatches, 
        hasMatches 
}) => {
        return (
                <div className="match-card empty-state-card bg-gradient-to-br from-gray-900/80 to-slate-800/60 backdrop-blur-lg rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
                        <div className="text-center py-12">
                                {/* Empty State Icon */}
                                <div className="relative mb-8">
                                        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-full flex items-center justify-center border border-gray-600/30 empty-state-icon">
                                                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                        </div>
                                        {/* Floating dots around empty icon */}
                                        <div className="absolute -top-2 -right-2 w-3 h-3 bg-gray-500/50 rounded-full empty-state-dots"></div>
                                        <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-gray-500/30 rounded-full empty-state-dots" style={{animationDelay: '1s'}}></div>
                                </div>

                                {/* Empty State Title */}
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-300 mb-4 vietnamese-text">
                                        Hôm Nay Không Có Trận Đấu
                                </h2>

                                {/* Empty State Description */}
                                <p className="text-lg text-gray-400 mb-8 max-w-md mx-auto vietnamese-text leading-relaxed">
                                        Không có trận đấu nào được lên lịch cho hôm nay. Hãy quay lại vào các ngày khác để theo dõi những trận cầu hấp dẫn!
                                </p>

                                {/* Next Match Info */}
                                <div className="bg-black/40 rounded-2xl p-6 border border-gray-600/30 mb-8">
                                        <h3 className="text-xl font-semibold text-white mb-4 vietnamese-text">Trận Đấu Tiếp Theo</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                                                <div>
                                                        <p className="text-gray-400 text-sm uppercase tracking-wider">Ngày</p>
                                                        <p className="text-white font-bold">7/6/2025</p>
                                                </div>
                                                <div>
                                                        <p className="text-gray-400 text-sm uppercase tracking-wider">Giải Đấu</p>
                                                        <p className="text-white font-bold">Premier League</p>
                                                </div>
                                                <div>
                                                        <p className="text-gray-400 text-sm uppercase tracking-wider">Số Trận</p>
                                                        <p className="text-white font-bold">3 Trận</p>
                                                </div>
                                        </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                        <button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                                                <span className="flex items-center justify-center space-x-2">
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                        <span>Xem Lịch Tuần</span>
                                                </span>
                                        </button>
                                        <button 
                                                onClick={onToggleMatches}
                                                className="bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105"
                                        >
                                                <span className="flex items-center justify-center space-x-2">
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                        </svg>
                                                        <span>Refresh Data</span>
                                                </span>
                                        </button>
                                </div>

                                {/* Test Toggle Button (for demo purposes) */}
                                <div className="mt-8 pt-6 border-t border-gray-700/30">
                                        <button 
                                                onClick={onToggleMatches}
                                                className="text-sm text-gray-500 hover:text-gray-400 transition-colors"
                                        >
                                                [Demo: Toggle Match Data - Hiện tại: {hasMatches ? 'Có trận đấu' : 'Không có trận đấu'}]
                                        </button>
                                </div>
                        </div>
                </div>
        );
};
