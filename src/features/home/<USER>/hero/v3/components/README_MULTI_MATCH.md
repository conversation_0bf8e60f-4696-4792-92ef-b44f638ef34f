# MultiMatchDisplayCard - Multiple Matches Display System

## 🎯 Tổng quan

`MultiMatchDisplayCard` là component nâng cao của Hero v3 để hiển thị nhiều trận đấu cùng lúc với 3 display modes kh<PERSON><PERSON> nhau, phụ<PERSON> vụ cho các trường hợp có nhiều trận đấu diễn ra trong cùng một thời điểm.

## 🏗️ Component Architecture

### **Interface Definition**
```tsx
interface MultiMatchDisplayCardProps {
        matches: MatchData[];           // Danh sách các trận đấu
        countdown: CountdownTime;       // Countdown timer
        displayMode?: 'grid' | 'carousel' | 'stack';  // Chế độ hiển thị
        maxDisplayed?: number;          // Số trận đấu tối đa hiển thị
}
```

### **Display Modes**

#### 1. **Stack Mode** (Default)
- **Use Case**: <PERSON>hi có 2-5 trận đấu
- **Layout**: Main match lớn + secondary matches nhỏ hơn xếp chồng
- **Features**: 
  - Tr<PERSON><PERSON> ch<PERSON>h hiển thị full details
  - Trận phụ hiển thị compact info
  - Show more indicator cho trận đấu bổ sung

#### 2. **Carousel Mode**
- **Use Case**: Khi muốn focus từng trận một
- **Layout**: Single match display với navigation dots
- **Features**:
  - Interactive navigation
  - Smooth transition giữa các trận
  - Current match indicator

#### 3. **Grid Mode**
- **Use Case**: Khi muốn overview tất cả trận đấu
- **Layout**: Grid layout 2 columns trên desktop
- **Features**:
  - Equal sizing cho tất cả matches
  - Responsive design
  - Compact view

## 🎨 Visual Design Features

### **Adaptive Sizing**
```tsx
// Main match (isMainMatch=true)
width={isMainMatch ? 64 : 48}
height={isMainMatch ? 64 : 48}
className={`${isMainMatch ? 'text-xl md:text-2xl' : 'text-lg'} font-bold`}

// Main match details
{isMainMatch && (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        // Venue, League, Weather, Status
    </div>
)}

// Secondary match mini details
{!isMainMatch && (
    <div className="flex justify-between items-center text-xs">
        // Venue, League, Date
    </div>
)}
```

### **Visual Hierarchy**
- **Main Match**: Full details với large logos và complete info cards
- **Secondary Matches**: Compact layout với essential info
- **Status Indicators**: Consistent green dots cho live status
- **Action Buttons**: Context-aware (single match vs multiple matches)

## 🔄 Integration với Hero v3

### **Toggle System**
```tsx
const [displayMode, setDisplayMode] = useState<'single' | 'multiple'>('single');

// Toggle function
const toggleDisplayMode = () => {
    setDisplayMode(displayMode === 'single' ? 'multiple' : 'single');
};

// Conditional rendering
{displayMode === 'single' && nextMatch ? (
    <MatchDisplayCard match={nextMatch} countdown={countdown} />
) : upcomingMatches.length > 0 ? (
    <MultiMatchDisplayCard 
        matches={upcomingMatches} 
        countdown={countdown} 
        displayMode="stack" 
    />
) : (
    <EmptyStateCard />
)}
```

### **Toggle Buttons**
- **Single → Multiple**: "📋 Xem Tất Cả Trận"
- **Multiple → Single**: "🎯 Xem Trận Chính"

## 📊 Data Management

### **Enhanced Match Data**
```tsx
// Expanded match data (5 matches thay vì 3)
{
    id: 4,
    homeTeam: "Juventus",
    awayTeam: "AC Milan",
    date: "Today",
    time: "22:45",
    venue: "Allianz Stadium",
    league: "Serie A",
    importance: "high",
    weather: "🌙 19°C"
},
{
    id: 5,
    homeTeam: "PSG", 
    awayTeam: "Marseille",
    date: "Tomorrow",
    time: "20:00",
    venue: "Parc des Princes",
    league: "Ligue 1",
    importance: "medium",
    weather: "☁️ 16°C"
}
```

### **Smart Filtering**
- `maxDisplayed` parameter để control số lượng
- Show more indicator cho remaining matches
- Priority-based sorting (importance + isNextMatch)

## 🎯 UX Benefits

### **User Choice**
- Toggle giữa focused view và overview
- Flexible display modes cho different scenarios
- Consistent navigation patterns

### **Information Density**
- **Single Mode**: Deep focus, full details
- **Multiple Mode**: Breadth overview, essential info
- **Progressive Disclosure**: Main → Secondary → More

### **Responsive Design**
- Grid adapts từ 2 columns → 1 column trên mobile
- Logo sizes scale appropriately
- Touch-friendly navigation dots

## 🚀 Performance Optimizations

### **Conditional Rendering**
```tsx
{isMainMatch && (
    // Full details chỉ render cho main match
)}

{!isMainMatch && (
    // Mini details cho secondary matches
)}
```

### **Lazy Loading Ready**
- Image components với proper alt text
- Prepared cho lazy loading implementation
- Optimized re-renders với proper keys

## 📱 Mobile Experience

### **Touch Interactions**
- Larger touch targets cho navigation
- Swipe-friendly carousel mode
- Optimized button spacing

### **Layout Adaptations**
- Stack mode works best trên mobile
- Grid collapses to single column
- Maintained visual hierarchy

## 🎮 Interactive Features

### **Carousel Navigation**
```tsx
// Navigation dots
{displayedMatches.map((_, index) => (
    <button
        onClick={() => setCurrentMatchIndex(index)}
        className={`w-3 h-3 rounded-full transition-all duration-300 ${
            index === currentMatchIndex 
                ? 'bg-purple-500 scale-125' 
                : 'bg-gray-500 hover:bg-gray-400'
        }`}
    />
))}
```

### **State Management**
- Current match index tracking
- Smooth transitions
- Active state indicators

## 🔮 Future Enhancements

### **Potential Features**
1. **Auto-rotation** trong carousel mode
2. **Drag & drop** reordering
3. **Filter by league** functionality
4. **Live score updates** integration
5. **Favorite matches** starring

### **Animation Improvements**
1. **Enter/exit animations** khi toggle modes
2. **Stagger animations** cho multiple matches
3. **Hover effects** improvement
4. **Loading states** cho dynamic data

## ✅ Implementation Status

### **Completed Features**
- ✅ 3 display modes (stack, carousel, grid)
- ✅ Toggle system integration
- ✅ Responsive design
- ✅ TypeScript interfaces
- ✅ Visual hierarchy
- ✅ Action buttons
- ✅ Navigation system

### **Testing Status**
- ✅ Component compilation
- ✅ Props type safety
- ✅ Integration with Hero v3
- ✅ Toggle functionality
- ✅ Multiple match data display

### **Browser Compatibility**
- ✅ Modern browsers support
- ✅ Mobile responsive
- ✅ Touch interactions
- ✅ CSS Grid support

## 🎉 Usage Examples

### **Basic Usage**
```tsx
<MultiMatchDisplayCard
    matches={allMatches}
    countdown={countdown}
    displayMode="stack"
    maxDisplayed={3}
/>
```

### **Carousel Mode**
```tsx
<MultiMatchDisplayCard
    matches={todayMatches}
    countdown={countdown}
    displayMode="carousel"
    maxDisplayed={5}
/>
```

### **Grid Overview**
```tsx
<MultiMatchDisplayCard
    matches={weekendMatches}
    countdown={countdown}
    displayMode="grid"
    maxDisplayed={4}
/>
```

---

**Status**: ✅ **HOÀN THÀNH**  
**Date**: June 6, 2025  
**Integration**: Hero v3 Compatible  
**Performance**: Optimized  
**Mobile**: Responsive Ready
