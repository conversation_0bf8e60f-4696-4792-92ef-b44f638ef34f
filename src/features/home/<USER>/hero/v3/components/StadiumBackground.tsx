"use client";

import React from "react";
import Image from "next/image";

export const StadiumBackground: React.FC = () => {
        return (
                <>
                        {/* Football Stadium Background with Enhanced Dynamic Elements */}
                        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900/80 to-slate-900 overflow-hidden">
                                {/* Football Field Green Base with Improved Texture */}
                                <div className="absolute inset-0 bg-gradient-to-br from-green-900/30 via-green-800/20 to-green-900/30 opacity-30"></div>

                                {/* Field Pattern Rotation */}
                                <div className="absolute inset-0 field-pattern-rotate opacity-5">
                                        <div className="absolute inset-0" style={{
                                                backgroundImage: 'repeating-linear-gradient(0deg, transparent, transparent 40px, rgba(255, 255, 255, 0.05) 40px, rgba(255, 255, 255, 0.05) 80px)',
                                                backgroundSize: '100% 80px'
                                        }}></div>
                                        <div className="absolute inset-0" style={{
                                                backgroundImage: 'repeating-linear-gradient(90deg, transparent, transparent 40px, rgba(255, 255, 255, 0.05) 40px, rgba(255, 255, 255, 0.05) 80px)',
                                                backgroundSize: '80px 100%'
                                        }}></div>
                                </div>

                                {/* Field Boundary Lines */}
                                <div className="absolute top-[30%] left-[8%] right-[8%] bottom-[30%] border-2 border-white/20 shadow-md"></div>

                                {/* Sidelines - More visible boundary */}
                                <div className="absolute top-[30%] left-[8%] right-[8%] h-0.5 bg-white/30 shadow-sm"></div>
                                <div className="absolute bottom-[30%] left-[8%] right-[8%] h-0.5 bg-white/30 shadow-sm"></div>
                                <div className="absolute top-[30%] bottom-[30%] left-[8%] w-0.5 bg-white/30 shadow-sm"></div>
                                <div className="absolute top-[30%] bottom-[30%] right-[8%] w-0.5 bg-white/30 shadow-sm"></div>

                                {/* Enhanced Football Field Markings */}
                                <div className="absolute inset-0">
                                        {/* Center Circle with Improved Visibility */}
                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 border-2 border-white/20 rounded-full"></div>
                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white/20 rounded-full"></div>

                                        {/* Center Line with Glow */}
                                        <div className="absolute top-1/2 left-0 right-0 h-1 bg-white/20 field-glow"></div>

                                        {/* Penalty Areas with Drop Shadow */}
                                        <div className="absolute top-1/2 left-[10%] -translate-y-1/2 w-[15%] h-[40%] border-2 border-white/20 shadow-lg"></div>
                                        <div className="absolute top-1/2 right-[10%] -translate-y-1/2 w-[15%] h-[40%] border-2 border-white/20 shadow-lg"></div>

                                        {/* Goal Areas with Improved Visibility */}
                                        <div className="absolute top-1/2 left-[10%] -translate-y-1/2 w-[5%] h-[20%] border-2 border-white/20"></div>
                                        <div className="absolute top-1/2 right-[10%] -translate-y-1/2 w-[5%] h-[20%] border-2 border-white/20"></div>

                                        {/* Penalty Spots with Glow Effect */}
                                        <div className="absolute top-1/2 left-[20%] -translate-y-1/2 w-3 h-3 bg-white/30 rounded-full shadow-white shadow-sm"></div>
                                        <div className="absolute top-1/2 right-[20%] -translate-y-1/2 w-3 h-3 bg-white/30 rounded-full shadow-white shadow-sm"></div>

                                        {/* Penalty Arcs */}
                                        <div className="absolute top-1/2 left-[22%] -translate-y-1/2 w-16 h-32 border-r-2 border-white/20 rounded-r-full"></div>
                                        <div className="absolute top-1/2 right-[22%] -translate-y-1/2 w-16 h-32 border-l-2 border-white/20 rounded-l-full"></div>

                                        {/* Corner Arcs with Improved Visibility */}
                                        <div className="absolute top-[30%] left-[8%] w-10 h-10 border-r-2 border-white/20 rounded-br-full"></div>
                                        <div className="absolute bottom-[30%] left-[8%] w-10 h-10 border-r-2 border-white/20 rounded-tr-full"></div>
                                        <div className="absolute top-[30%] right-[8%] w-10 h-10 border-l-2 border-white/20 rounded-bl-full"></div>
                                        <div className="absolute bottom-[30%] right-[8%] w-10 h-10 border-l-2 border-white/20 rounded-tl-full"></div>

                                        {/* Enhanced Field Decoration Lines */}
                                        <div className="absolute top-[35%] left-[30%] right-[30%] h-px bg-white/10"></div>
                                        <div className="absolute bottom-[35%] left-[30%] right-[30%] h-px bg-white/10"></div>

                                        {/* Player Shadows - Gives impression of players */}
                                        <div className="absolute top-[40%] left-[30%] w-3 h-8 bg-black/10 rounded-full blur-sm transform skew-x-12"></div>
                                        <div className="absolute top-[45%] left-[40%] w-3 h-8 bg-black/10 rounded-full blur-sm transform skew-x-12"></div>
                                        <div className="absolute top-[60%] left-[25%] w-3 h-8 bg-black/10 rounded-full blur-sm transform skew-x-12"></div>
                                        <div className="absolute top-[50%] left-[15%] w-3 h-8 bg-black/10 rounded-full blur-sm transform skew-x-12"></div>

                                        <div className="absolute top-[40%] right-[30%] w-3 h-8 bg-black/10 rounded-full blur-sm transform -skew-x-12"></div>
                                        <div className="absolute top-[45%] right-[40%] w-3 h-8 bg-black/10 rounded-full blur-sm transform -skew-x-12"></div>
                                        <div className="absolute top-[60%] right-[25%] w-3 h-8 bg-black/10 rounded-full blur-sm transform -skew-x-12"></div>
                                        <div className="absolute top-[50%] right-[15%] w-3 h-8 bg-black/10 rounded-full blur-sm transform -skew-x-12"></div>
                                </div>

                                {/* Enhanced Field Texture Pattern */}
                                <div className="absolute inset-0 opacity-10">
                                        {Array.from({ length: 30 }).map((_, i) => (
                                                <div
                                                        key={`horizontal-${i}`}
                                                        className="absolute left-0 right-0 h-px bg-green-100"
                                                        style={{
                                                                top: `${(i * 100) / 30}%`,
                                                                opacity: i % 2 === 0 ? 0.15 : 0.1
                                                        }}
                                                ></div>
                                        ))}
                                        {Array.from({ length: 15 }).map((_, i) => (
                                                <div
                                                        key={`vertical-${i}`}
                                                        className="absolute top-0 bottom-0 w-px bg-green-100"
                                                        style={{
                                                                left: `${(i * 100) / 15}%`,
                                                                opacity: i % 2 === 0 ? 0.15 : 0.1
                                                        }}
                                                ></div>
                                        ))}
                                </div>

                                {/* Enhanced Stadium Floodlights Glow */}
                                <div className="absolute -top-40 -left-40 w-96 h-96 bg-gradient-radial from-blue-500/30 to-transparent rounded-full blur-3xl gradient-pulse"></div>
                                <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-radial from-blue-500/30 to-transparent rounded-full blur-3xl gradient-pulse" style={{ animationDelay: '1.5s' }}></div>
                                <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-radial from-purple-500/30 to-transparent rounded-full blur-3xl gradient-pulse" style={{ animationDelay: '2.5s' }}></div>
                                <div className="absolute -bottom-40 -right-40 w-96 h-96 bg-gradient-radial from-purple-500/30 to-transparent rounded-full blur-3xl gradient-pulse" style={{ animationDelay: '3.5s' }}></div>

                                {/* Enhanced Stadium Atmosphere */}
                                <div className="absolute inset-0">
                                        {/* Stadium Spotlights with Improved Glow */}
                                        <div className="absolute top-0 left-1/4 w-2 h-60 bg-gradient-to-b from-blue-400/50 via-blue-300/30 to-transparent transform -rotate-15 stadium-light"></div>
                                        <div className="absolute top-0 right-1/4 w-2 h-60 bg-gradient-to-b from-blue-400/50 via-blue-300/30 to-transparent transform rotate-15 stadium-light" style={{ animationDelay: '0.5s' }}></div>
                                        <div className="absolute top-0 left-2/4 w-2 h-60 bg-gradient-to-b from-blue-400/50 via-blue-300/30 to-transparent transform -rotate-5 stadium-light" style={{ animationDelay: '0.8s' }}></div>
                                        <div className="absolute top-0 right-2/4 w-2 h-60 bg-gradient-to-b from-blue-400/50 via-blue-300/30 to-transparent transform rotate-5 stadium-light" style={{ animationDelay: '1.2s' }}></div>
                                        <div className="absolute top-0 left-3/4 w-2 h-60 bg-gradient-to-b from-blue-400/50 via-blue-300/30 to-transparent transform -rotate-15 stadium-light" style={{ animationDelay: '1.7s' }}></div>
                                        <div className="absolute top-0 right-3/4 w-2 h-60 bg-gradient-to-b from-blue-400/50 via-blue-300/30 to-transparent transform rotate-15 stadium-light" style={{ animationDelay: '2.1s' }}></div>
                                </div>
                        </div>

                        {/* Enhanced Football Field Elements */}
                        <div className="absolute inset-0 overflow-hidden pointer-events-none">
                                {/* Goal Posts with Improved 3D Effect */}
                                <div className="absolute top-1/2 left-[7.5%] -translate-y-1/2 w-1.5 h-[16%] bg-gradient-to-r from-white/80 to-white/40 shadow-lg"></div>
                                <div className="absolute top-[42%] left-[7.5%] w-[2.5%] h-1.5 bg-gradient-to-b from-white/80 to-white/40 shadow-lg"></div>
                                <div className="absolute top-1/2 right-[7.5%] -translate-y-1/2 w-1.5 h-[16%] bg-gradient-to-l from-white/80 to-white/40 shadow-lg"></div>
                                <div className="absolute top-[42%] right-[7.5%] w-[2.5%] h-1.5 bg-gradient-to-b from-white/80 to-white/40 shadow-lg"></div>

                                {/* Goal Nets */}
                                <div className="absolute top-[42%] left-[7.5%] w-[2.5%] h-[16%] border-r border-t border-white/10 bg-white/5"></div>
                                <div className="absolute top-[42%] right-[7.5%] w-[2.5%] h-[16%] border-l border-t border-white/10 bg-white/5"></div>

                                {/* Enhanced Soccer Balls with Realistic Design */}
                                <div className="absolute top-[18%] right-[18%] w-16 h-16 enhanced-ball">
                                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-white to-white/80 shadow-xl flex items-center justify-center">
                                                <div className="w-14 h-14 rounded-full border-2 border-gray-700/40 flex items-center justify-center">
                                                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-gray-800/20 to-gray-900/30 flex items-center justify-center">
                                                                <div className="absolute inset-0 w-full h-full">
                                                                        {/* Soccer Ball Pattern */}
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-gray-700/40 rotate-45"></div>
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-gray-700/40 -rotate-45"></div>
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-gray-700/40 rotate-[15deg]"></div>
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-gray-700/40 -rotate-[15deg]"></div>
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-gray-700/40 rotate-[75deg]"></div>
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-1 bg-gray-700/40 -rotate-[75deg]"></div>
                                                                </div>
                                                        </div>
                                                </div>
                                        </div>
                                </div>

                                <div className="absolute bottom-[22%] left-[12%] w-12 h-12 enhanced-ball" style={{ animationDelay: '2s' }}>
                                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-white to-white/70 shadow-xl flex items-center justify-center">
                                                <div className="w-10 h-10 rounded-full border-2 border-gray-700/40 flex items-center justify-center">
                                                        <div className="w-7 h-7 rounded-full bg-gradient-to-br from-gray-800/20 to-gray-900/30">
                                                                <div className="absolute inset-0">
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-0.5 bg-gray-700/40 rotate-45"></div>
                                                                        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-0.5 bg-gray-700/40 -rotate-45"></div>
                                                                </div>
                                                        </div>
                                                </div>
                                        </div>
                                </div>

                                {/* Additional Soccer Ball */}
                                <div className="absolute top-[65%] right-[25%] w-10 h-10 enhanced-ball" style={{ animationDelay: '3.5s' }}>
                                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-white to-white/70 shadow-xl flex items-center justify-center">
                                                <div className="w-8 h-8 rounded-full border-2 border-gray-700/40 flex items-center justify-center">
                                                        <div className="w-6 h-6 rounded-full bg-gradient-to-br from-gray-800/20 to-gray-900/30"></div>
                                                </div>
                                        </div>
                                </div>
                        </div>

                        {/* Enhanced Stadium Atmosphere Particles */}
                        <div className="absolute inset-0 overflow-hidden pointer-events-none">
                                {Array.from({ length: 60 }).map((_, i) => (
                                        <div
                                                key={i}
                                                className="absolute bg-white/30 rounded-full float-animation"
                                                style={{
                                                        width: `${Math.random() * 3 + 1}px`,
                                                        height: `${Math.random() * 3 + 1}px`,
                                                        left: `${Math.random() * 100}%`,
                                                        top: `${Math.random() * 100}%`,
                                                        animationDelay: `${Math.random() * 5}s`,
                                                        animationDuration: `${4 + Math.random() * 6}s`,
                                                        opacity: Math.random() * 0.5 + 0.1
                                                }}
                                        ></div>
                                ))}
                        </div>

                        {/* Enhanced Crowd Effect */}
                        <div className="absolute top-0 left-0 right-0 h-[35%] bg-gradient-to-b from-blue-900/20 via-blue-900/10 to-transparent overflow-hidden">
                                {/* Stadium Seats */}
                                <div className="absolute inset-0">
                                        {Array.from({ length: 8 }).map((_, i) => (
                                                <div
                                                        key={i}
                                                        className="absolute left-0 right-0 h-px bg-blue-400/10"
                                                        style={{ top: `${(i * 100) / 8}%` }}
                                                ></div>
                                        ))}
                                </div>

                                {/* Camera Flashes - Simulate spectators taking photos */}
                                {Array.from({ length: 8 }).map((_, i) => (
                                        <div
                                                key={`flash-${i}`}
                                                className="absolute rounded-full bg-white/60 camera-flash"
                                                style={{
                                                        width: `${Math.random() * 3 + 2}px`,
                                                        height: `${Math.random() * 3 + 2}px`,
                                                        left: `${Math.random() * 100}%`,
                                                        top: `${Math.random() * 100}%`,
                                                        animationDelay: `${Math.random() * 10}s`,
                                                        opacity: 0
                                                }}
                                        ></div>
                                ))}

                                {/* Crowd Simulation */}
                                {Array.from({ length: 100 }).map((_, i) => (
                                        <div
                                                key={i}
                                                className="absolute rounded-full"
                                                style={{
                                                        width: Math.random() < 0.3 ? '2px' : '1px',
                                                        height: Math.random() < 0.3 ? '3px' : '2px',
                                                        backgroundColor: Math.random() < 0.5 ? 'rgba(255, 255, 255, 0.15)' : 'rgba(59, 130, 246, 0.15)',
                                                        left: `${Math.random() * 100}%`,
                                                        top: `${Math.random() * 100}%`,
                                                        animation: `crowdMove ${1 + Math.random() * 2}s ease-in-out infinite alternate`,
                                                        animationDelay: `${Math.random() * 2}s`
                                                }}
                                        ></div>
                                ))}
                        </div>

                        {/* Stadium Floodlight Effect */}
                        <div className="absolute inset-0">
                                <div className="absolute top-0 left-1/4 w-48 h-48 bg-gradient-radial from-blue-400/10 to-transparent rounded-full blur-2xl"></div>
                                <div className="absolute top-0 right-1/4 w-48 h-48 bg-gradient-radial from-blue-400/10 to-transparent rounded-full blur-2xl"></div>
                        </div>

                        {/* Dynamic Stadium Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-slate-900/70 via-slate-900/30 to-transparent"></div>
                </>
        );
};

// Stadium floodlights with enhanced effects
export const StadiumFloodlights: React.FC = () => {
        return (
                <div className="absolute inset-0 pointer-events-none overflow-hidden">
                        {/* Stadium Corner Lights with Enhanced Glow */}
                        <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-radial from-blue-400/10 to-transparent"></div>
                        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-radial from-blue-400/10 to-transparent"></div>
                        <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-radial from-blue-400/10 to-transparent"></div>
                        <div className="absolute bottom-0 right-0 w-1/3 h-1/3 bg-gradient-radial from-blue-400/10 to-transparent"></div>

                        {/* Enhanced Stadium Light Beams */}
                        {Array.from({ length: 12 }).map((_, i) => (
                                <div
                                        key={i}
                                        className="absolute w-2 h-48 bg-gradient-to-b from-blue-400/10 via-blue-300/5 to-transparent stadium-light"
                                        style={{
                                                left: `${8 + (i * 7)}%`,
                                                top: 0,
                                                transform: `translateY(-20%) rotate(${85 + Math.sin(i) * 20}deg)`,
                                                animationDelay: `${i * 0.2}s`
                                        }}
                                ></div>
                        ))}

                        {/* Stadium Spotlights */}
                        <div className="absolute top-0 left-[15%] w-24 h-24 bg-gradient-radial from-blue-400/5 to-transparent rounded-full blur-xl"></div>
                        <div className="absolute top-0 right-[15%] w-24 h-24 bg-gradient-radial from-blue-400/5 to-transparent rounded-full blur-xl"></div>
                        <div className="absolute top-0 left-[40%] w-24 h-24 bg-gradient-radial from-blue-400/5 to-transparent rounded-full blur-xl"></div>
                        <div className="absolute top-0 right-[40%] w-24 h-24 bg-gradient-radial from-blue-400/5 to-transparent rounded-full blur-xl"></div>

                        {/* Additional Light Effects */}
                        <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-blue-900/15"></div>
                </div>
        );
};
