"use client";

import React, { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { buildImageUrl, handleImageError } from "@/shared/utils/imageUtils";

interface MatchData {
        id: number;
        homeTeam: string;
        awayTeam: string;
        homeLogo: string;
        awayLogo: string;
        date: string;
        time: string;
        venue: string;
        league: string;
        importance: string;
        isNextMatch: boolean;
        weather: string;
        // New fields for watch URL generation
        slug?: string;           // URL-friendly match identifier
        fixtureId?: string;      // Official fixture ID
}

interface CountdownTime {
        hours: number;
        minutes: number;
        seconds: number;
}

interface MultiMatchDisplayCardProps {
        matches: MatchData[];
        countdown: CountdownTime;
        displayMode?: 'grid' | 'carousel' | 'stack';
        maxDisplayed?: number;
}

// Navigation utility functions
const generateSlug = (homeTeam: string, awayTeam: string): string => {
        const slugText = `${homeTeam}-vs-${awayTeam}`
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
        return slugText;
};

const generateWatchUrl = (match: MatchData): string => {
        const slug = match.slug || generateSlug(match.homeTeam, match.awayTeam);
        const fixtureId = match.fixtureId || `fixture-${match.id}`;
        return `/watch/${slug}/${fixtureId}`;
};

// Single Match Component để reuse
const SingleMatchDisplay: React.FC<{ match: MatchData; countdown: CountdownTime; isMainMatch?: boolean; router: any }> = ({
        match,
        countdown,
        isMainMatch = false,
        router
}) => {
        // Handle watch button click
        const handleWatchClick = () => {
                const watchUrl = generateWatchUrl(match);
                router.push(watchUrl);
        };
        return (
                <div className={`relative ${isMainMatch ? 'mb-6' : 'mb-4'}`}>
                        {/* Match Background Glow */}
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-purple-600/10 rounded-2xl blur-xl"></div>

                        <div className={`relative bg-gradient-to-r from-gray-800/50 to-gray-900/50 rounded-2xl ${isMainMatch ? 'p-6' : 'p-4'} border border-gray-600/30`}>
                                {/* Teams Display */}
                                <div className="flex items-center justify-between mb-4">
                                        {/* Home Team */}
                                        <div className="flex items-center space-x-3 flex-1">
                                                <div className="relative">
                                                        <Image
                                                                src={buildImageUrl(match.homeLogo)}
                                                                alt={`${match.homeTeam} logo`}
                                                                width={isMainMatch ? 64 : 48}
                                                                height={isMainMatch ? 64 : 48}
                                                                className="rounded-full border-2 border-white/20"
                                                                onError={(e) => handleImageError(e, match.homeTeam)}
                                                                unoptimized={true}
                                                        />
                                                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                                                </div>
                                                <div>
                                                        <h3 className={`${isMainMatch ? 'text-xl md:text-2xl' : 'text-lg'} font-bold text-white vietnamese-text`}>
                                                                {match.homeTeam}
                                                        </h3>
                                                        <p className="text-gray-400 text-sm">Home</p>
                                                </div>
                                        </div>

                                        {/* VS Section */}
                                        <div className="text-center px-4 flex-shrink-0">
                                                <div className={`${isMainMatch ? 'w-12 h-12' : 'w-10 h-10'} bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-2 shadow-lg shadow-purple-500/30 mx-auto`}>
                                                        <span className="text-white font-bold text-sm">VS</span>
                                                </div>
                                                <p className="text-gray-300 text-xs font-semibold">{match.time}</p>
                                        </div>

                                        {/* Away Team */}
                                        <div className="flex items-center justify-end space-x-3 flex-1">
                                                <div className="text-right">
                                                        <h3 className={`${isMainMatch ? 'text-xl md:text-2xl' : 'text-lg'} font-bold text-white vietnamese-text`}>
                                                                {match.awayTeam}
                                                        </h3>
                                                        <p className="text-gray-400 text-sm">Away</p>
                                                </div>
                                                <div className="relative">
                                                        <Image
                                                                src={buildImageUrl(match.awayLogo)}
                                                                alt={`${match.awayTeam} logo`}
                                                                width={isMainMatch ? 64 : 48}
                                                                height={isMainMatch ? 64 : 48}
                                                                className="rounded-full border-2 border-white/20"
                                                                onError={(e) => handleImageError(e, match.awayTeam)}
                                                                unoptimized={true}
                                                        />
                                                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                                                </div>
                                        </div>
                                </div>

                                {/* Match Details - Chỉ hiển thị cho main match */}
                                {isMainMatch && (
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-center mb-4">
                                                <div className="bg-black/30 rounded-lg p-2 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">Venue</p>
                                                        <p className="text-white font-semibold text-sm">{match.venue}</p>
                                                </div>
                                                <div className="bg-black/30 rounded-lg p-2 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">League</p>
                                                        <p className="text-white font-semibold text-sm">{match.league}</p>
                                                </div>
                                                <div className="bg-black/30 rounded-lg p-2 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">Weather</p>
                                                        <p className="text-white font-semibold text-sm">{match.weather}</p>
                                                </div>
                                                <div className="bg-black/30 rounded-lg p-2 border border-gray-600/30">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">Status</p>
                                                        <div className="flex items-center justify-center space-x-1">
                                                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                                                <p className="text-green-400 font-semibold text-sm">Ready</p>
                                                        </div>
                                                </div>
                                        </div>
                                )}

                                {/* Mini details cho secondary matches */}
                                {!isMainMatch && (
                                        <div className="flex justify-between items-center text-xs text-gray-400 mt-3">
                                                <span>{match.venue}</span>
                                                <span>{match.league}</span>
                                                <span>{match.date}</span>
                                        </div>
                                )}

                                {/* Individual Watch Button */}
                                <div className="mt-4 text-center">
                                        <button
                                                onClick={handleWatchClick}
                                                className={`${isMainMatch
                                                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 py-2 px-6'
                                                        : 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 py-1.5 px-4'
                                                        } text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105`}
                                        >
                                                <span className="flex items-center space-x-2">
                                                        <svg className={`${isMainMatch ? 'w-4 h-4' : 'w-3 h-3'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 002 2v8a2 2 0 002 2z" />
                                                        </svg>
                                                        <span className={isMainMatch ? 'text-sm' : 'text-xs'}>Xem</span>
                                                </span>
                                        </button>

                                        {/* Debug info for individual matches */}
                                        {isMainMatch && (
                                                <div className="mt-2 text-xs text-gray-500">
                                                        <p>📺 {generateWatchUrl(match)}</p>
                                                </div>
                                        )}
                                </div>
                        </div>
                </div>
        );
};

export const MultiMatchDisplayCard: React.FC<MultiMatchDisplayCardProps> = ({
        matches,
        countdown,
        displayMode = 'stack',
        maxDisplayed = 3
}) => {
        const router = useRouter();
        const [currentMatchIndex, setCurrentMatchIndex] = useState(0);
        const [isAutoRotating, setIsAutoRotating] = useState(true);

        if (!matches || matches.length === 0) {
                return null;
        }

        const displayedMatches = matches.slice(0, maxDisplayed);
        const mainMatch = displayedMatches[0];
        const secondaryMatches = displayedMatches.slice(1);

        // Navigation handlers
        const handleMainWatchClick = () => {
                // Navigate to the main match (first match or current match in carousel)
                const targetMatch = displayMode === 'carousel'
                        ? displayedMatches[currentMatchIndex]
                        : mainMatch;
                const watchUrl = generateWatchUrl(targetMatch);
                router.push(watchUrl);
        };

        const handleViewAllMatches = () => {
                // This could navigate to a matches overview page
                // For now, navigate to the first match
                const watchUrl = generateWatchUrl(mainMatch);
                router.push(watchUrl);
        };

        // Auto-rotation cho carousel mode
        React.useEffect(() => {
                if (displayMode === 'carousel' && isAutoRotating && displayedMatches.length > 1) {
                        const interval = setInterval(() => {
                                setCurrentMatchIndex((prev) =>
                                        prev < displayedMatches.length - 1 ? prev + 1 : 0
                                );
                        }, 5000); // 5 seconds per match

                        return () => clearInterval(interval);
                }
        }, [displayMode, isAutoRotating, displayedMatches.length]);

        // Enhanced keyboard navigation với advanced shortcuts
        React.useEffect(() => {
                const handleKeyDown = (event: KeyboardEvent) => {
                        if (displayMode === 'carousel' && displayedMatches.length > 1) {
                                switch (event.key) {
                                        case 'ArrowLeft':
                                                event.preventDefault();
                                                setIsAutoRotating(false);
                                                setCurrentMatchIndex((prev) =>
                                                        prev > 0 ? prev - 1 : displayedMatches.length - 1
                                                );
                                                break;
                                        case 'ArrowRight':
                                                event.preventDefault();
                                                setIsAutoRotating(false);
                                                setCurrentMatchIndex((prev) =>
                                                        prev < displayedMatches.length - 1 ? prev + 1 : 0
                                                );
                                                break;
                                        case ' ': // Space to toggle auto-rotation
                                                event.preventDefault();
                                                setIsAutoRotating(prev => !prev);
                                                break;
                                        case 'Home': // Go to first match
                                                event.preventDefault();
                                                setIsAutoRotating(false);
                                                setCurrentMatchIndex(0);
                                                break;
                                        case 'End': // Go to last match
                                                event.preventDefault();
                                                setIsAutoRotating(false);
                                                setCurrentMatchIndex(displayedMatches.length - 1);
                                                break;
                                        case 'Escape': // Stop auto-rotation
                                                event.preventDefault();
                                                setIsAutoRotating(false);
                                                break;
                                        default:
                                                // Number keys 1-9 để jump to specific match
                                                const num = parseInt(event.key);
                                                if (num >= 1 && num <= displayedMatches.length) {
                                                        event.preventDefault();
                                                        setIsAutoRotating(false);
                                                        setCurrentMatchIndex(num - 1);
                                                }
                                                break;
                                }
                        }
                };

                window.addEventListener('keydown', handleKeyDown);
                return () => window.removeEventListener('keydown', handleKeyDown);
        }, [displayMode, displayedMatches.length]);

        const renderGridMode = () => (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {displayedMatches.map((match, index) => (
                                <SingleMatchDisplay
                                        key={match.id}
                                        match={match}
                                        countdown={countdown}
                                        isMainMatch={index === 0}
                                        router={router}
                                />
                        ))}
                </div>
        );

        const renderCarouselMode = () => (
                <div className="space-y-4">
                        <div className="flex justify-between items-center mb-4">
                                <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center space-x-3">
                                        <span className="live-indicator w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                                🔥
                                        </span>
                                        <span>Trận Đấu Hôm Nay ({displayedMatches.length})</span>
                                </h2>
                                <div className="flex items-center space-x-4">
                                        {/* Auto-rotation Controls */}
                                        <div className="flex items-center space-x-2">
                                                <button
                                                        onClick={() => setIsAutoRotating(!isAutoRotating)}
                                                        className={`p-2 rounded-lg transition-all duration-300 ${isAutoRotating
                                                                ? 'bg-green-600/20 text-green-400 border border-green-600/30'
                                                                : 'bg-gray-600/20 text-gray-400 border border-gray-600/30'
                                                                } hover:scale-105`}
                                                        title={isAutoRotating ? 'Pause auto-rotation (Space)' : 'Start auto-rotation (Space)'}
                                                >
                                                        {isAutoRotating ? (
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
                                                                </svg>
                                                        ) : (
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h1m4 0h1" />
                                                                </svg>
                                                        )}
                                                </button>
                                                <span className="text-xs text-gray-400">
                                                        {isAutoRotating ? 'Auto' : 'Manual'}
                                                </span>
                                        </div>

                                        {/* Countdown Timer */}
                                        <div className="text-right">
                                                <p className="text-gray-400 text-sm">Countdown</p>
                                                <div className="flex space-x-2 text-white font-mono font-bold text-xl">
                                                        <span>{countdown.hours.toString().padStart(2, '0')}</span>
                                                        <span className="text-gray-500">:</span>
                                                        <span>{countdown.minutes.toString().padStart(2, '0')}</span>
                                                        <span className="text-gray-500">:</span>
                                                        <span>{countdown.seconds.toString().padStart(2, '0')}</span>
                                                </div>
                                        </div>
                                </div>
                        </div>

                        {/* Main Match Display với animation và navigation controls */}
                        <div className="relative overflow-hidden rounded-2xl">
                                {/* Navigation Arrows */}
                                {displayedMatches.length > 1 && (
                                        <>
                                                <button
                                                        onClick={() => {
                                                                setIsAutoRotating(false);
                                                                setCurrentMatchIndex(prev =>
                                                                        prev > 0 ? prev - 1 : displayedMatches.length - 1
                                                                );
                                                        }}
                                                        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all duration-300 hover:scale-110 backdrop-blur-sm border border-white/10"
                                                        title="Previous match (Left Arrow)"
                                                >
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                                        </svg>
                                                </button>
                                                <button
                                                        onClick={() => {
                                                                setIsAutoRotating(false);
                                                                setCurrentMatchIndex(prev =>
                                                                        prev < displayedMatches.length - 1 ? prev + 1 : 0
                                                                );
                                                        }}
                                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all duration-300 hover:scale-110 backdrop-blur-sm border border-white/10"
                                                        title="Next match (Right Arrow)"
                                                >
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                        </svg>
                                                </button>
                                        </>
                                )}

                                {/* Slides Container */}
                                <div
                                        className="transition-transform duration-500 ease-in-out"
                                        style={{ transform: `translateX(-${currentMatchIndex * 100}%)` }}
                                >
                                        <div className="flex">
                                                {displayedMatches.map((match, index) => (
                                                        <div key={match.id} className="w-full flex-shrink-0">
                                                                <SingleMatchDisplay
                                                                        match={match}
                                                                        countdown={countdown}
                                                                        isMainMatch={true}
                                                                        router={router}
                                                                />
                                                        </div>
                                                ))}
                                        </div>
                                </div>

                                {/* Match Info Overlay */}
                                <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/10">
                                        <div className="text-white text-sm font-medium">
                                                {currentMatchIndex + 1} / {displayedMatches.length}
                                        </div>
                                </div>
                        </div>

                        {/* Enhanced Match Navigation & Controls */}
                        {displayedMatches.length > 1 && (
                                <div className="flex flex-col space-y-4">
                                        {/* Dot Navigation */}
                                        <div className="flex justify-center space-x-2">
                                                {displayedMatches.map((_, index) => (
                                                        <button
                                                                key={index}
                                                                onClick={() => {
                                                                        setIsAutoRotating(false);
                                                                        setCurrentMatchIndex(index);
                                                                }}
                                                                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentMatchIndex
                                                                        ? 'bg-purple-500 scale-125 shadow-lg shadow-purple-500/50'
                                                                        : 'bg-gray-500 hover:bg-gray-400 hover:scale-110'
                                                                        }`}
                                                                title={`Go to match ${index + 1}`}
                                                        />
                                                ))}
                                        </div>

                                        {/* Progress Bar for Auto-rotation */}
                                        {isAutoRotating && (
                                                <div className="w-full max-w-md mx-auto">
                                                        <div className="h-1 bg-gray-700 rounded-full overflow-hidden relative">
                                                                <div
                                                                        key={currentMatchIndex} // Force re-render để restart animation
                                                                        className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"
                                                                        style={{
                                                                                width: '100%',
                                                                                animation: 'pulse 5s linear infinite'
                                                                        }}
                                                                ></div>
                                                        </div>
                                                        <p className="text-center text-xs text-gray-400 mt-1">
                                                                Auto-advancing to next match...
                                                        </p>
                                                </div>
                                        )}

                                        {/* Enhanced Keyboard Shortcuts Hint */}
                                        <div className="text-center text-xs text-gray-500 space-y-1">
                                                <div className="hidden md:block">
                                                        <p>🎮 <strong>Shortcuts:</strong> ← → (Navigate) • Space (Play/Pause) • 1-{displayedMatches.length} (Jump) • Home/End (First/Last) • Esc (Stop)</p>
                                                </div>
                                                <div className="md:hidden">
                                                        <p>Tap arrows, dots, or use number keys • {displayedMatches.length} matches total</p>
                                                </div>
                                        </div>
                                </div>
                        )}

                        {/* Action Button */}
                        <div className="text-center">
                                <button
                                        onClick={handleMainWatchClick}
                                        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/30"
                                >
                                        <span className="flex items-center space-x-2">
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                <span>Xem Trực Tiếp</span>
                                        </span>
                                </button>

                                {/* Debug info - Current match URL preview */}
                                <div className="mt-2 text-xs text-gray-500">
                                        <p>📺 {generateWatchUrl(displayedMatches[currentMatchIndex])}</p>
                                </div>
                        </div>
                </div>
        );

        const renderStackMode = () => (
                <div className="space-y-4">
                        <div className="flex items-center justify-between mb-6">
                                <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center space-x-3">
                                        <span className="live-indicator w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                                🔥
                                        </span>
                                        <span>Trận Đấu Hôm Nay ({displayedMatches.length})</span>
                                </h2>
                                <div className="text-right">
                                        <p className="text-gray-400 text-sm">Countdown</p>
                                        <div className="flex space-x-2 text-white font-mono font-bold text-xl">
                                                <span>{countdown.hours.toString().padStart(2, '0')}</span>
                                                <span className="text-gray-500">:</span>
                                                <span>{countdown.minutes.toString().padStart(2, '0')}</span>
                                                <span className="text-gray-500">:</span>
                                                <span>{countdown.seconds.toString().padStart(2, '0')}</span>
                                        </div>
                                </div>
                        </div>

                        {/* Main Match */}
                        <SingleMatchDisplay
                                match={mainMatch}
                                countdown={countdown}
                                isMainMatch={true}
                                router={router}
                        />

                        {/* Secondary Matches */}
                        {secondaryMatches.length > 0 && (
                                <div className="space-y-3">
                                        <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                                                <span className="w-6 h-6 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full flex items-center justify-center text-white text-xs">
                                                        +
                                                </span>
                                                <span>Trận Tiếp Theo</span>
                                        </h3>
                                        {secondaryMatches.map((match) => (
                                                <SingleMatchDisplay
                                                        key={match.id}
                                                        match={match}
                                                        countdown={countdown}
                                                        isMainMatch={false}
                                                        router={router}
                                                />
                                        ))}
                                </div>
                        )}

                        {/* Show more indicator */}
                        {matches.length > maxDisplayed && (
                                <div className="text-center">
                                        <p className="text-gray-400 text-sm">
                                                +{matches.length - maxDisplayed} trận đấu khác
                                        </p>
                                </div>
                        )}

                        {/* Action Button */}
                        <div className="text-center">
                                <button
                                        onClick={handleViewAllMatches}
                                        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/30"
                                >
                                        <span className="flex items-center space-x-2">
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                <span>Xem Tất Cả Trận Đấu</span>
                                        </span>
                                </button>

                                {/* Debug info - URL preview */}
                                <div className="mt-2 text-xs text-gray-500">
                                        <p>📺 {generateWatchUrl(mainMatch)}</p>
                                </div>
                        </div>
                </div>
        );

        return (
                <div className="match-card bg-gradient-to-br from-gray-900/80 to-slate-800/60 backdrop-blur-lg rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
                        {displayMode === 'grid' && renderGridMode()}
                        {displayMode === 'carousel' && renderCarouselMode()}
                        {displayMode === 'stack' && renderStackMode()}
                </div>
        );
};
