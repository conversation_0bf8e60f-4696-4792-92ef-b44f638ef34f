"use client";

import React from "react";

interface League {
        name: string;
        color: string;
        accent: string;
        icon: string;
        gradient: string;
}

interface LeagueSelectorProps {
        leagues: Record<string, League>;
        activeLeague: 'premier' | 'champions' | 'laliga' | 'serie' | 'bundesliga';
        onLeagueChange: (league: 'premier' | 'champions' | 'laliga' | 'serie' | 'bundesliga') => void;
}

export const LeagueSelector: React.FC<LeagueSelectorProps> = ({
        leagues,
        activeLeague,
        onLeagueChange
}) => {
        return (
                <div className="mb-12">
                        <div className="flex justify-center">
                                <div
                                        className="flex space-x-2 bg-black/40 backdrop-blur-lg rounded-2xl p-2 border border-gray-700/50"
                                        role="tablist"
                                        aria-label="Select football league"
                                >
                                        {Object.entries(leagues).map(([key, league]) => (
                                                <button
                                                        key={key}
                                                        onClick={() => onLeagueChange(key as 'premier' | 'champions' | 'laliga' | 'serie' | 'bundesliga')}
                                                        className={`league-tab flex items-center space-x-2 px-4 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${activeLeague === key
                                                                ? `bg-gradient-to-r ${league.color} text-white shadow-lg shadow-${league.color.split('-')[1]}-500/30`
                                                                : 'text-gray-400 hover:text-white hover:bg-white/10'
                                                                }`}
                                                        role="tab"
                                                        aria-selected={activeLeague === key}
                                                        aria-label={`Select ${league.name} matches`}
                                                        tabIndex={activeLeague === key ? 0 : -1}
                                                >
                                                        <span className="text-lg" aria-hidden="true">{league.icon}</span>
                                                        <span className="text-sm font-bold hidden md:inline">{league.name}</span>
                                                </button>
                                        ))}
                                </div>
                        </div>
                </div>
        );
};
