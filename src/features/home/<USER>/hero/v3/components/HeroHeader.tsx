"use client";

import React from "react";

interface HeroHeaderProps {
        currentTime: Date;
}

export const HeroHeader: React.FC<HeroHeaderProps> = ({ currentTime }) => {
        return (
                <div className="text-center mb-16">
                        <div className="flex items-center justify-center space-x-4 mb-6" role="status" aria-live="polite">
                                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" aria-hidden="true"></div>
                                <span className="text-red-400 font-bold text-lg uppercase tracking-wider">Live Championship</span>
                                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" aria-hidden="true"></div>
                        </div>

                        <h2 className="championship-title text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-blue-200 to-purple-300 mb-4 vietnamese-title leading-tight">
                                Sân Vận Động
                        </h2>

                        <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto vietnamese-text">
                                Theo dõi trực tiếp các trận đấu hàng đầu thế giới ngay tại sân cỏ với thông tin chi tiết và cập nhật theo thời gian thực
                        </p>

                        {/* Digital Stadium Clock */}
                        <div
                                className="countdown-container inline-flex items-center space-x-6 bg-black/60 backdrop-blur-lg rounded-2xl px-8 py-4 border border-gray-700/50"
                                role="timer"
                                aria-label="Stadium clock showing current time and date"
                        >
                                <div className="text-center">
                                        <p className="text-gray-400 text-sm uppercase tracking-wider">Stadium Time</p>
                                        <time
                                                className="digital-clock text-2xl md:text-3xl font-mono font-bold text-white"
                                                dateTime={currentTime.toISOString()}
                                                aria-label={`Current time: ${currentTime.toLocaleTimeString('vi-VN')}`}
                                        >
                                                {currentTime.toLocaleTimeString('vi-VN', {
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                        second: '2-digit'
                                                })}
                                        </time>
                                </div>
                                <div className="w-px h-12 bg-gray-600" aria-hidden="true"></div>
                                <div className="text-center">
                                        <p className="text-gray-400 text-sm uppercase tracking-wider">Date</p>
                                        <time
                                                className="text-lg md:text-xl font-semibold text-white"
                                                dateTime={currentTime.toISOString().split('T')[0]}
                                                aria-label={`Current date: ${currentTime.toLocaleDateString('vi-VN')}`}
                                        >
                                                {currentTime.toLocaleDateString('vi-VN', {
                                                        weekday: 'short',
                                                        month: 'short',
                                                        day: 'numeric'
                                                })}
                                        </time>
                                </div>
                        </div>
                </div>
        );
};
