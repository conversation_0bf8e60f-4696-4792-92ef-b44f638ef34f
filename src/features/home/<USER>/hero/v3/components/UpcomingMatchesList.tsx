"use client";

import React from "react";
import Image from "next/image";

interface MatchData {
        id: number;
        homeTeam: string;
        awayTeam: string;
        homeLogo: string;
        awayLogo: string;
        date: string;
        time: string;
        venue: string;
        league: string;
        importance: string;
        isNextMatch: boolean;
        weather: string;
}

interface UpcomingMatchesListProps {
        upcomingMatches: MatchData[];
        hasMatches: boolean;
}

export const UpcomingMatchesList: React.FC<UpcomingMatchesListProps> = ({
        upcomingMatches,
        hasMatches
}) => {
        return (
                <div className="bg-gradient-to-br from-gray-900/80 to-slate-800/60 backdrop-blur-lg rounded-3xl p-6 border border-gray-700/30">
                        <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                                <span className="text-2xl">📅</span>
                                <span>Upcoming Matches</span>
                        </h3>

                        <div className="space-y-4">
                                {hasMatches ? upcomingMatches.slice(1).map((match) => (
                                        <div key={match.id} className="bg-black/40 rounded-xl p-4 border border-gray-600/30 hover:bg-black/60 transition-all duration-300 cursor-pointer">
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="flex -space-x-2">
                                                                        <Image
                                                                                src={match.homeLogo}
                                                                                alt={`${match.homeTeam} logo`}
                                                                                width={32}
                                                                                height={32}
                                                                                className="rounded-full border-2 border-gray-700"
                                                                        />
                                                                        <Image
                                                                                src={match.awayLogo}
                                                                                alt={`${match.awayTeam} logo`}
                                                                                width={32}
                                                                                height={32}
                                                                                className="rounded-full border-2 border-gray-700"
                                                                        />
                                                                </div>
                                                                <div>
                                                                        <p className="text-white font-semibold text-sm">{match.homeTeam} vs {match.awayTeam}</p>
                                                                        <p className="text-gray-400 text-xs">{match.league}</p>
                                                                </div>
                                                        </div>
                                                        <div className="text-right">
                                                                <p className="text-white font-bold text-sm">{match.time}</p>
                                                                <p className="text-gray-400 text-xs">{match.date}</p>
                                                        </div>
                                                </div>
                                        </div>
                                )) : (
                                        <div className="text-center py-4">
                                                <p className="text-gray-400 text-sm">Không có trận đấu nào sắp diễn ra</p>
                                        </div>
                                )}
                        </div>
                </div>
        );
};
