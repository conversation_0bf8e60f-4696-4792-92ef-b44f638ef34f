"use client";

import React from "react";

interface Statistic {
        label: string;
        value: string;
        trend: 'up' | 'down' | 'neutral';
}

interface StatisticsPanelProps {
        liveStats: Statistic[];
}

export const StatisticsPanel: React.FC<StatisticsPanelProps> = ({ liveStats }) => {
        return (
                <div className="bg-gradient-to-br from-gray-900/80 to-slate-800/60 backdrop-blur-lg rounded-3xl p-6 border border-gray-700/30">
                        <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                                <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                </div>
                                <span>Live Stats</span>
                        </h3>

                        <div className="grid grid-cols-2 gap-4">
                                {liveStats.map((stat, index) => (
                                        <div key={index} className="bg-black/40 rounded-xl p-4 border border-gray-600/30">
                                                <div className="flex items-center justify-between mb-2">
                                                        <p className="text-gray-400 text-xs uppercase tracking-wider">{stat.label}</p>
                                                        {stat.trend !== 'neutral' && (
                                                                <div className={`flex items-center space-x-1 ${stat.trend === 'up' ? 'text-green-400' : 'text-red-400'
                                                                        }`}>
                                                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                                                                        d={stat.trend === 'up' ? "M7 11l5-5m0 0l5 5m-5-5v12" : "M17 13l-5 5m0 0l-5-5m5 5V6"}
                                                                                />
                                                                        </svg>
                                                                </div>
                                                        )}
                                                        {stat.trend === 'neutral' && (
                                                                <div className="flex items-center space-x-1 text-gray-400 opacity-70">
                                                                        <div className="w-3 h-3 rounded-full bg-gray-500/50"></div>
                                                                </div>
                                                        )}
                                                </div>
                                                <p className="text-white font-bold text-lg">{stat.value}</p>
                                        </div>
                                ))}
                        </div>
                </div>
        );
};
