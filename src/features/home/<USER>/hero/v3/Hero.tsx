// Hero.tsx (v3) - Championship Stadium Experience with Match Schedule Focus & Empty State (Refactored)
"use client";

import React, { useState, useEffect } from "react";
import './styles/hero-v3.css';
import { useMobileDetection, useDeviceCapabilities } from '@/shared/hooks/useMobileDetection';
import { PerformanceAwareAnimation } from '@/shared/components/mobile/PerformanceAwareAnimation';
import {
        EmptyStateCard,
        MatchDisplayCard,
        MultiMatchDisplayCard,
        StadiumBackground,
        StadiumFloodlights,
        HeroHeader,
        LeagueSelector,
        StatisticsPanel,
        UpcomingMatchesList
} from './components';

// Types
interface MatchData {
        id: number;
        homeTeam: string;
        awayTeam: string;
        homeLogo: string;
        awayLogo: string;
        date: string;
        time: string;
        venue: string;
        league: string;
        importance: string;
        isNextMatch: boolean;
        weather: string;
        // New fields for watch URL generation
        slug?: string;           // URL-friendly match identifier
        fixtureId?: string;      // Official fixture ID
}

interface Statistic {
        label: string;
        value: string;
        trend: 'up' | 'down' | 'neutral';
}

const Hero: React.FC = () => {
        const [currentTime, setCurrentTime] = useState(new Date());
        const [activeLeague, setActiveLeague] = useState<'premier' | 'champions' | 'laliga' | 'serie' | 'bundesliga'>('premier');
        const [countdown, setCountdown] = useState({ hours: 0, minutes: 0, seconds: 0 });
        const [hasMatches, setHasMatches] = useState(true); // Toggle để test empty state
        const [displayMode, setDisplayMode] = useState<'single' | 'multiple'>('single'); // New: Toggle display mode
        const [multiDisplayMode, setMultiDisplayMode] = useState<'stack' | 'carousel' | 'grid'>('stack'); // New: Multi-display mode

        // Mobile detection and performance optimization
        const { isMobile, isTablet } = useMobileDetection();
        const { prefersReducedMotion, connectionSpeed } = useDeviceCapabilities();

        // Function để toggle giữa có/không có trận đấu (for testing)
        const toggleMatchesAvailability = () => {
                setHasMatches(!hasMatches);
        };

        // Function để toggle giữa single và multiple display mode
        const toggleDisplayMode = () => {
                setDisplayMode(displayMode === 'single' ? 'multiple' : 'single');
        };

        useEffect(() => {
                const timer = setInterval(() => {
                        setCurrentTime(new Date());
                }, 1000);
                return () => clearInterval(timer);
        }, []);

        // Countdown to next big match
        useEffect(() => {
                const nextMatch = new Date();
                nextMatch.setHours(nextMatch.getHours() + 2, 30, 0, 0); // Next match in 2.5 hours

                const timer = setInterval(() => {
                        const now = new Date();
                        const difference = nextMatch.getTime() - now.getTime();

                        const hours = Math.floor(difference / (1000 * 60 * 60));
                        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

                        setCountdown({ hours, minutes, seconds });
                }, 1000);

                return () => clearInterval(timer);
        }, []);

        const leagues = {
                premier: {
                        name: "Premier League",
                        color: "from-purple-600 to-blue-700",
                        accent: "text-purple-300",
                        icon: "👑",
                        gradient: "bg-gradient-to-r from-purple-600/20 to-blue-600/20"
                },
                champions: {
                        name: "Champions League",
                        color: "from-blue-600 to-indigo-800",
                        accent: "text-blue-300",
                        icon: "⭐",
                        gradient: "bg-gradient-to-r from-blue-600/20 to-indigo-600/20"
                },
                laliga: {
                        name: "La Liga",
                        color: "from-red-600 to-orange-700",
                        accent: "text-red-300",
                        icon: "🏆",
                        gradient: "bg-gradient-to-r from-red-600/20 to-orange-600/20"
                },
                serie: {
                        name: "Serie A",
                        color: "from-green-600 to-emerald-700",
                        accent: "text-green-300",
                        icon: "🇮🇹",
                        gradient: "bg-gradient-to-r from-green-600/20 to-emerald-600/20"
                },
                bundesliga: {
                        name: "Bundesliga",
                        color: "from-yellow-600 to-amber-700",
                        accent: "text-yellow-300",
                        icon: "🦅",
                        gradient: "bg-gradient-to-r from-yellow-600/20 to-amber-600/20"
                }
        };

        // Lấy data trận đấu dựa trên hasMatches state
        const getMatchesData = (): MatchData[] => {
                if (!hasMatches) return [];

                return [
                        {
                                id: 1,
                                homeTeam: "Manchester City",
                                awayTeam: "Liverpool",
                                homeLogo: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=80&h=80&fit=crop&crop=center&auto=format&q=85",
                                awayLogo: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=80&h=80&fit=crop&crop=center&auto=format&q=85",
                                date: "Today",
                                time: "20:30",
                                venue: "Etihad Stadium",
                                league: "Premier League",
                                importance: "high",
                                isNextMatch: true,
                                weather: "⛅ 18°C",
                                slug: "manchester-city-vs-liverpool",
                                fixtureId: "001"
                        },
                        {
                                id: 2,
                                homeTeam: "Real Madrid",
                                awayTeam: "Barcelona",
                                homeLogo: "https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=80&h=80&fit=crop&crop=center&auto=format&q=85",
                                awayLogo: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=80&h=80&fit=crop&crop=center&auto=format&q=85",
                                date: "Today",
                                time: "21:00",
                                venue: "Santiago Bernabéu",
                                league: "La Liga",
                                importance: "high",
                                isNextMatch: false,
                                weather: "☀️ 22°C",
                                slug: "real-madrid-vs-barcelona",
                                fixtureId: "002"
                        },
                        {
                                id: 3,
                                homeTeam: "Bayern Munich",
                                awayTeam: "Borussia Dortmund",
                                homeLogo: "https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=80&h=80&fit=crop&crop=center&auto=format&q=75",
                                awayLogo: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=80&h=80&fit=crop&crop=center&auto=format&q=75",
                                date: "Today",
                                time: "19:30",
                                venue: "Allianz Arena",
                                league: "Bundesliga",
                                importance: "medium",
                                isNextMatch: false,
                                weather: "🌧️ 15°C",
                                slug: "bayern-munich-vs-borussia-dortmund",
                                fixtureId: "003"
                        },
                        {
                                id: 4,
                                homeTeam: "Juventus",
                                awayTeam: "AC Milan",
                                homeLogo: "https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=80&h=80&fit=crop&crop=center&auto=format&q=75",
                                awayLogo: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=80&h=80&fit=crop&crop=center&auto=format&q=75",
                                date: "Today",
                                time: "22:45",
                                venue: "Allianz Stadium",
                                league: "Serie A",
                                importance: "high",
                                isNextMatch: false,
                                weather: "🌙 19°C",
                                slug: "juventus-vs-ac-milan",
                                fixtureId: "004"
                        },
                        {
                                id: 5,
                                homeTeam: "PSG",
                                awayTeam: "Marseille",
                                homeLogo: "https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=80&h=80&fit=crop&crop=center&auto=format&q=75",
                                awayLogo: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=80&h=80&fit=crop&crop=center&auto=format&q=75",
                                date: "Tomorrow",
                                time: "20:00",
                                venue: "Parc des Princes",
                                league: "Ligue 1",
                                importance: "medium",
                                isNextMatch: false,
                                weather: "☁️ 16°C",
                                slug: "psg-vs-marseille",
                                fixtureId: "005"
                        }
                ];
        };

        // Live Stats dựa trên trạng thái có trận đấu hay không
        const getLiveStats = (): Statistic[] => {
                if (!hasMatches) {
                        return [
                                { label: "Live Matches", value: "0", trend: "neutral" },
                                { label: "Goals Today", value: "0", trend: "neutral" },
                                { label: "Cards Issued", value: "0", trend: "neutral" },
                                { label: "Attendance", value: "0", trend: "neutral" }
                        ];
                }

                return [
                        { label: "Live Matches", value: "3", trend: "up" },
                        { label: "Goals Today", value: "27", trend: "up" },
                        { label: "Cards Issued", value: "14", trend: "down" },
                        { label: "Attendance", value: "156K", trend: "up" }
                ];
        };

        const upcomingMatches = getMatchesData();
        const liveStats = getLiveStats();
        const nextMatch = upcomingMatches.length > 0 ? upcomingMatches[0] : null;

        return (
                <section
                        className="hero-fullwidth relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800"
                        aria-label="Hero section with live football matches and statistics"
                >
                        {/* Stadium Background với các hiệu ứng - Performance Aware */}
                        <PerformanceAwareAnimation
                                animationType={isMobile ? 'subtle' : 'complex'}
                                fallbackContent={<div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800" />}
                        >
                                <StadiumBackground />
                                <StadiumFloodlights />
                        </PerformanceAwareAnimation>

                        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
                                {/* Hero Header với Digital Clock */}
                                <header aria-label="Live football matches overview">
                                        <HeroHeader currentTime={currentTime} />
                                </header>

                                {/* League Selector với 3D Effect */}
                                <nav aria-label="Football league selection">
                                        <LeagueSelector
                                                leagues={leagues}
                                                activeLeague={activeLeague}
                                                onLeagueChange={setActiveLeague}
                                        />
                                </nav>

                                {/* Main Content Grid - Mobile Optimized */}
                                <div className={`grid gap-6 ${isMobile
                                        ? 'grid-cols-1 gap-4'
                                        : isTablet
                                                ? 'grid-cols-1 lg:grid-cols-2 gap-6'
                                                : 'grid-cols-1 lg:grid-cols-3 gap-8'
                                        }`}>
                                        {/* Next Big Match Spotlight hoặc Empty State */}
                                        <article className="lg:col-span-2" aria-label="Featured match content">
                                                {!hasMatches ? (
                                                        <EmptyStateCard
                                                                onToggleMatches={toggleMatchesAvailability}
                                                                hasMatches={hasMatches}
                                                        />
                                                ) : displayMode === 'single' && nextMatch ? (
                                                        <div className="space-y-4">
                                                                {/* Toggle Button */}
                                                                <div className="flex justify-end">
                                                                        <button
                                                                                onClick={toggleDisplayMode}
                                                                                className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg transition-all duration-300 text-sm flex items-center space-x-2"
                                                                                aria-label="View all matches instead of single match"
                                                                        >
                                                                                <span>📋</span>
                                                                                <span>Xem Tất Cả Trận</span>
                                                                        </button>
                                                                </div>
                                                                <MatchDisplayCard
                                                                        match={nextMatch}
                                                                        countdown={countdown}
                                                                />
                                                        </div>
                                                ) : upcomingMatches.length > 0 ? (
                                                        <div className="space-y-4">
                                                                {/* Enhanced Toggle & Mode Controls */}
                                                                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                                                                        {/* Single/Multiple Toggle */}
                                                                        <button
                                                                                onClick={toggleDisplayMode}
                                                                                className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg transition-all duration-300 text-sm flex items-center space-x-2"
                                                                                aria-label="View single featured match instead of all matches"
                                                                        >
                                                                                <span>🎯</span>
                                                                                <span>Xem Trận Chính</span>
                                                                        </button>

                                                                        {/* Multi-Display Mode Selector */}
                                                                        <fieldset className="flex items-center space-x-2">
                                                                                <legend className="text-gray-400 text-sm">Chế độ:</legend>
                                                                                <div className="flex bg-gray-800/50 rounded-lg p-1 border border-gray-600/30" role="radiogroup" aria-label="Match display mode selection">
                                                                                        {(['stack', 'carousel', 'grid'] as const).map((mode) => (
                                                                                                <button
                                                                                                        key={mode}
                                                                                                        onClick={() => setMultiDisplayMode(mode)}
                                                                                                        className={`px-3 py-1 rounded-md text-xs font-medium transition-all duration-200 ${multiDisplayMode === mode
                                                                                                                ? 'bg-purple-600 text-white shadow-lg'
                                                                                                                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                                                                                                                }`}
                                                                                                        title={
                                                                                                                mode === 'stack' ? 'Xếp chồng - Main match + secondary matches' :
                                                                                                                        mode === 'carousel' ? 'Carousel - Slideshow với auto-rotation' :
                                                                                                                                'Grid - Lưới 2 cột responsive'
                                                                                                        }
                                                                                                >
                                                                                                        {mode === 'stack' && '📚'}
                                                                                                        {mode === 'carousel' && '🎠'}
                                                                                                        {mode === 'grid' && '⚏'}
                                                                                                        <span className="ml-1 capitalize">{mode}</span>
                                                                                                </button>
                                                                                        ))}
                                                                                </div>
                                                                        </fieldset>
                                                                </div>

                                                                <MultiMatchDisplayCard
                                                                        matches={upcomingMatches}
                                                                        countdown={countdown}
                                                                        displayMode={multiDisplayMode}
                                                                        maxDisplayed={multiDisplayMode === 'grid' ? 4 : 3}
                                                                />
                                                        </div>
                                                ) : (
                                                        <EmptyStateCard
                                                                onToggleMatches={toggleMatchesAvailability}
                                                                hasMatches={hasMatches}
                                                        />
                                                )}
                                        </article>

                                        {/* Live Statistics & Upcoming Matches */}
                                        <div className="space-y-8" aria-label="Live statistics and upcoming matches">
                                                {/* Live Statistics Panel */}
                                                <section aria-labelledby="live-stats-heading">
                                                        <StatisticsPanel liveStats={liveStats} />
                                                </section>

                                                {/* Upcoming Matches Mini List */}
                                                <section aria-labelledby="upcoming-matches-heading">
                                                        <UpcomingMatchesList
                                                                upcomingMatches={upcomingMatches}
                                                                hasMatches={hasMatches}
                                                        />
                                                </section>
                                        </div>
                                </div>
                        </div>
                </section>
        );
};

export default Hero;
