# LiveStatistics Component V1

## 📊 Overview
LiveStatistics V1 is a modular dashboard component that displays real-time sports statistics with interactive cards and live data updates.

## ✨ Features
- **Real-time Data Updates**: Live matches and goals counters with automatic refresh
- **Interactive Cards**: 4 responsive statistics cards with hover effects
- **Live Indicators**: Animated pulse indicators for real-time data
- **Responsive Grid**: Mobile-first responsive layout (1/2/4 columns)
- **Modern Design**: Neomorphism cards with gradient backgrounds

## 🎨 Statistics Cards
1. **Total Matches** - Season match count with percentage growth
2. **Live Matches** - Current live games with real-time indicator
3. **Active Teams** - Registered teams with new additions counter
4. **Total Goals** - Season goals count with live updates

## 📱 Responsive Design
- **Mobile**: Single column layout
- **Tablet**: 2 columns
- **Desktop**: 4 columns

## 🔄 Live Updates
- Live matches update every 3 seconds (random 7-12 range)
- Goals increment occasionally (0.5% chance per interval)
- Automatic cleanup on component unmount

## 🎯 Usage
```tsx
import LiveStatistics from '@/shared/components/layout/body/live-statistics';

// In your component
<LiveStatistics />
```

## 📊 Technical Specs
- **Lines of Code**: ~100
- **Dependencies**: React hooks (useState, useEffect)
- **CSS Classes**: TailwindCSS with custom neomorphism classes
- **Performance**: Optimized with controlled update intervals

## 🎨 Styling
Uses TailwindCSS classes with custom design system:
- `neomorphism-card`: Custom card styling
- `gradient-text`: Gradient text effects
- `neon-glow-*`: Color-specific glow effects
- Responsive breakpoints: `md:`, `lg:`

## 🔧 Customization
The component can be easily extended with:
- Additional statistics cards
- Different update intervals
- Custom data sources
- Theme variations

---
**Status**: ✅ Production Ready
**Version**: v1
**Last Updated**: June 5, 2025
