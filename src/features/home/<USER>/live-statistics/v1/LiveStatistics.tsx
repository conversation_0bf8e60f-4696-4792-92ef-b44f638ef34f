// LiveStatistics.tsx (v1) - Live Statistics Dashboard Component
"use client";

import React, { useState, useEffect } from "react";

const LiveStatistics: React.FC = () => {
        const [liveMatches, setLiveMatches] = useState(8);
        const [totalGoals, setTotalGoals] = useState(9234);

        useEffect(() => {
                // Simulate live data updates
                const interval = setInterval(() => {
                        // Randomly update live matches (7-12 range)
                        if (Math.random() > 0.7) {
                                setLiveMatches(prev => Math.max(7, Math.min(12, prev + (Math.random() > 0.5 ? 1 : -1))));
                        }
                        // Occasionally increment goals
                        if (Math.random() > 0.95) {
                                setTotalGoals(prev => prev + 1);
                        }
                }, 3000);

                return () => clearInterval(interval);
        }, []);

        return (
                <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {/* Total Matches Card */}
                        <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between mb-4">
                                        <div className="p-3 rounded-lg bg-gradient-to-br from-green-500/20 to-emerald-500/20 neon-glow-blue">
                                                <span className="text-2xl">🏆</span>
                                        </div>
                                        <div className="text-right">
                                                <span className="text-xs text-gray-400 uppercase tracking-wider">Total Matches</span>
                                                <p className="text-3xl font-bold gradient-text">2,847</p>
                                        </div>
                                </div>
                                <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-400">This Season</span>
                                        <span className="text-green-400 text-sm font-medium">+12.3%</span>
                                </div>
                        </div>

                        {/* Live Matches Card */}
                        <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between mb-4">
                                        <div className="p-3 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 neon-glow-purple">
                                                <span className="text-2xl">⚽</span>
                                        </div>
                                        <div className="text-right">
                                                <span className="text-xs text-gray-400 uppercase tracking-wider">Live Matches</span>
                                                <p className="text-3xl font-bold gradient-text">{liveMatches}</p>
                                        </div>
                                </div>
                                <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-400">Right Now</span>
                                        <div className="flex items-center gap-1">
                                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                <span className="text-red-400 text-sm font-medium">LIVE</span>
                                        </div>
                                </div>
                        </div>

                        {/* Active Teams Card */}
                        <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between mb-4">
                                        <div className="p-3 rounded-lg bg-gradient-to-br from-cyan-500/20 to-blue-500/20 neon-glow-cyan">
                                                <span className="text-2xl">👥</span>
                                        </div>
                                        <div className="text-right">
                                                <span className="text-xs text-gray-400 uppercase tracking-wider">Active Teams</span>
                                                <p className="text-3xl font-bold gradient-text">127</p>
                                        </div>
                                </div>
                                <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-400">Registered</span>
                                        <span className="text-cyan-400 text-sm font-medium">+5 New</span>
                                </div>
                        </div>

                        {/* Total Goals Card */}
                        <div className="neomorphism-card p-6 hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between mb-4">
                                        <div className="p-3 rounded-lg bg-gradient-to-br from-orange-500/20 to-red-500/20 neon-glow-blue">
                                                <span className="text-2xl">📊</span>
                                        </div>
                                        <div className="text-right">
                                                <span className="text-xs text-gray-400 uppercase tracking-wider">Total Goals</span>
                                                <p className="text-3xl font-bold gradient-text">{totalGoals}</p>
                                        </div>
                                </div>
                                <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-400">This Season</span>
                                        <span className="text-orange-400 text-sm font-medium">+847</span>
                                </div>
                        </div>
                </section>
        );
};

export default LiveStatistics;