// LastNews.tsx (v4) - Ultra Clean & Spacious Sports News Display
"use client";

import React from "react";

const LastNews: React.FC = () => {
        const newsData = [
                {
                        id: 1,
                        title: "Manchester City Signs New Star Midfielder",
                        description: "Manchester City completes record-breaking transfer for talented midfielder who will strengthen the squad for upcoming season.",
                        author: "<PERSON>",
                        time: "2 hours ago",
                        category: "Transfer",
                        thumbnail: "🏆",
                        teamCode: "MC",
                        teamColor: "from-sky-400 to-blue-600"
                },
                {
                        id: 2,
                        title: "Liverpool vs Manchester United Derby Results",
                        description: "Thrilling derby match sees Liverpool secure convincing 3-1 victory over Manchester United at Anfield stadium.",
                        author: "<PERSON>",
                        time: "4 hours ago",
                        category: "Match",
                        thumbnail: "⚽",
                        teamCode: "LIV",
                        teamColor: "from-red-600 to-red-700"
                },
                {
                        id: 3,
                        title: "Haaland Breaks Premier League Scoring Record",
                        description: "Erling <PERSON> continues remarkable form, breaking Premier League record for most goals in debut season.",
                        author: "<PERSON>",
                        time: "6 hours ago",
                        category: "Player",
                        thumbnail: "🌟",
                        teamCode: "MC",
                        teamColor: "from-sky-400 to-blue-600"
                },
                {
                        id: 4,
                        title: "Premier League Introduces New VAR Technology",
                        description: "Advanced VAR technology implementation promises faster and more precise decision-making during matches.",
                        author: "<PERSON>",
                        time: "8 hours ago",
                        category: "League",
                        thumbnail: "📊",
                        teamCode: "PL",
                        teamColor: "from-purple-600 to-blue-600"
                },
                {
                        id: 5,
                        title: "Arsenal Eyes Brazilian Wonder Kid Signing",
                        description: "Arsenal scouts monitoring 18-year-old Brazilian midfielder ahead of summer transfer window opening.",
                        author: "<PERSON> Silva",
                        time: "12 hours ago",
                        category: "Transfer",
                        thumbnail: "🔍",
                        teamCode: "ARS",
                        teamColor: "from-red-600 to-yellow-500"
                },
                {
                        id: 6,
                        title: "Champions League Semi-Final Draw Results",
                        description: "UEFA reveals Champions League semi-final matchups with exciting clashes between Europe's top teams.",
                        author: "UEFA Sports",
                        time: "1 day ago",
                        category: "League",
                        thumbnail: "🏆",
                        teamCode: "UCL",
                        teamColor: "from-blue-500 to-purple-600"
                }
        ];

        const getCategoryStyles = (category: string) => {
                const styles = {
                        "Transfer": {
                                text: "text-emerald-400",
                                bg: "bg-emerald-500/10 border-emerald-500/20",
                                icon: "from-emerald-600/20 to-green-600/20"
                        },
                        "Match": {
                                text: "text-rose-400",
                                bg: "bg-rose-500/10 border-rose-500/20",
                                icon: "from-rose-600/20 to-red-600/20"
                        },
                        "Player": {
                                text: "text-amber-400",
                                bg: "bg-amber-500/10 border-amber-500/20",
                                icon: "from-amber-600/20 to-orange-600/20"
                        },
                        "League": {
                                text: "text-cyan-400",
                                bg: "bg-cyan-500/10 border-cyan-500/20",
                                icon: "from-cyan-600/20 to-blue-600/20"
                        }
                };
                return styles[category as keyof typeof styles] || styles.League;
        };

        return (
                <section className="space-y-12">
                        {/* Ultra Clean Header */}
                        <div className="text-center space-y-4">
                                <h2 className="text-4xl font-bold gradient-text tracking-tight">
                                        Latest Sports News
                                </h2>
                                <p className="text-gray-400 text-lg max-w-2xl mx-auto leading-relaxed">
                                        Stay updated with the latest developments in the world of sports
                                </p>
                        </div>

                        {/* Spacious News Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                                {newsData.map((news) => {
                                        const categoryStyles = getCategoryStyles(news.category);

                                        return (
                                                <article
                                                        key={news.id}
                                                        className="group neomorphism-card p-8 hover:scale-[1.02] hover:shadow-2xl transition-all duration-300 cursor-pointer overflow-hidden"
                                                >
                                                        {/* Header Section */}
                                                        <div className="flex items-start justify-between mb-6">
                                                                <div className="flex items-center space-x-4">
                                                                        {/* Enhanced Icon */}
                                                                        <div className={`w-14 h-14 bg-gradient-to-br ${categoryStyles.icon} rounded-2xl flex items-center justify-center text-3xl shadow-lg`}>
                                                                                {news.thumbnail}
                                                                        </div>

                                                                        {/* Category Badge */}
                                                                        <span className={`px-4 py-2 rounded-full text-sm font-semibold border ${categoryStyles.text} ${categoryStyles.bg} backdrop-blur-sm`}>
                                                                                {news.category}
                                                                        </span>
                                                                </div>

                                                                {/* Team Badge */}
                                                                <div className={`w-12 h-12 bg-gradient-to-r ${news.teamColor} rounded-full flex items-center justify-center text-sm font-bold text-white shadow-lg`}>
                                                                        {news.teamCode}
                                                                </div>
                                                        </div>

                                                        {/* Content Section */}
                                                        <div className="space-y-5">
                                                                {/* Title */}
                                                                <h3 className="text-xl font-bold text-white line-clamp-2 group-hover:text-purple-300 transition-colors leading-tight">
                                                                        {news.title}
                                                                </h3>

                                                                {/* Description */}
                                                                <p className="text-gray-400 text-base line-clamp-3 leading-relaxed">
                                                                        {news.description}
                                                                </p>

                                                                {/* Footer */}
                                                                <div className="flex items-center justify-between pt-4 border-t border-gray-700/40">
                                                                        <div className="flex items-center space-x-3 text-gray-500">
                                                                                {/* Author Avatar */}
                                                                                <div className="w-8 h-8 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full flex items-center justify-center text-xs font-semibold text-white">
                                                                                        {news.author.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                                                </div>
                                                                                <div className="text-sm">
                                                                                        <span className="text-gray-300 font-medium">{news.author}</span>
                                                                                        <span className="mx-2">•</span>
                                                                                        <span className="text-gray-500">{news.time}</span>
                                                                                </div>
                                                                        </div>

                                                                        {/* Read More Button */}
                                                                        <button className="text-purple-400 hover:text-purple-300 text-sm font-semibold transition-all duration-200 hover:translate-x-1">
                                                                                Read More →
                                                                        </button>
                                                                </div>
                                                        </div>
                                                </article>
                                        );
                                })}
                        </div>

                        {/* Enhanced CTA */}
                        <div className="text-center pt-8">
                                <button className="futuristic-button px-12 py-4 rounded-2xl font-semibold text-lg neon-glow-cyan hover:scale-105 transition-all duration-300">
                                        View All News
                                </button>
                        </div>
                </section>
        );
};

export default LastNews;
