// LastNews.tsx (v6) - Horizontal Layout Version with Real Images & Modern UX
"use client";

import React from "react";
import Image from "next/image";

const LastNews: React.FC = () => {
        const newsData = [
                {
                        id: 1,
                        title: "Manchester City Completes Record-Breaking Midfielder Transfer",
                        description: "Manchester City finalizes a landmark €120M deal for Europe's most sought-after midfielder, marking the biggest transfer of the summer window. The 24-year-old playmaker will bring exceptional creativity and vision to Pep Guardiola's already formidable squad, with the deal including performance-based bonuses that could reach €140M total value.",
                        author: "<PERSON>",
                        time: "2 hours ago",
                        category: "Transfer",
                        imageUrl: "https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=800&h=400&fit=crop&crop=faces&auto=format&q=85",
                        alt: "Manchester City football transfer announcement",
                        teamCode: "MC",
                        teamColor: "from-sky-400 to-blue-600",
                        priority: "high",
                        readTime: "3 min read",
                        fixture: null
                },
                {
                        id: 2,
                        title: "Liverpool Crushes Manchester United in Epic Derby Showdown",
                        description: "A masterclass performance sees Liverpool dominate their rivals 4-1 at Anfield, with <PERSON><PERSON> scoring a hat-trick in front of 65,000 passionate fans. The Reds displayed exceptional attacking prowess throughout the match, with each goal showcasing their tactical superiority and individual brilliance under <PERSON>lopp's guidance.",
                        author: "Emma <PERSON>",
                        time: "4 hours ago",
                        category: "Match",
                        imageUrl: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800&h=400&fit=crop&crop=center&auto=format&q=85",
                        alt: "Liverpool vs Manchester United football match action",
                        teamCode: "LIV",
                        teamColor: "from-red-600 to-red-700",
                        priority: "high",
                        readTime: "4 min read",
                        fixture: {
                                homeTeam: "Liverpool",
                                awayTeam: "Manchester United",
                                score: "4-1",
                                date: "Today",
                                venue: "Anfield",
                                competition: "Premier League"
                        }
                },
                {
                        id: 3,
                        title: "Erling Haaland Shatters Premier League Goal-Scoring Records",
                        description: "The Norwegian sensation breaks multiple scoring records with his 35th goal of the season, cementing his status as one of football's elite strikers. His incredible pace, precision, and tactical awareness have redefined what it means to be a modern striker in today's game, with analysts predicting even greater achievements ahead.",
                        author: "David Kim",
                        time: "6 hours ago",
                        category: "Player",
                        imageUrl: "https://images.unsplash.com/photo-1543326727-cf6c39e8f84c?w=800&h=400&fit=crop&crop=center&auto=format&q=85",
                        alt: "Football player celebrating spectacular goal",
                        teamCode: "MC",
                        teamColor: "from-sky-400 to-blue-600",
                        priority: "medium",
                        readTime: "5 min read",
                        fixture: null
                },
                {
                        id: 4,
                        title: "Revolutionary VAR Technology Transforms Premier League Decision-Making",
                        description: "The Premier League unveils cutting-edge VAR systems powered by AI, promising 99.7% accuracy in critical decisions and reducing controversy. This groundbreaking technology utilizes machine learning algorithms to analyze multiple camera angles simultaneously, providing referees with unprecedented clarity for offside calls, penalty decisions, and goal-line technology.",
                        author: "Sarah Rodriguez",
                        time: "8 hours ago",
                        category: "League",
                        imageUrl: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=400&fit=crop&crop=center&auto=format&q=85",
                        alt: "Advanced VAR technology in modern football stadium",
                        teamCode: "PL",
                        teamColor: "from-purple-600 to-blue-600",
                        priority: "medium",
                        readTime: "6 min read",
                        fixture: null
                },
                {
                        id: 5,
                        title: "Arsenal Targets Brazilian Wonderkid in €80M Summer Swoop",
                        description: "The Gunners prepare a massive bid for São Paulo's 18-year-old sensation, with Arteta personally flying to Brazil to seal the deal. The versatile midfielder has already captured attention from Europe's biggest clubs with his exceptional dribbling skills, vision, and maturity beyond his years, making him one of the most sought-after talents in world football.",
                        author: "Lucas Silva",
                        time: "12 hours ago",
                        category: "Transfer",
                        imageUrl: "https://images.unsplash.com/photo-1553778263-73a83bab9b0c?w=800&h=400&fit=crop&crop=center&auto=format&q=85",
                        alt: "Young Brazilian football talent in action",
                        teamCode: "ARS",
                        teamColor: "from-red-600 to-yellow-500",
                        priority: "medium",
                        readTime: "4 min read",
                        fixture: null
                },
                {
                        id: 6,
                        title: "Champions League Semi-Finals Set for Explosive Encounters",
                        description: "UEFA's draw delivers dream matchups as Europe's giants clash for a spot in the final, promising unforgettable nights of Champions League magic. Real Madrid faces Manchester City in a repeat of last year's thriller, while Bayern Munich takes on Paris Saint-Germain in what promises to be a tactical masterclass between two football philosophies.",
                        author: "UEFA Sports",
                        time: "1 day ago",
                        category: "League",
                        imageUrl: "https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?w=800&h=400&fit=crop&crop=center&auto=format&q=85",
                        alt: "Champions League trophy in magnificent stadium",
                        teamCode: "UCL",
                        teamColor: "from-blue-500 to-purple-600",
                        priority: "low",
                        readTime: "7 min read",
                        fixture: {
                                homeTeam: "Real Madrid",
                                awayTeam: "Manchester City",
                                score: "vs",
                                date: "April 9, 2025",
                                venue: "Santiago Bernabéu",
                                competition: "Champions League SF"
                        }
                }
        ];

        const getCategoryStyles = (category: string) => {
                const styles = {
                        "Transfer": {
                                text: "text-emerald-300",
                                bg: "bg-emerald-500/15 border-emerald-400/30",
                                icon: "from-emerald-500/25 to-green-500/25",
                                glow: "shadow-emerald-500/20"
                        },
                        "Match": {
                                text: "text-rose-300",
                                bg: "bg-rose-500/15 border-rose-400/30",
                                icon: "from-rose-500/25 to-red-500/25",
                                glow: "shadow-rose-500/20"
                        },
                        "Player": {
                                text: "text-amber-300",
                                bg: "bg-amber-500/15 border-amber-400/30",
                                icon: "from-amber-500/25 to-orange-500/25",
                                glow: "shadow-amber-500/20"
                        },
                        "League": {
                                text: "text-cyan-300",
                                bg: "bg-cyan-500/15 border-cyan-400/30",
                                icon: "from-cyan-500/25 to-blue-500/25",
                                glow: "shadow-cyan-500/20"
                        }
                };
                return styles[category as keyof typeof styles] || styles.League;
        };

        const getPriorityBorder = (priority: string) => {
                switch (priority) {
                        case "high": return "border-l-4 border-l-purple-400/60";
                        case "medium": return "border-l-4 border-l-blue-400/40";
                        default: return "border-l-4 border-l-gray-400/20";
                }
        };

        return (
                <section
                        className="space-y-16 lg:space-y-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-16"
                        aria-labelledby="latest-news-heading"
                        aria-label="Latest sports news and articles section with Vietnamese content"
                        role="region"
                >
                        {/* Professional Wave League Navigation */}
                        <header className="text-center mb-16">
                                <nav
                                        className="bg-gradient-to-r from-slate-900/80 via-gray-800/70 to-slate-900/80 rounded-3xl py-8 px-8 border border-gray-700/30 shadow-2xl backdrop-blur-lg overflow-hidden"
                                        aria-label="Football leagues navigation"
                                >
                                        <div className="flex items-center justify-center space-x-3 lg:space-x-6 flex-wrap gap-y-6">
                                                {/* Premier League */}
                                                <div className="league-item wave-1 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-purple-600/25 to-purple-700/15 border border-purple-500/20 hover:border-purple-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-purple-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center text-2xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                PL
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">Premier League</span>
                                                </div>

                                                {/* La Liga */}
                                                <div className="league-item wave-2 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-red-600/25 to-orange-600/15 border border-red-500/20 hover:border-red-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-red-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🏆
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">La Liga</span>
                                                </div>

                                                {/* Serie A */}
                                                <div className="league-item wave-3 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-green-600/25 to-emerald-600/15 border border-green-500/20 hover:border-green-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-green-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🇮🇹
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">Serie A</span>
                                                </div>

                                                {/* Bundesliga */}
                                                <div className="league-item wave-4 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-yellow-600/25 to-amber-600/15 border border-yellow-500/20 hover:border-yellow-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-yellow-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🦅
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">Bundesliga</span>
                                                </div>

                                                {/* Ligue 1 */}
                                                <div className="league-item wave-5 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-blue-600/25 to-cyan-600/15 border border-blue-500/20 hover:border-blue-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-blue-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🐓
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">Ligue 1</span>
                                                </div>

                                                {/* Champions League */}
                                                <div className="league-item wave-6 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-indigo-600/25 to-purple-600/15 border border-indigo-500/20 hover:border-indigo-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-indigo-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                ⭐
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">Champions</span>
                                                </div>

                                                {/* Europa League */}
                                                <div className="league-item wave-7 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-orange-600/25 to-red-600/15 border border-orange-500/20 hover:border-orange-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-orange-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🏅
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">Europa</span>
                                                </div>

                                                {/* MLS */}
                                                <div className="league-item wave-8 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-cyan-600/25 to-teal-600/15 border border-cyan-500/20 hover:border-cyan-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-cyan-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-teal-600 rounded-xl flex items-center justify-center text-2xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                ⚽
                                                        </div>
                                                        <span className="text-xs font-bold text-white transition-colors duration-300">MLS</span>
                                                </div>
                                        </div>
                                </nav>
                        </header>

                        {/* Tin Nổi Bật Section Title - Left Aligned */}
                        <div className="mb-12">
                                <div className="flex items-center space-x-4 mb-2">
                                        <h2
                                                id="latest-news-heading"
                                                className="text-2xl md:text-3xl lg:text-4xl font-bold text-white gradient-text vietnamese-title"
                                        >
                                                Tin Nổi Bật
                                        </h2>
                                        <div className="flex-1 h-px bg-gradient-to-r from-purple-500/50 to-transparent" aria-hidden="true"></div>
                                </div>
                                <p className="text-gray-400 text-sm md:text-base max-w-2xl">
                                        Cập nhật những tin tức thể thao mới nhất và quan trọng nhất từ các giải đấu hàng đầu thế giới
                                </p>
                        </div>

                        {/* Optimized Horizontal News Layout */}
                        <div className="space-y-8">
                                {newsData.map((news, index) => {
                                        const categoryStyles = getCategoryStyles(news.category);
                                        const priorityBorder = getPriorityBorder(news.priority);
                                        const isEven = index % 2 === 0;

                                        return (
                                                <article
                                                        key={news.id}
                                                        className={`group neomorphism-card ${priorityBorder} ${news.priority === 'high' ? 'priority-high relative' : ''} ${isEven ? 'news-card-even' : 'news-card-odd'} overflow-hidden bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-md hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-500 cursor-pointer rounded-2xl border border-gray-700/30 hover:border-purple-500/30`}
                                                        style={{
                                                                animationDelay: `${index * 80}ms`
                                                        }}
                                                        aria-labelledby={`news-title-${news.id}`}
                                                        role="article"
                                                >
                                                        {/* Optimized Horizontal Layout Container */}
                                                        <div className={`flex flex-col ${isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'}`}>
                                                                {/* Enhanced Image Section - Better proportions */}
                                                                <div className="relative lg:w-5/12 h-64 md:h-72 lg:h-64 xl:h-72 overflow-hidden bg-gray-800/50">
                                                                        <Image
                                                                                src={news.imageUrl}
                                                                                alt={news.alt}
                                                                                fill
                                                                                className="object-cover group-hover:scale-105 transition-transform duration-700"
                                                                                sizes="(max-width: 1024px) 100vw, 42vw"
                                                                                priority={index < 3}
                                                                        />

                                                                        {/* Improved Image Overlay */}
                                                                        <div className={`absolute inset-0 ${isEven ? 'bg-gradient-to-r' : 'bg-gradient-to-l'} from-gray-900/80 via-gray-900/20 to-transparent lg:from-gray-900/60 lg:via-gray-900/20 lg:to-transparent`}></div>

                                                                        {/* Enhanced Category Badge */}
                                                                        <div className="absolute top-3 left-3 lg:top-4 lg:left-4">
                                                                                <div className="flex items-center space-x-1">
                                                                                        <span className={`px-2.5 py-1 lg:px-3 lg:py-1.5 rounded-lg text-xs font-semibold border ${categoryStyles.text} ${categoryStyles.bg} backdrop-blur-md shadow-md`}>
                                                                                                {news.category}
                                                                                        </span>
                                                                                        {news.priority === 'high' && (
                                                                                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                                                        )}
                                                                                </div>
                                                                        </div>

                                                                        {/* Enhanced Team Badge */}
                                                                        <div className="absolute top-3 right-3 lg:top-4 lg:right-4">
                                                                                <div className={`w-9 h-9 lg:w-11 lg:h-11 bg-gradient-to-r ${news.teamColor} rounded-lg flex items-center justify-center text-xs lg:text-sm font-bold text-white shadow-lg backdrop-blur-sm border border-white/20`}>
                                                                                        {news.teamCode}
                                                                                </div>
                                                                        </div>

                                                                        {/* Improved Read Time Badge */}
                                                                        <div className="absolute bottom-3 right-3 lg:bottom-4 lg:right-4">
                                                                                <span className="px-2 py-1 lg:px-2.5 lg:py-1 bg-black/80 text-white text-xs font-medium rounded-md backdrop-blur-sm border border-gray-600/50">
                                                                                        {news.readTime}
                                                                                </span>
                                                                        </div>
                                                                </div>

                                                                {/* Enhanced Content Section - Better spacing */}
                                                                <div className="lg:w-7/12 p-5 md:p-6 lg:p-6 xl:p-8 flex flex-col justify-between space-y-4">
                                                                        <div className="space-y-3 lg:space-y-4">
                                                                                {/* Optimized Title */}
                                                                                <h2 className="text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold text-white line-clamp-2 transition-colors duration-300 leading-tight vietnamese-text">
                                                                                        {news.title}
                                                                                </h2>

                                                                                {/* Enhanced Fixture Information */}
                                                                                {news.fixture && (
                                                                                        <div className="bg-gradient-to-r from-slate-800/80 to-slate-700/60 rounded-lg p-3 lg:p-4 border border-slate-600/40 backdrop-blur-sm">
                                                                                                <div className="flex items-center justify-between">
                                                                                                        <div className="text-center flex-1">
                                                                                                                <p className="text-white font-semibold text-sm lg:text-base vietnamese-text">{news.fixture.homeTeam}</p>
                                                                                                        </div>
                                                                                                        <div className="text-center px-3 lg:px-4">
                                                                                                                <span className="text-purple-300 font-bold text-lg lg:text-xl xl:text-2xl">{news.fixture.score}</span>
                                                                                                                <p className="text-gray-400 text-xs lg:text-sm mt-0.5">{news.fixture.date}</p>
                                                                                                        </div>
                                                                                                        <div className="text-center flex-1">
                                                                                                                <p className="text-white font-semibold text-sm lg:text-base vietnamese-text">{news.fixture.awayTeam}</p>
                                                                                                        </div>
                                                                                                </div>
                                                                                                <div className="text-center mt-2 pt-2 border-t border-slate-600/30">
                                                                                                        <span className="text-gray-300 text-xs lg:text-sm">{news.fixture.venue} • {news.fixture.competition}</span>
                                                                                                </div>
                                                                                        </div>
                                                                                )}

                                                                                {/* Enhanced Description */}
                                                                                <p className="text-gray-300 text-sm lg:text-base xl:text-lg line-clamp-3 lg:line-clamp-4 xl:line-clamp-5 leading-relaxed vietnamese-text" style={{ textAlign: 'justify' }}>
                                                                                        {news.description}
                                                                                </p>
                                                                        </div>

                                                                        {/* Enhanced Author Section */}
                                                                        <div className="flex items-center justify-between pt-3 lg:pt-4 border-t border-gray-600/30">
                                                                                <div className="flex items-center space-x-3">
                                                                                        {/* Enhanced Author Avatar */}
                                                                                        <div className="relative">
                                                                                                <div className="w-9 h-9 lg:w-10 lg:h-10 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full flex items-center justify-center text-xs lg:text-sm font-bold text-white shadow-md ring-2 ring-gray-500/20">
                                                                                                        {news.author.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                                                                </div>
                                                                                                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                                                                                        </div>
                                                                                        <div>
                                                                                                <p className="text-white font-medium text-sm lg:text-base vietnamese-text">{news.author}</p>
                                                                                                <p className="text-gray-400 text-xs lg:text-sm">{news.time}</p>
                                                                                        </div>
                                                                                </div>

                                                                                {/* Enhanced Read More Button */}
                                                                                <button className="group/btn flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-blue-600/20 text-purple-300 text-sm lg:text-base font-semibold px-4 py-2 rounded-lg border border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20">
                                                                                        <span>Đọc thêm</span>
                                                                                        <svg className="w-4 h-4 lg:w-5 lg:h-5 transform group-hover/btn:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                                                                        </svg>
                                                                                </button>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </article>
                                        );
                                })}
                        </div>
                </section>
        );
};

export default LastNews;
