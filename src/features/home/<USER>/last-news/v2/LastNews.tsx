// LastNews.tsx (v2) - Enhanced System News Display Component with Improved UX
"use client";

import React, { useState } from "react";

const LastNews: React.FC = () => {
        const [selectedCategory, setSelectedCategory] = useState<string>("all");
        const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

        const categories = [
                { id: "all", label: "All News", icon: "📰", count: 8 },
                { id: "transfer", label: "Transfers", icon: "🔄", count: 2 },
                { id: "match", label: "Matches", icon: "⚽", count: 3 },
                { id: "player", label: "Players", icon: "⭐", count: 2 },
                { id: "league", label: "League", icon: "🏆", count: 1 }
        ];

        const newsData = [
                {
                        id: 1,
                        category: "transfer",
                        priority: "high",
                        title: "BREAKING: <PERSON><PERSON><PERSON> Confirms Real Madrid Move",
                        description: "PSG superstar <PERSON><PERSON><PERSON> has officially confirmed his transfer to Real Madrid in a record-breaking deal worth €200 million.",
                        author: { name: "<PERSON>", avatar: "ER", verified: true },
                        publishedAt: "15 minutes ago",
                        readTime: "3 min read",
                        thumbnail: "🔥",
                        teams: [
                                { name: "Real Madrid", code: "RM", color: "from-white to-purple-100" },
                                { name: "PSG", code: "PSG", color: "from-blue-600 to-red-600" }
                        ],
                        trending: true,
                        views: "12.5K"
                },
                {
                        id: 2,
                        category: "match",
                        priority: "high",
                        title: "Champions League Final: City vs Inter",
                        description: "Manchester City faces Inter Milan in what promises to be an epic Champions League final at Wembley Stadium.",
                        author: { name: "David Kim", avatar: "DK", verified: true },
                        publishedAt: "1 hour ago",
                        readTime: "5 min read",
                        thumbnail: "🏆",
                        teams: [
                                { name: "Man City", code: "MC", color: "from-sky-400 to-blue-600" },
                                { name: "Inter Milan", code: "INT", color: "from-blue-800 to-black" }
                        ],
                        live: true,
                        views: "25.1K"
                },
                {
                        id: 3,
                        category: "player",
                        priority: "medium",
                        title: "Haaland Breaks All-Time Scoring Record",
                        description: "Erling Haaland becomes the youngest player to score 50 goals in a single Premier League season.",
                        author: { name: "Sarah Martinez", avatar: "SM", verified: true },
                        publishedAt: "3 hours ago",
                        readTime: "4 min read",
                        thumbnail: "⚽",
                        player: { name: "E. Haaland", code: "EH", color: "from-yellow-400 to-orange-500" },
                        teams: [{ name: "Man City", code: "MC", color: "from-sky-400 to-blue-600" }],
                        views: "18.7K"
                },
                {
                        id: 4,
                        category: "league",
                        priority: "medium",
                        title: "Premier League Introduces New Technology",
                        description: "Revolutionary goal-line technology and enhanced VAR system to be implemented next season.",
                        author: { name: "Mike Johnson", avatar: "MJ", verified: false },
                        publishedAt: "5 hours ago",
                        readTime: "2 min read",
                        thumbnail: "🔧",
                        teams: [{ name: "Premier League", code: "PL", color: "from-purple-600 to-pink-600" }],
                        views: "8.3K"
                },
                {
                        id: 5,
                        category: "transfer",
                        priority: "low",
                        title: "Arsenal Eyes Brazilian Wonder Kid",
                        description: "Arsenal scouts are closely monitoring 18-year-old Brazilian midfielder ahead of summer window.",
                        author: { name: "Lucas Silva", avatar: "LS", verified: true },
                        publishedAt: "8 hours ago",
                        readTime: "3 min read",
                        thumbnail: "🌟",
                        teams: [
                                { name: "Arsenal", code: "ARS", color: "from-red-600 to-yellow-500" },
                                { name: "Santos", code: "SAN", color: "from-white to-gray-300" }
                        ],
                        views: "5.2K"
                },
                {
                        id: 6,
                        category: "match",
                        priority: "high",
                        title: "El Clasico: Barca Dominates Real 4-1",
                        description: "Barcelona delivers stunning performance against Real Madrid in the latest El Clasico encounter.",
                        author: { name: "Carlos Rodriguez", avatar: "CR", verified: true },
                        publishedAt: "12 hours ago",
                        readTime: "6 min read",
                        thumbnail: "🔥",
                        teams: [
                                { name: "Barcelona", code: "BAR", color: "from-blue-700 to-red-600" },
                                { name: "Real Madrid", code: "RM", color: "from-white to-purple-100" }
                        ],
                        score: "4-1",
                        views: "45.8K"
                }
        ];

        const filteredNews = selectedCategory === "all"
                ? newsData
                : newsData.filter(news => news.category === selectedCategory);

        const getPriorityColor = (priority: string) => {
                switch (priority) {
                        case "high": return "border-l-red-500 bg-red-500/5";
                        case "medium": return "border-l-yellow-500 bg-yellow-500/5";
                        case "low": return "border-l-green-500 bg-green-500/5";
                        default: return "border-l-gray-500 bg-gray-500/5";
                }
        };

        const getCategoryBadge = (category: string) => {
                const badges = {
                        transfer: { bg: "bg-blue-500/20", text: "text-blue-400", label: "Transfer" },
                        match: { bg: "bg-red-500/20", text: "text-red-400", label: "Match" },
                        player: { bg: "bg-yellow-500/20", text: "text-yellow-400", label: "Player" },
                        league: { bg: "bg-purple-500/20", text: "text-purple-400", label: "League" }
                };
                return badges[category as keyof typeof badges] || badges.league;
        };

        return (
                <section className="space-y-8">
                        {/* Enhanced Header */}
                        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                                <div className="space-y-2">
                                        <div className="flex items-center space-x-3">
                                                <h2 className="text-4xl font-bold gradient-text">Latest News</h2>
                                                <div className="animate-pulse">
                                                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                                </div>
                                        </div>
                                        <p className="text-gray-400 text-lg">Breaking news and updates from the sports world</p>
                                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                <span>📊 {newsData.length} articles today</span>
                                                <span>🔥 {newsData.filter(n => n.trending).length} trending</span>
                                                <span>🎯 Last updated 5 min ago</span>
                                        </div>
                                </div>

                                {/* Control Panel */}
                                <div className="flex flex-col sm:flex-row gap-4">
                                        {/* View Mode Toggle */}
                                        <div className="flex bg-slate-800/50 rounded-xl p-1">
                                                <button
                                                        onClick={() => setViewMode("grid")}
                                                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${viewMode === "grid"
                                                                        ? "bg-purple-600 text-white shadow-lg"
                                                                        : "text-gray-400 hover:text-white"
                                                                }`}
                                                >
                                                        📱 Grid
                                                </button>
                                                <button
                                                        onClick={() => setViewMode("list")}
                                                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${viewMode === "list"
                                                                        ? "bg-purple-600 text-white shadow-lg"
                                                                        : "text-gray-400 hover:text-white"
                                                                }`}
                                                >
                                                        📋 List
                                                </button>
                                        </div>

                                        <button className="futuristic-button px-6 py-3 rounded-xl text-sm font-medium neon-glow-purple">
                                                <span className="flex items-center space-x-2">
                                                        <span>📺</span>
                                                        <span>Live Updates</span>
                                                </span>
                                        </button>
                                </div>
                        </div>

                        {/* Category Filter */}
                        <div className="flex flex-wrap gap-3">
                                {categories.map((category) => (
                                        <button
                                                key={category.id}
                                                onClick={() => setSelectedCategory(category.id)}
                                                className={`flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all ${selectedCategory === category.id
                                                                ? "bg-purple-600 text-white shadow-lg scale-105"
                                                                : "bg-slate-800/50 text-gray-300 hover:bg-slate-700/50 hover:text-white"
                                                        }`}
                                        >
                                                <span>{category.icon}</span>
                                                <span>{category.label}</span>
                                                <span className="bg-white/20 text-xs px-2 py-0.5 rounded-full">
                                                        {category.count}
                                                </span>
                                        </button>
                                ))}
                        </div>

                        {/* News Content */}
                        <div className={`${viewMode === "grid"
                                ? "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
                                : "space-y-4"
                                }`}>
                                {filteredNews.map((news) => {
                                        const badge = getCategoryBadge(news.category);
                                        const priorityStyle = getPriorityColor(news.priority);

                                        if (viewMode === "list") {
                                                return (
                                                        <div
                                                                key={news.id}
                                                                className={`neomorphism-card p-6 border-l-4 ${priorityStyle} hover:scale-[1.02] transition-all duration-300 cursor-pointer`}
                                                        >
                                                                <div className="flex items-start space-x-4">
                                                                        {/* Thumbnail */}
                                                                        <div className="flex-shrink-0 w-20 h-20 rounded-xl bg-gradient-to-br from-purple-600/20 to-blue-600/20 flex items-center justify-center text-2xl">
                                                                                {news.thumbnail}
                                                                        </div>

                                                                        {/* Content */}
                                                                        <div className="flex-1 min-w-0">
                                                                                <div className="flex items-start justify-between mb-2">
                                                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-1">
                                                                                                {news.title}
                                                                                        </h3>
                                                                                        <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
                                                                                                {news.trending && (
                                                                                                        <span className="text-orange-400">🔥</span>
                                                                                                )}
                                                                                                {news.live && (
                                                                                                        <span className="animate-pulse text-red-400">🔴</span>
                                                                                                )}
                                                                                                <span className={`${badge.bg} ${badge.text} px-2 py-1 rounded-full text-xs font-medium`}>
                                                                                                        {badge.label}
                                                                                                </span>
                                                                                        </div>
                                                                                </div>

                                                                                <p className="text-gray-400 text-sm line-clamp-2 mb-3">
                                                                                        {news.description}
                                                                                </p>

                                                                                <div className="flex items-center justify-between">
                                                                                        <div className="flex items-center space-x-3">
                                                                                                <div className="flex items-center space-x-2">
                                                                                                        <div className={`w-6 h-6 bg-gradient-to-r ${news.teams[0]?.color || 'from-gray-500 to-gray-600'} rounded-full flex items-center justify-center text-xs font-bold text-white`}>
                                                                                                                {news.author.avatar}
                                                                                                        </div>
                                                                                                        <span className="text-sm text-gray-400">{news.author.name}</span>
                                                                                                        {news.author.verified && (
                                                                                                                <span className="text-blue-400">✓</span>
                                                                                                        )}
                                                                                                </div>
                                                                                                <span className="text-xs text-gray-500">•</span>
                                                                                                <span className="text-xs text-gray-500">{news.publishedAt}</span>
                                                                                                <span className="text-xs text-gray-500">•</span>
                                                                                                <span className="text-xs text-gray-500">{news.readTime}</span>
                                                                                        </div>

                                                                                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                                                                                                <span>👁️ {news.views}</span>
                                                                                                <button className="text-purple-400 hover:text-purple-300 font-medium">
                                                                                                        Read More →
                                                                                                </button>
                                                                                        </div>
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                );
                                        }

                                        // Grid View
                                        return (
                                                <div
                                                        key={news.id}
                                                        className={`neomorphism-card p-6 border-l-4 ${priorityStyle} hover:scale-105 transition-all duration-300 cursor-pointer group`}
                                                >
                                                        <div className="flex flex-col h-full">
                                                                {/* Enhanced Thumbnail */}
                                                                <div className="relative mb-4 rounded-xl overflow-hidden h-48 bg-gradient-to-br from-purple-600/20 to-blue-600/20">
                                                                        <div className="absolute inset-0 flex items-center justify-center text-6xl">
                                                                                {news.thumbnail}
                                                                        </div>

                                                                        {/* Overlays */}
                                                                        <div className="absolute top-3 left-3 flex space-x-2">
                                                                                {news.trending && (
                                                                                        <span className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                                                                                                <span>🔥</span>
                                                                                                <span>Trending</span>
                                                                                        </span>
                                                                                )}
                                                                                {news.live && (
                                                                                        <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 animate-pulse">
                                                                                                <span>🔴</span>
                                                                                                <span>Live</span>
                                                                                        </span>
                                                                                )}
                                                                        </div>

                                                                        <div className="absolute top-3 right-3">
                                                                                <span className={`${badge.bg} ${badge.text} px-2 py-1 rounded-full text-xs font-medium`}>
                                                                                        {badge.label}
                                                                                </span>
                                                                        </div>

                                                                        <div className="absolute bottom-3 right-3 bg-black/70 text-white px-2 py-1 rounded-lg text-xs">
                                                                                {news.readTime}
                                                                        </div>
                                                                </div>

                                                                {/* Enhanced Content */}
                                                                <div className="flex-1 space-y-3">
                                                                        <h3 className="text-xl font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                                {news.title}
                                                                        </h3>

                                                                        <p className="text-gray-400 text-sm line-clamp-3">
                                                                                {news.description}
                                                                        </p>

                                                                        {/* Enhanced Author Info */}
                                                                        <div className="flex items-center justify-between text-sm">
                                                                                <div className="flex items-center space-x-2">
                                                                                        <div className={`w-8 h-8 bg-gradient-to-r ${news.teams[0]?.color || 'from-gray-500 to-gray-600'} rounded-full flex items-center justify-center text-xs font-bold text-white`}>
                                                                                                {news.author.avatar}
                                                                                        </div>
                                                                                        <div>
                                                                                                <div className="flex items-center space-x-1">
                                                                                                        <span className="text-gray-300">{news.author.name}</span>
                                                                                                        {news.author.verified && (
                                                                                                                <span className="text-blue-400">✓</span>
                                                                                                        )}
                                                                                                </div>
                                                                                                <span className="text-xs text-gray-500">{news.publishedAt}</span>
                                                                                        </div>
                                                                                </div>
                                                                                <div className="text-xs text-gray-500 flex items-center space-x-1">
                                                                                        <span>👁️</span>
                                                                                        <span>{news.views}</span>
                                                                                </div>
                                                                        </div>

                                                                        {/* Teams/Player Section */}
                                                                        <div className="flex items-center justify-between">
                                                                                <div className="flex items-center space-x-2">
                                                                                        {news.teams.map((team, index) => (
                                                                                                <div key={index} className="flex items-center space-x-1">
                                                                                                        <div className={`w-6 h-6 bg-gradient-to-r ${team.color} rounded-full flex items-center justify-center text-xs font-bold text-white`}>
                                                                                                                {team.code}
                                                                                                        </div>
                                                                                                        <span className="text-xs text-gray-400">{team.name}</span>
                                                                                                        {index < news.teams.length - 1 && (
                                                                                                                <span className="text-gray-500 mx-1">vs</span>
                                                                                                        )}
                                                                                                </div>
                                                                                        ))}
                                                                                        {news.player && (
                                                                                                <div className="flex items-center space-x-1">
                                                                                                        <div className={`w-6 h-6 bg-gradient-to-r ${news.player.color} rounded-full flex items-center justify-center text-xs font-bold text-white`}>
                                                                                                                {news.player.code}
                                                                                                        </div>
                                                                                                        <span className="text-xs text-gray-400">{news.player.name}</span>
                                                                                                </div>
                                                                                        )}
                                                                                        {news.score && (
                                                                                                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-bold">
                                                                                                        {news.score}
                                                                                                </span>
                                                                                        )}
                                                                                </div>

                                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors group-hover:translate-x-1">
                                                                                        Read More →
                                                                                </button>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>
                                        );
                                })}
                        </div>

                        {/* Load More Section */}
                        {filteredNews.length > 0 && (
                                <div className="text-center pt-8">
                                        <button className="futuristic-button px-8 py-4 rounded-xl font-medium neon-glow-cyan">
                                                <span className="flex items-center space-x-2">
                                                        <span>Load More Articles</span>
                                                        <span>📰</span>
                                                </span>
                                        </button>
                                        <p className="text-gray-500 text-sm mt-3">
                                                Showing {filteredNews.length} of {newsData.length} articles
                                        </p>
                                </div>
                        )}
                </section>
        );
};

export default LastNews;
