// LastNews.tsx (v5) - Professional Sports News Display with Real Images & Modern UX
"use client";

import React from "react";
import Image from "next/image";

const LastNews: React.FC = () => {
        const newsData = [
                {
                        id: 1,
                        title: "Manchester City Completes Record-Breaking Midfielder Transfer",
                        description: "Manchester City finalizes a landmark €120M deal for Europe's most sought-after midfielder, marking the biggest transfer of the summer window. The 24-year-old playmaker will bring exceptional creativity and vision to Pep Guardiola's already formidable squad, with the deal including performance-based bonuses that could reach €140M total value.",
                        author: "<PERSON>",
                        time: "2 hours ago",
                        category: "Transfer",
                        imageUrl: "https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=1200&h=600&fit=crop&crop=faces&auto=format&q=85",
                        alt: "Manchester City football transfer announcement",
                        teamCode: "MC",
                        teamColor: "from-sky-400 to-blue-600",
                        priority: "high",
                        readTime: "3 min read",
                        fixture: null
                },
                {
                        id: 2,
                        title: "Liverpool Crushes Manchester United in Epic Derby Showdown",
                        description: "A masterclass performance sees Liverpool dominate their rivals 4-1 at Anfield, with <PERSON><PERSON> scoring a hat-trick in front of 65,000 passionate fans. The Reds displayed exceptional attacking prowess throughout the match, with each goal showcasing their tactical superiority and individual brilliance under Klopp's guidance.",
                        author: "<PERSON> <PERSON>",
                        time: "4 hours ago",
                        category: "Match",
                        imageUrl: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=1200&h=600&fit=crop&crop=center&auto=format&q=85",
                        alt: "Liverpool vs Manchester United football match action",
                        teamCode: "LIV",
                        teamColor: "from-red-600 to-red-700",
                        priority: "high",
                        readTime: "4 min read",
                        fixture: {
                                homeTeam: "Liverpool",
                                awayTeam: "Manchester United",
                                score: "4-1",
                                date: "Today",
                                venue: "Anfield",
                                competition: "Premier League"
                        }
                },
                {
                        id: 3,
                        title: "Erling Haaland Shatters Premier League Goal-Scoring Records",
                        description: "The Norwegian sensation breaks multiple scoring records with his 35th goal of the season, cementing his status as one of football's elite strikers. His incredible pace, precision, and tactical awareness have redefined what it means to be a modern striker in today's game, with analysts predicting even greater achievements ahead.",
                        author: "David Kim",
                        time: "6 hours ago",
                        category: "Player",
                        imageUrl: "https://images.unsplash.com/photo-1543326727-cf6c39e8f84c?w=1200&h=600&fit=crop&crop=center&auto=format&q=85",
                        alt: "Football player celebrating spectacular goal",
                        teamCode: "MC",
                        teamColor: "from-sky-400 to-blue-600",
                        priority: "medium",
                        readTime: "5 min read",
                        fixture: null
                },
                {
                        id: 4,
                        title: "Revolutionary VAR Technology Transforms Premier League Decision-Making",
                        description: "The Premier League unveils cutting-edge VAR systems powered by AI, promising 99.7% accuracy in critical decisions and reducing controversy. This groundbreaking technology utilizes machine learning algorithms to analyze multiple camera angles simultaneously, providing referees with unprecedented clarity for offside calls, penalty decisions, and goal-line technology.",
                        author: "Sarah Rodriguez",
                        time: "8 hours ago",
                        category: "League",
                        imageUrl: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=1200&h=600&fit=crop&crop=center&auto=format&q=85",
                        alt: "Advanced VAR technology in modern football stadium",
                        teamCode: "PL",
                        teamColor: "from-purple-600 to-blue-600",
                        priority: "medium",
                        readTime: "6 min read",
                        fixture: null
                },
                {
                        id: 5,
                        title: "Arsenal Targets Brazilian Wonderkid in €80M Summer Swoop",
                        description: "The Gunners prepare a massive bid for São Paulo's 18-year-old sensation, with Arteta personally flying to Brazil to seal the deal. The versatile midfielder has already captured attention from Europe's biggest clubs with his exceptional dribbling skills, vision, and maturity beyond his years, making him one of the most sought-after talents in world football.",
                        author: "Lucas Silva",
                        time: "12 hours ago",
                        category: "Transfer",
                        imageUrl: "https://images.unsplash.com/photo-1553778263-73a83bab9b0c?w=1200&h=600&fit=crop&crop=center&auto=format&q=85",
                        alt: "Young Brazilian football talent in action",
                        teamCode: "ARS",
                        teamColor: "from-red-600 to-yellow-500",
                        priority: "medium",
                        readTime: "4 min read",
                        fixture: null
                },
                {
                        id: 6,
                        title: "Champions League Semi-Finals Set for Explosive Encounters",
                        description: "UEFA's draw delivers dream matchups as Europe's giants clash for a spot in the final, promising unforgettable nights of Champions League magic. Real Madrid faces Manchester City in a repeat of last year's thriller, while Bayern Munich takes on Paris Saint-Germain in what promises to be a tactical masterclass between two football philosophies.",
                        author: "UEFA Sports",
                        time: "1 day ago",
                        category: "League",
                        imageUrl: "https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?w=1200&h=600&fit=crop&crop=center&auto=format&q=85",
                        alt: "Champions League trophy in magnificent stadium",
                        teamCode: "UCL",
                        teamColor: "from-blue-500 to-purple-600",
                        priority: "low",
                        readTime: "7 min read",
                        fixture: {
                                homeTeam: "Real Madrid",
                                awayTeam: "Manchester City",
                                score: "vs",
                                date: "April 9, 2025",
                                venue: "Santiago Bernabéu",
                                competition: "Champions League SF"
                        }
                }
        ];

        const getCategoryStyles = (category: string) => {
                const styles = {
                        "Transfer": {
                                text: "text-emerald-300",
                                bg: "bg-emerald-500/15 border-emerald-400/30",
                                icon: "from-emerald-500/25 to-green-500/25",
                                glow: "shadow-emerald-500/20"
                        },
                        "Match": {
                                text: "text-rose-300",
                                bg: "bg-rose-500/15 border-rose-400/30",
                                icon: "from-rose-500/25 to-red-500/25",
                                glow: "shadow-rose-500/20"
                        },
                        "Player": {
                                text: "text-amber-300",
                                bg: "bg-amber-500/15 border-amber-400/30",
                                icon: "from-amber-500/25 to-orange-500/25",
                                glow: "shadow-amber-500/20"
                        },
                        "League": {
                                text: "text-cyan-300",
                                bg: "bg-cyan-500/15 border-cyan-400/30",
                                icon: "from-cyan-500/25 to-blue-500/25",
                                glow: "shadow-cyan-500/20"
                        }
                };
                return styles[category as keyof typeof styles] || styles.League;
        };

        const getPriorityBorder = (priority: string) => {
                switch (priority) {
                        case "high": return "border-l-4 border-l-purple-400/60";
                        case "medium": return "border-l-4 border-l-blue-400/40";
                        default: return "border-l-4 border-l-gray-400/20";
                }
        }; return (
                <section className="space-y-16 lg:space-y-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-16">
                        {/* Professional Wave League Navigation */}
                        <header className="text-center mb-16">
                                <div className="bg-gradient-to-r from-slate-900/80 via-gray-800/70 to-slate-900/80 rounded-3xl py-8 px-8 border border-gray-700/30 shadow-2xl backdrop-blur-lg overflow-hidden">
                                        <div className="flex items-center justify-center space-x-3 lg:space-x-6 flex-wrap gap-y-6">
                                                {/* Premier League */}
                                                <div className="league-item wave-1 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-purple-600/25 to-purple-700/15 hover:from-purple-600/40 hover:to-purple-700/25 border border-purple-500/20 hover:border-purple-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-purple-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center text-2xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                PL
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-purple-200 transition-colors duration-300">Premier League</span>
                                                </div>

                                                {/* La Liga */}
                                                <div className="league-item wave-2 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-red-600/25 to-orange-600/15 hover:from-red-600/40 hover:to-orange-600/25 border border-red-500/20 hover:border-red-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-red-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🏆
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-red-200 transition-colors duration-300">La Liga</span>
                                                </div>

                                                {/* Serie A */}
                                                <div className="league-item wave-3 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-green-600/25 to-emerald-600/15 hover:from-green-600/40 hover:to-emerald-600/25 border border-green-500/20 hover:border-green-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-green-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🇮🇹
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-green-200 transition-colors duration-300">Serie A</span>
                                                </div>

                                                {/* Bundesliga */}
                                                <div className="league-item wave-4 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-yellow-600/25 to-amber-600/15 hover:from-yellow-600/40 hover:to-amber-600/25 border border-yellow-500/20 hover:border-yellow-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-yellow-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🦅
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-yellow-200 transition-colors duration-300">Bundesliga</span>
                                                </div>

                                                {/* Ligue 1 */}
                                                <div className="league-item wave-5 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-blue-600/25 to-cyan-600/15 hover:from-blue-600/40 hover:to-cyan-600/25 border border-blue-500/20 hover:border-blue-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-blue-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🐓
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-blue-200 transition-colors duration-300">Ligue 1</span>
                                                </div>

                                                {/* Champions League */}
                                                <div className="league-item wave-6 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-indigo-600/25 to-purple-600/15 hover:from-indigo-600/40 hover:to-purple-600/25 border border-indigo-500/20 hover:border-indigo-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-indigo-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                ⭐
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-indigo-200 transition-colors duration-300">Champions</span>
                                                </div>

                                                {/* Europa League */}
                                                <div className="league-item wave-7 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-orange-600/25 to-red-600/15 hover:from-orange-600/40 hover:to-red-600/25 border border-orange-500/20 hover:border-orange-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-orange-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center text-xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                🏅
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-orange-200 transition-colors duration-300">Europa</span>
                                                </div>

                                                {/* MLS */}
                                                <div className="league-item wave-8 flex flex-col items-center space-y-3 px-5 py-4 rounded-2xl bg-gradient-to-br from-cyan-600/25 to-teal-600/15 hover:from-cyan-600/40 hover:to-teal-600/25 border border-cyan-500/20 hover:border-cyan-400/40 transition-all duration-500 cursor-pointer group shadow-lg hover:shadow-cyan-500/20">
                                                        <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-teal-600 rounded-xl flex items-center justify-center text-2xl font-bold text-white transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-400 shadow-lg">
                                                                ⚽
                                                        </div>
                                                        <span className="text-xs font-bold text-white group-hover:text-cyan-200 transition-colors duration-300">MLS</span>
                                                </div>
                                        </div>
                                </div>
                        </header>

                        {/* Tin Nổi Bật Section Title */}
                        <div className="text-center mb-12">
                                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 gradient-text vietnamese-title">
                                        Tin Nổi Bật
                                </h2>
                                <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 mx-auto rounded-full"></div>
                        </div>

                        {/* Professional News Grid with Enhanced Layout */}
                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12">
                                {newsData.map((news, index) => {
                                        const categoryStyles = getCategoryStyles(news.category);
                                        const priorityBorder = getPriorityBorder(news.priority);

                                        return (
                                                <article
                                                        key={news.id}
                                                        className={`group neomorphism-card ${priorityBorder} overflow-hidden bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm hover:scale-[1.02] hover:shadow-2xl transition-all duration-700 cursor-pointer rounded-3xl`}
                                                        style={{
                                                                animationDelay: `${index * 150}ms`
                                                        }}
                                                >
                                                        {/* Professional Image Section */}
                                                        <div className="relative h-56 sm:h-64 lg:h-72 overflow-hidden">
                                                                <Image
                                                                        src={news.imageUrl}
                                                                        alt={news.alt}
                                                                        fill
                                                                        className="object-cover group-hover:scale-110 transition-transform duration-700"
                                                                        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                                                                        priority={index < 3}
                                                                />

                                                                {/* Image Overlay */}
                                                                <div className="absolute inset-0 bg-gradient-to-t from-gray-900/90 via-gray-900/20 to-transparent"></div>

                                                                {/* Category Badge on Image */}
                                                                <div className="absolute top-4 left-4 lg:top-6 lg:left-6">
                                                                        <span className={`px-3 py-1.5 lg:px-4 lg:py-2 rounded-full text-xs font-bold border ${categoryStyles.text} ${categoryStyles.bg} backdrop-blur-md shadow-lg`}>
                                                                                {news.category}
                                                                        </span>
                                                                </div>

                                                                {/* Team Badge */}
                                                                <div className="absolute top-4 right-4 lg:top-6 lg:right-6">
                                                                        <div className={`w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-r ${news.teamColor} rounded-xl flex items-center justify-center text-xs lg:text-sm font-bold text-white shadow-xl backdrop-blur-sm`}>
                                                                                {news.teamCode}
                                                                        </div>
                                                                </div>

                                                                {/* Read Time Badge */}
                                                                <div className="absolute bottom-4 right-4 lg:bottom-6 lg:right-6">
                                                                        <span className="px-2.5 py-1 lg:px-3 lg:py-1 bg-black/70 text-white text-xs font-medium rounded-full backdrop-blur-sm">
                                                                                {news.readTime}
                                                                        </span>
                                                                </div>
                                                        </div>

                                                        {/* Enhanced Content Section */}
                                                        <div className="p-6 space-y-5">
                                                                {/* Professional Title */}
                                                                <h2 className="text-lg md:text-xl font-bold text-white line-clamp-2 group-hover:text-purple-200 transition-colors duration-300 leading-tight">
                                                                        {news.title}
                                                                </h2>

                                                                {/* Fixture Information (if available) */}
                                                                {news.fixture && (
                                                                        <div className="bg-gradient-to-r from-slate-800/60 to-slate-700/40 rounded-xl p-4 border border-slate-600/30">
                                                                                <div className="flex items-center justify-between">
                                                                                        <div className="text-center flex-1">
                                                                                                <p className="text-white font-semibold text-sm">{news.fixture.homeTeam}</p>
                                                                                        </div>
                                                                                        <div className="text-center px-4">
                                                                                                <span className="text-purple-300 font-bold text-lg">{news.fixture.score}</span>
                                                                                                <p className="text-gray-400 text-xs mt-1">{news.fixture.date}</p>
                                                                                        </div>
                                                                                        <div className="text-center flex-1">
                                                                                                <p className="text-white font-semibold text-sm">{news.fixture.awayTeam}</p>
                                                                                        </div>
                                                                                </div>
                                                                                <div className="text-center mt-2">
                                                                                        <span className="text-gray-300 text-xs">{news.fixture.venue} • {news.fixture.competition}</span>
                                                                                </div>
                                                                        </div>
                                                                )}

                                                                {/* Enhanced Professional Description */}
                                                                <p className="text-gray-300 text-sm md:text-base line-clamp-6 leading-relaxed font-light text-justify hyphens-auto" style={{ textAlign: 'justify' }}>
                                                                        {news.description}
                                                                </p>

                                                                {/* Compact Author Section */}
                                                                <div className="flex items-center justify-between pt-4 border-t border-gray-600/20">
                                                                        <div className="flex items-center space-x-2">
                                                                                {/* Smaller Author Avatar */}
                                                                                <div className="w-8 h-8 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-md ring-1 ring-gray-500/20">
                                                                                        {news.author.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                                                </div>
                                                                                <div>
                                                                                        <p className="text-white font-medium text-xs">{news.author}</p>
                                                                                        <p className="text-gray-400 text-xs">{news.time}</p>
                                                                                </div>
                                                                        </div>

                                                                        {/* Compact Read More Button */}
                                                                        <button className="group/btn flex items-center space-x-1 text-purple-300 hover:text-purple-200 text-xs font-semibold transition-all duration-300 hover:translate-x-1">
                                                                                <span>Đọc thêm</span>
                                                                                <svg className="w-3 h-3 transform group-hover/btn:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                                                                </svg>
                                                                        </button>
                                                                </div>
                                                        </div>
                                                </article>
                                        );
                                })}
                        </div>


                </section>
        );
};

export default LastNews;