// LastNews.tsx (v1) - Clean and Simple Sports News Display
"use client";

import React from "react";

const LastNews: React.FC = () => {
        return (
                <section className="space-y-8">
                        {/* Clean Header */}
                        <div className="text-center">
                                <h2 className="text-3xl font-bold gradient-text mb-3">Latest Sports News</h2>
                                <p className="text-gray-400">Stay updated with the latest sports developments</p>
                        </div>

                        {/* Clean Grid Layout */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {/* News Article 1 - Transfer News */}
                                <article className="neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 cursor-pointer group">
                                        <div className="flex flex-col h-full space-y-4">
                                                {/* Clean Thumbnail */}
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="w-12 h-12 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-xl flex items-center justify-center text-2xl">
                                                                        🏆
                                                                </div>
                                                                <span className="bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-xs font-medium">
                                                                        Transfer
                                                                </span>
                                                        </div>
                                                        <div className="w-8 h-8 bg-gradient-to-r from-sky-400 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                MC
                                                        </div>
                                                </div>

                                                {/* Content */}
                                                <div className="flex-1 space-y-3">
                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                Manchester City Signs New Star Midfielder for Record Fee
                                                        </h3>

                                                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                                                                Manchester City has completed the signing of talented midfielder for a record-breaking transfer fee. The 24-year-old player is expected to strengthen the squad significantly for the upcoming season.
                                                        </p>

                                                        {/* Clean Footer */}
                                                        <div className="flex items-center justify-between pt-3 border-t border-gray-700/30">
                                                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                                        <span>John Smith</span>
                                                                        <span>•</span>
                                                                        <span>2 hours ago</span>
                                                                </div>
                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
                                                                        Read →
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>
                                </article>

                                {/* News Article 2 - Match Report */}
                                <article className="neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 cursor-pointer group">
                                        <div className="flex flex-col h-full space-y-4">
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="w-12 h-12 bg-gradient-to-br from-red-600/20 to-orange-600/20 rounded-xl flex items-center justify-center text-2xl">
                                                                        ⚽
                                                                </div>
                                                                <span className="bg-red-500/10 text-red-400 px-3 py-1 rounded-full text-xs font-medium">
                                                                        Match
                                                                </span>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                                <div className="w-6 h-6 bg-gradient-to-r from-red-600 to-red-700 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                        LIV
                                                                </div>
                                                                <span className="text-gray-500 text-xs">vs</span>
                                                                <div className="w-6 h-6 bg-gradient-to-r from-red-800 to-yellow-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                        MU
                                                                </div>
                                                        </div>
                                                </div>

                                                <div className="flex-1 space-y-3">
                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                Liverpool Dominates Manchester United in Derby Clash
                                                        </h3>

                                                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                                                                A thrilling derby match saw Liverpool secure a convincing 3-1 victory over Manchester United at Anfield. Goals from Salah, Mané, and Firmino sealed the win in spectacular fashion.
                                                        </p>

                                                        <div className="flex items-center justify-between pt-3 border-t border-gray-700/30">
                                                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                                        <span>Emma Martinez</span>
                                                                        <span>•</span>
                                                                        <span>4 hours ago</span>
                                                                </div>
                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
                                                                        Read →
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>
                                </article>

                                {/* News Article 3 - Player Spotlight */}
                                <article className="neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 cursor-pointer group">
                                        <div className="flex flex-col h-full space-y-4">
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="w-12 h-12 bg-gradient-to-br from-yellow-600/20 to-orange-600/20 rounded-xl flex items-center justify-center text-2xl">
                                                                        🌟
                                                                </div>
                                                                <span className="bg-yellow-500/10 text-yellow-400 px-3 py-1 rounded-full text-xs font-medium">
                                                                        Player
                                                                </span>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                                <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                        EH
                                                                </div>
                                                                <div className="w-6 h-6 bg-gradient-to-r from-sky-400 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                        MC
                                                                </div>
                                                        </div>
                                                </div>

                                                <div className="flex-1 space-y-3">
                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                Haaland Breaks Goal Scoring Record in Premier League
                                                        </h3>

                                                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                                                                Erling Haaland continues his remarkable goal-scoring form, breaking the Premier League record for most goals in a debut season. The Norwegian striker shows no signs of slowing down.
                                                        </p>

                                                        <div className="flex items-center justify-between pt-3 border-t border-gray-700/30">
                                                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                                        <span>David Kim</span>
                                                                        <span>•</span>
                                                                        <span>6 hours ago</span>
                                                                </div>
                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
                                                                        Read →
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>
                                </article>

                                {/* News Article 4 - League Update */}
                                <article className="neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 cursor-pointer group">
                                        <div className="flex flex-col h-full space-y-4">
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="w-12 h-12 bg-gradient-to-br from-green-600/20 to-blue-600/20 rounded-xl flex items-center justify-center text-2xl">
                                                                        📊
                                                                </div>
                                                                <span className="bg-blue-500/10 text-blue-400 px-3 py-1 rounded-full text-xs font-medium">
                                                                        League
                                                                </span>
                                                        </div>
                                                        <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                PL
                                                        </div>
                                                </div>

                                                <div className="flex-1 space-y-3">
                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                Premier League Introduces New VAR Technology
                                                        </h3>

                                                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                                                                The Premier League announces the implementation of advanced VAR technology to improve decision-making accuracy. The new system promises faster and more precise calls during matches.
                                                        </p>

                                                        <div className="flex items-center justify-between pt-3 border-t border-gray-700/30">
                                                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                                        <span>Sarah Rodriguez</span>
                                                                        <span>•</span>
                                                                        <span>8 hours ago</span>
                                                                </div>
                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
                                                                        Read →
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>
                                </article>

                                {/* News Article 5 - Additional */}
                                <article className="neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 cursor-pointer group">
                                        <div className="flex flex-col h-full space-y-4">
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="w-12 h-12 bg-gradient-to-br from-orange-600/20 to-red-600/20 rounded-xl flex items-center justify-center text-2xl">
                                                                        🔍
                                                                </div>
                                                                <span className="bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-xs font-medium">
                                                                        Transfer
                                                                </span>
                                                        </div>
                                                        <div className="w-8 h-8 bg-gradient-to-r from-red-600 to-yellow-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                ARS
                                                        </div>
                                                </div>

                                                <div className="flex-1 space-y-3">
                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                Arsenal Eyes Brazilian Wonder Kid Signing
                                                        </h3>

                                                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                                                                Arsenal scouts are closely monitoring 18-year-old Brazilian midfielder ahead of summer transfer window. The talented youngster has impressed in domestic league.
                                                        </p>

                                                        <div className="flex items-center justify-between pt-3 border-t border-gray-700/30">
                                                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                                        <span>Lucas Silva</span>
                                                                        <span>•</span>
                                                                        <span>12 hours ago</span>
                                                                </div>
                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
                                                                        Read →
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>
                                </article>

                                {/* News Article 6 - Additional */}
                                <article className="neomorphism-card p-6 hover:scale-[1.02] transition-all duration-300 cursor-pointer group">
                                        <div className="flex flex-col h-full space-y-4">
                                                <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                                <div className="w-12 h-12 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-xl flex items-center justify-center text-2xl">
                                                                        🏆
                                                                </div>
                                                                <span className="bg-blue-500/10 text-blue-400 px-3 py-1 rounded-full text-xs font-medium">
                                                                        League
                                                                </span>
                                                        </div>
                                                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                                                                UCL
                                                        </div>
                                                </div>

                                                <div className="flex-1 space-y-3">
                                                        <h3 className="text-lg font-bold text-gray-200 line-clamp-2 group-hover:text-purple-300 transition-colors">
                                                                Champions League Semi-Final Draw Results
                                                        </h3>

                                                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                                                                UEFA reveals Champions League semi-final matchups with exciting clashes between Europe's top teams. The draw promises thrilling encounters ahead.
                                                        </p>

                                                        <div className="flex items-center justify-between pt-3 border-t border-gray-700/30">
                                                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                                        <span>UEFA Sports</span>
                                                                        <span>•</span>
                                                                        <span>1 day ago</span>
                                                                </div>
                                                                <button className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
                                                                        Read →
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>
                                </article>
                        </div>

                        {/* Simple CTA */}
                        <div className="text-center">
                                <button className="futuristic-button px-8 py-3 rounded-xl font-medium neon-glow-cyan">
                                        View All News
                                </button>
                        </div>
                </section>
        );
};

export default LastNews;
