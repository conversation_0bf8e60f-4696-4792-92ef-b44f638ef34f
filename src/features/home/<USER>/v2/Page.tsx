import React from 'react';
// Removed Header and Footer imports - these are handled by root layout
import Breadcrumbs from '@/shared/components/navigation/breadcrumbs/v2';
import { Hero, FeaturedMatches, LastNews, LiveStatistics } from '../../components';
import { generateWebSiteSchema } from '@/lib/seo/structured-data';



const HomePage = () => {
        // Schema.org structured data for the homepage
        const websiteSchema = generateWebSiteSchema();

        return (
                <>
                        {/* JSON-LD Structured Data */}
                        <script
                                type="application/ld+json"
                                dangerouslySetInnerHTML={{
                                        __html: JSON.stringify(websiteSchema)
                                }}
                        />

                        {/* Breadcrumb Navigation */}
                        <nav aria-label="Breadcrumb navigation">
                                <Breadcrumbs
                                        items={[
                                                { label: 'Home', href: '/', isActive: true }
                                        ]}
                                />
                        </nav>

                        {/* Hero Section - Primary Content */}
                        <Hero />

                        {/* Featured Matches Section */}
                        <section aria-labelledby="matches-heading">
                                <h2 id="matches-heading" className="sr-only">Featured Matches</h2>
                                <FeaturedMatches />
                        </section>

                        {/* Latest News Section - Complementary Content */}
                        <aside aria-labelledby="news-heading">
                                <h2 id="news-heading" className="sr-only">Latest Sports News</h2>
                                <LastNews />
                        </aside>

                        {/* Live Statistics Section - Currently Hidden */}
                        {/*
          <section aria-labelledby="stats-heading" role="complementary">
            <SectionWrapper
              id="live-statistics"
              ariaLabelledBy="stats-heading"
              variant="feature"
              spacing="lg"
            >
              <h2 id="stats-heading" className="sr-only">Live Sports Statistics</h2>
              <LiveStatistics />
            </SectionWrapper>
          </section>
          */}
                </>
        );
};

export default HomePage;