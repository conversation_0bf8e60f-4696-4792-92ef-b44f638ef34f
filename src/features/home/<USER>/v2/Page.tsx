import React from 'react';
import dynamic from 'next/dynamic';
// Removed Header and <PERSON>er imports - these are handled by root layout
import SectionWrapper from '@/shared/components/layout/section-wrapper/v1';
import Breadcrumbs from '@/shared/components/navigation/breadcrumbs/v2';
// Direct imports for better tree-shaking
import Hero from '../../components/hero';
import FeaturedMatches from '../../components/featured-matches';
import { generateWebSiteSchema } from '@/lib/seo/structured-data';
import { ImagePreloader } from '@/shared/components/performance/ImagePreloader';
import { PerformanceMonitor } from '@/shared/components/performance/PerformanceMonitor';
import { PerformanceBudget, AdaptiveContent } from '@/shared/components/performance/PerformanceBudget';

// Lazy load non-critical components for better performance
const LastNews = dynamic(() => import('../../components/last-news'), {
        loading: () => (
                <div className="animate-pulse bg-slate-800/50 rounded-lg p-8 mx-auto max-w-7xl">
                        <div className="h-8 bg-slate-700/50 rounded w-1/3 mb-6"></div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {[...Array(6)].map((_, i) => (
                                        <div key={i} className="bg-slate-700/30 rounded-lg p-4">
                                                <div className="h-48 bg-slate-600/50 rounded mb-4"></div>
                                                <div className="h-4 bg-slate-600/50 rounded mb-2"></div>
                                                <div className="h-4 bg-slate-600/50 rounded w-2/3"></div>
                                        </div>
                                ))}
                        </div>
                </div>
        ),
        ssr: true // Keep SSR for SEO
});

const LiveStatistics = dynamic(() => import('../../components/live-statistics'), {
        loading: () => (
                <div className="animate-pulse bg-slate-800/50 rounded-lg p-6">
                        <div className="h-6 bg-slate-700/50 rounded w-1/4 mb-4"></div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {[...Array(4)].map((_, i) => (
                                        <div key={i} className="bg-slate-700/30 rounded p-3">
                                                <div className="h-8 bg-slate-600/50 rounded mb-2"></div>
                                                <div className="h-4 bg-slate-600/50 rounded"></div>
                                        </div>
                                ))}
                        </div>
                </div>
        ),
        ssr: true // Keep SSR for Next.js 15 compatibility
});

const HomePage = () => {
        // Schema.org structured data for the homepage
        const websiteSchema = generateWebSiteSchema();

        // Critical images for preloading
        const criticalImages = [
                'https://logos-world.net/wp-content/uploads/2020/06/Chelsea-Logo.png',
                'https://logos-world.net/wp-content/uploads/2020/06/Manchester-City-Logo.png',
                'https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png',
                'https://logos-world.net/wp-content/uploads/2020/06/Arsenal-Logo.png'
        ];

        return (
                <>
                        {/* Performance monitoring and optimization */}
                        <PerformanceMonitor />
                        <ImagePreloader images={criticalImages} priority={true} />

                        {/* JSON-LD Structured Data */}
                        <script
                                type="application/ld+json"
                                dangerouslySetInnerHTML={{
                                        __html: JSON.stringify(websiteSchema)
                                }}
                        />

                        {/* Breadcrumb Navigation */}
                        <nav role="navigation" aria-label="Breadcrumb navigation">
                                <SectionWrapper
                                        id="breadcrumbs-section"
                                        variant="content"
                                        spacing="none"
                                >
                                        <Breadcrumbs
                                                items={[
                                                        { label: 'Home', href: '/', isActive: true }
                                                ]}
                                        />
                                </SectionWrapper>
                        </nav>

                        {/* Hero Section - Primary Content with Performance Budget */}
                        <section aria-labelledby="hero-heading" role="banner">
                                <h1 id="hero-heading" className="sr-only">Sports Command Center - Live Football Dashboard</h1>
                                <PerformanceBudget
                                        budgetThreshold={150}
                                        fallbackContent={
                                                <div className="min-h-[400px] bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 rounded-lg flex items-center justify-center">
                                                        <div className="text-center text-white">
                                                                <h2 className="text-2xl font-bold mb-2">Sports Dashboard</h2>
                                                                <p className="text-gray-300">Optimized for your device</p>
                                                        </div>
                                                </div>
                                        }
                                >
                                        <Hero />
                                </PerformanceBudget>
                        </section>

                        {/* Featured Matches Section - Main Content */}
                        <section aria-labelledby="matches-heading" role="main">
                                <h2 id="matches-heading" className="sr-only">Featured Matches</h2>
                                <FeaturedMatches />
                        </section>

                        {/* Latest News Section - Complementary Content with Adaptive Loading */}
                        <section aria-labelledby="news-heading" role="complementary">
                                <SectionWrapper
                                        id="latest-news"
                                        ariaLabelledBy="news-heading"
                                        variant="content"
                                        spacing="none"
                                        className="professional-section-bg"
                                >
                                        <h2 id="news-heading" className="sr-only">Latest Sports News</h2>
                                        <AdaptiveContent
                                                threshold={100}
                                                lightContent={
                                                        <div className="bg-gray-800/50 rounded-lg p-6 text-center">
                                                                <h3 className="text-lg font-semibold text-white mb-2">Latest News</h3>
                                                                <p className="text-gray-400">Loading optimized content...</p>
                                                        </div>
                                                }
                                        >
                                                <LastNews />
                                        </AdaptiveContent>
                                </SectionWrapper>
                        </section>

                        {/* Live Statistics Section - Currently Hidden */}
                        {/*
          <section aria-labelledby="stats-heading" role="complementary">
            <SectionWrapper
              id="live-statistics"
              ariaLabelledBy="stats-heading"
              variant="feature"
              spacing="lg"
            >
              <h2 id="stats-heading" className="sr-only">Live Sports Statistics</h2>
              <LiveStatistics />
            </SectionWrapper>
          </section>
          */}
                </>
        );
};

export default HomePage;