import React from 'react';
// Removed Header and Footer imports - these are handled by root layout
import SectionWrapper from '@/shared/components/layout/section-wrapper/v1';
import Breadcrumbs from '@/shared/components/navigation/breadcrumbs/v2';
import { Hero, FeaturedMatches, LastNews, LiveStatistics } from '../../components';
import { generateWebSiteSchema } from '@/lib/seo/structured-data';



const HomePage = () => {
        // Schema.org structured data for the homepage
        const websiteSchema = generateWebSiteSchema();

        return (
                <>
                        {/* JSON-LD Structured Data */}
                        <script
                                type="application/ld+json"
                                dangerouslySetInnerHTML={{
                                        __html: JSON.stringify(websiteSchema)
                                }}
                        />

                        {/* Breadcrumb Navigation */}
                        <nav role="navigation" aria-label="Breadcrumb navigation">
                                <SectionWrapper
                                        id="breadcrumbs-section"
                                        variant="content"
                                        spacing="none"
                                >
                                        <Breadcrumbs
                                                items={[
                                                        { label: 'Home', href: '/', isActive: true }
                                                ]}
                                        />
                                </SectionWrapper>
                        </nav>

                        {/* Hero Section - Primary Content */}
                        <section aria-labelledby="hero-heading" role="banner">
                                <h1 id="hero-heading" className="sr-only">Sports Command Center - Live Football Dashboard</h1>
                                <Hero />
                        </section>

                        {/* Featured Matches Section - Main Content */}
                        <section aria-labelledby="matches-heading" role="main">
                                <h2 id="matches-heading" className="sr-only">Featured Matches</h2>
                                <FeaturedMatches />
                        </section>

                        {/* Latest News Section - Complementary Content */}
                        <section aria-labelledby="news-heading" role="complementary">
                                <SectionWrapper
                                        id="latest-news"
                                        ariaLabelledBy="news-heading"
                                        variant="content"
                                        spacing="none"
                                        className="professional-section-bg"
                                >
                                        <h2 id="news-heading" className="sr-only">Latest Sports News</h2>
                                        <LastNews />
                                </SectionWrapper>
                        </section>

                        {/* Live Statistics Section - Currently Hidden */}
                        {/*
          <section aria-labelledby="stats-heading" role="complementary">
            <SectionWrapper
              id="live-statistics"
              ariaLabelledBy="stats-heading"
              variant="feature"
              spacing="lg"
            >
              <h2 id="stats-heading" className="sr-only">Live Sports Statistics</h2>
              <LiveStatistics />
            </SectionWrapper>
          </section>
          */}
                </>
        );
};

export default HomePage;