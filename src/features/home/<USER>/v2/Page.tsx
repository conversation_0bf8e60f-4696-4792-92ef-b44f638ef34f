import React from 'react';
import Header from '@/shared/components/layout/header';
import Footer from '@/shared/components/layout/footer';
import MainContent from '@/shared/components/layout/main-content/v1';
import SectionWrapper from '@/shared/components/layout/section-wrapper/v1';
import PageWrapper from '@/shared/components/layout/page-wrapper/v2';
import SkipLink from '@/shared/components/layout/skip-link/v1';
import Breadcrumbs from '@/shared/components/navigation/breadcrumbs/v2';
import { Hero, FeaturedMatches, LastNews, LiveStatistics } from '../../components';
import { generateWebSiteSchema } from '@/lib/seo/structured-data';

const HomePage = () => {
        // Schema.org structured data for the homepage
        const websiteSchema = generateWebSiteSchema();

        return (
                <>
                        {/* JSON-LD Structured Data */}
                        <script
                                type="application/ld+json"
                                dangerouslySetInnerHTML={{
                                        __html: JSON.stringify(websiteSchema)
                                }}
                        />

                        <PageWrapper pageType="home">
                                <SkipLink href="#main-content" />

                                <Header />

                                <MainContent id="main-content" className="hero-fullwidth-container">
                                        {/* Breadcrumbs */}
                                        <SectionWrapper
                                                id="breadcrumbs-section"
                                                variant="content"
                                                spacing="none"
                                        >
                                                <Breadcrumbs
                                                        items={[
                                                                { label: 'Home', href: '/', isActive: true }
                                                        ]}
                                                />
                                        </SectionWrapper>

                                        {/* Hero Section - Full Width */}
                                        <div className="sr-only">
                                                <h1 id="hero-heading">Sports Command Center - Live Football Dashboard</h1>
                                        </div>
                                        <Hero />                        {/* Featured Matches Section - Full Width Implementation */}
                                        <div className="sr-only">
                                                <h2 id="matches-heading">Home Page Matches</h2>
                                        </div>
                                        <FeaturedMatches />

                                        {/* Latest News Section */}
                                        <SectionWrapper
                                                id="latest-news"
                                                ariaLabelledBy="news-heading"
                                                variant="content"
                                                spacing="none"
                                                className="professional-section-bg"
                                        >
                                                <div className="sr-only">
                                                        <h2 id="news-heading">Latest Sports News</h2>
                                                </div>
                                                <LastNews />
                                        </SectionWrapper>

                                        {/* Live Statistics Section - Currently Hidden */}
                                        {/* 
          <SectionWrapper
            id="live-statistics"
            ariaLabelledBy="stats-heading"
            variant="feature"
            spacing="lg"
          >
            <div className="sr-only">
              <h2 id="stats-heading">Live Sports Statistics</h2>
            </div>
            <LiveStatistics />
          </SectionWrapper>
          */}
                                </MainContent>

                                <Footer />
                        </PageWrapper>
                </>
        );
};

export default HomePage;