import Header from '@/shared/components/layout/header';
import Footer from '@/shared/components/layout/footer';
import Body from '@/shared/components/layout/body_old';
import { Hero, FeaturedMatches, LastNews, LiveStatistics } from '../../components';

const HomePage = () => (
        <div className="min-h-screen flex flex-col">
                <Header />

                <main className="flex-1">
                        {/* Hero Section with margin-top to avoid header overlap */}
                        <section className="pt-16" aria-label="Hero section">
                                <Hero />
                        </section>

                        <Body>
                                {/* Home Feature Sections */}
                                {/* Live Statistics Dashboard - Currently Hidden */}
                                {/* <section aria-label="Live statistics">
                                        <LiveStatistics />
                                </section> */}

                                {/* Featured Matches Section */}
                                <section aria-label="Featured matches">
                                        <FeaturedMatches />
                                </section>

                                {/* Last News Section */}
                                <section aria-label="Latest news">
                                        <LastNews />
                                </section>
                        </Body>
                </main>

                <Footer />
        </div>
);

export default HomePage;
