// imageUtils.ts - Utility functions for handling images with CDN support

/**
 * Builds a properly formatted image URL with CDN support
 * @param imagePath - The path to the image
 * @returns Properly formatted image URL
 */
export const buildImageUrl = (imagePath: string): string => {
        if (!imagePath) return 'public/images/placeholder-team.png'; // Default placeholder if no path

        // If path already includes http(s), return as is (already full URL)
        if (imagePath.startsWith('http')) return imagePath;

        // Get CDN domain from environment variable
        const CDN_DOMAIN = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || "";

        // If we don't have a CDN domain, return the path as is
        if (!CDN_DOMAIN) return imagePath;

        // If CDN_DOMAIN ends with slash or imagePath starts with slash, prevent double slash
        if (CDN_DOMAIN.endsWith('/') && imagePath.startsWith('/')) {
                return `${CDN_DOMAIN}${imagePath.substring(1)}`;
        }
        // If neither has slash, add one
        else if (!CDN_DOMAIN.endsWith('/') && !imagePath.startsWith('/')) {
                return `${CDN_DOMAIN}/${imagePath}`;
        }
        // Otherwise, just combine them
        return `${CDN_DOMAIN}${imagePath}`;
};

/**
 * Image error handler - shows team initials instead of broken image
 * @param e - Error event
 * @param teamName - Team name to show initials for
 */
export const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>, teamName: string) => {
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';

        // If the parent element exists, set its content to the team initials
        if (target.parentElement) {
                target.parentElement.innerHTML = teamName.substring(0, 3).toUpperCase();
        }
};
