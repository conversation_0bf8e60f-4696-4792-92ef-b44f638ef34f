/**
 * Touch Optimization Utilities
 * Provides utilities for better touch interactions and mobile UX
 */

// Minimum touch target size (44px recommended by Apple/Google)
export const MIN_TOUCH_TARGET = 44;

/**
 * Check if device supports touch
 */
export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

/**
 * Get optimal touch target classes based on element type
 */
export const getTouchTargetClasses = (elementType: 'button' | 'link' | 'icon' | 'card' = 'button'): string => {
  const baseClasses = 'select-none';

  switch (elementType) {
    case 'button':
      return `${baseClasses} min-h-[44px] min-w-[44px] flex items-center justify-center`;
    case 'link':
      return `${baseClasses} min-h-[44px] inline-flex items-center`;
    case 'icon':
      return `${baseClasses} min-h-[44px] min-w-[44px] flex items-center justify-center p-2`;
    case 'card':
      return `${baseClasses} cursor-pointer`;
    default:
      return baseClasses;
  }
};

/**
 * Add touch feedback classes
 */
export const getTouchFeedbackClasses = (type: 'subtle' | 'medium' | 'strong' = 'medium'): string => {
  const baseClasses = 'transition-all duration-150 ease-out';

  switch (type) {
    case 'subtle':
      return `${baseClasses} active:scale-[0.98] active:opacity-90`;
    case 'medium':
      return `${baseClasses} active:scale-[0.95] active:opacity-80 hover:scale-[1.02]`;
    case 'strong':
      return `${baseClasses} active:scale-[0.92] active:opacity-70 hover:scale-[1.05]`;
    default:
      return baseClasses;
  }
};

/**
 * Debounce function for touch events
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for scroll/touch events
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Handle touch ripple effect
 */
export const createRippleEffect = (event: React.MouseEvent | React.TouchEvent, element: HTMLElement) => {
  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = ('touches' in event ? event.touches[0].clientX : event.clientX) - rect.left - size / 2;
  const y = ('touches' in event ? event.touches[0].clientY : event.clientY) - rect.top - size / 2;

  const ripple = document.createElement('span');
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    left: ${x}px;
    top: ${y}px;
    width: ${size}px;
    height: ${size}px;
    pointer-events: none;
  `;

  element.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
};

/**
 * Detect swipe gestures
 */
export interface SwipeConfig {
  threshold?: number;
  restraint?: number;
  allowedTime?: number;
}

export const useSwipeDetection = (
  element: HTMLElement | null,
  onSwipe: (direction: 'left' | 'right' | 'up' | 'down') => void,
  config: SwipeConfig = {}
) => {
  const { threshold = 100, restraint = 100, allowedTime = 300 } = config;

  let startX = 0;
  let startY = 0;
  let startTime = 0;

  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    startX = touch.clientX;
    startY = touch.clientY;
    startTime = Date.now();
  };

  const handleTouchEnd = (e: TouchEvent) => {
    const touch = e.changedTouches[0];
    const distX = touch.clientX - startX;
    const distY = touch.clientY - startY;
    const elapsedTime = Date.now() - startTime;

    if (elapsedTime <= allowedTime) {
      if (Math.abs(distX) >= threshold && Math.abs(distY) <= restraint) {
        onSwipe(distX < 0 ? 'left' : 'right');
      } else if (Math.abs(distY) >= threshold && Math.abs(distX) <= restraint) {
        onSwipe(distY < 0 ? 'up' : 'down');
      }
    }
  };

  if (element) {
    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }

  return () => { };
};

/**
 * Optimize button for touch
 */
export const optimizeButtonForTouch = (className: string = ''): string => {
  return `${getTouchTargetClasses('button')} ${getTouchFeedbackClasses('medium')} ${className}`.trim();
};

/**
 * Optimize link for touch
 */
export const optimizeLinkForTouch = (className: string = ''): string => {
  return `${getTouchTargetClasses('link')} ${getTouchFeedbackClasses('subtle')} ${className}`.trim();
};

/**
 * Optimize card for touch
 */
export const optimizeCardForTouch = (className: string = ''): string => {
  return `${getTouchTargetClasses('card')} ${getTouchFeedbackClasses('subtle')} ${className}`.trim();
};
