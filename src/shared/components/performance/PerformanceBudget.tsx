"use client";

import React, { useEffect, useState } from 'react';
import { useDeviceCapabilities } from '@/shared/hooks/useMobileDetection';

interface PerformanceBudgetProps {
  children: React.ReactNode;
  fallbackContent?: React.ReactNode;
  budgetThreshold?: number; // in milliseconds
}

interface PerformanceMetrics {
  memoryUsage?: number;
  renderTime?: number;
  isLowEndDevice?: boolean;
}

/**
 * Component that adapts content based on device performance budget
 */
export const PerformanceBudget: React.FC<PerformanceBudgetProps> = ({
  children,
  fallbackContent,
  budgetThreshold = 100
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [shouldShowFallback, setShouldShowFallback] = useState(false);
  const { connectionSpeed, prefersReducedMotion } = useDeviceCapabilities();

  useEffect(() => {
    const measurePerformance = () => {
      const startTime = performance.now();
      
      // Estimate device performance
      const isLowEndDevice = detectLowEndDevice();
      
      // Memory usage (if available)
      let memoryUsage = 0;
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      }

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      const newMetrics = {
        memoryUsage,
        renderTime,
        isLowEndDevice
      };

      setMetrics(newMetrics);

      // Decide whether to show fallback
      const shouldUseFallback = 
        isLowEndDevice || 
        renderTime > budgetThreshold ||
        connectionSpeed === 'slow' ||
        memoryUsage > 0.8;

      setShouldShowFallback(shouldUseFallback);
    };

    // Measure after a short delay to allow for initial render
    const timer = setTimeout(measurePerformance, 100);
    return () => clearTimeout(timer);
  }, [budgetThreshold, connectionSpeed]);

  if (shouldShowFallback && fallbackContent) {
    return <>{fallbackContent}</>;
  }

  return <>{children}</>;
};

/**
 * Detect if device is low-end based on various heuristics
 */
const detectLowEndDevice = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Check hardware concurrency (CPU cores)
  const cores = navigator.hardwareConcurrency || 1;
  if (cores <= 2) return true;

  // Check memory (if available)
  if ('deviceMemory' in navigator) {
    const memory = (navigator as any).deviceMemory;
    if (memory <= 2) return true; // 2GB or less
  }

  // Check connection type
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    if (connection && ['slow-2g', '2g'].includes(connection.effectiveType)) {
      return true;
    }
  }

  // Check user agent for low-end indicators
  const userAgent = navigator.userAgent.toLowerCase();
  const lowEndIndicators = [
    'android 4', 'android 5', 'android 6',
    'iphone os 9', 'iphone os 10', 'iphone os 11',
    'windows phone', 'blackberry', 'opera mini'
  ];

  return lowEndIndicators.some(indicator => userAgent.includes(indicator));
};

/**
 * Hook for performance-aware rendering
 */
export const usePerformanceBudget = (threshold: number = 100) => {
  const [isLowPerformance, setIsLowPerformance] = useState(false);
  const { connectionSpeed } = useDeviceCapabilities();

  useEffect(() => {
    const checkPerformance = () => {
      const startTime = performance.now();
      
      // Simulate a small workload
      for (let i = 0; i < 1000; i++) {
        Math.random();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      const isLowEnd = 
        detectLowEndDevice() ||
        duration > threshold ||
        connectionSpeed === 'slow';
        
      setIsLowPerformance(isLowEnd);
    };

    checkPerformance();
  }, [threshold, connectionSpeed]);

  return {
    isLowPerformance,
    shouldReduceAnimations: isLowPerformance,
    shouldReduceImages: isLowPerformance,
    shouldReduceEffects: isLowPerformance
  };
};

/**
 * Component for adaptive image loading
 */
interface AdaptiveImageProps {
  src: string;
  lowQualitySrc?: string;
  alt: string;
  className?: string;
  priority?: boolean;
}

export const AdaptiveImage: React.FC<AdaptiveImageProps> = ({
  src,
  lowQualitySrc,
  alt,
  className = '',
  priority = false
}) => {
  const { isLowPerformance } = usePerformanceBudget();
  const { connectionSpeed } = useDeviceCapabilities();

  const shouldUseLowQuality = 
    isLowPerformance || 
    connectionSpeed === 'slow' ||
    !priority;

  const imageSrc = shouldUseLowQuality && lowQualitySrc ? lowQualitySrc : src;

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      loading={priority ? 'eager' : 'lazy'}
      decoding="async"
    />
  );
};

/**
 * Component for adaptive content loading
 */
interface AdaptiveContentProps {
  children: React.ReactNode;
  lightContent?: React.ReactNode;
  threshold?: number;
}

export const AdaptiveContent: React.FC<AdaptiveContentProps> = ({
  children,
  lightContent,
  threshold = 50
}) => {
  const { isLowPerformance } = usePerformanceBudget(threshold);

  if (isLowPerformance && lightContent) {
    return <>{lightContent}</>;
  }

  return <>{children}</>;
};

export default PerformanceBudget;
