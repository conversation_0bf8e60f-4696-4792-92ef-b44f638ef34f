"use client";

import { useEffect } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  cls?: number; // Cumulative Layout Shift
  fid?: number; // First Input Delay
  ttfb?: number; // Time to First Byte
}

/**
 * PerformanceMonitor Component
 * Monitors Core Web Vitals and reports performance metrics
 */
export const PerformanceMonitor: React.FC = () => {
  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') return;

    const metrics: PerformanceMetrics = {};

    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              metrics.fcp = entry.startTime;
            }
            break;
          case 'largest-contentful-paint':
            metrics.lcp = entry.startTime;
            break;
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              metrics.cls = (metrics.cls || 0) + (entry as any).value;
            }
            break;
          case 'first-input':
            metrics.fid = (entry as any).processingStart - entry.startTime;
            break;
          case 'navigation':
            const navEntry = entry as PerformanceNavigationTiming;
            metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
            break;
        }
      }
    });

    // Observe different entry types
    try {
      observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift', 'first-input', 'navigation'] });
    } catch (e) {
      // Fallback for browsers that don't support all entry types
      console.warn('Some performance metrics not supported:', e);
    }

    // Report metrics after page load
    const reportMetrics = () => {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.group('🚀 Performance Metrics');
        console.log('First Contentful Paint:', metrics.fcp ? `${metrics.fcp.toFixed(2)}ms` : 'N/A');
        console.log('Largest Contentful Paint:', metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'N/A');
        console.log('Cumulative Layout Shift:', metrics.cls ? metrics.cls.toFixed(4) : 'N/A');
        console.log('First Input Delay:', metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'N/A');
        console.log('Time to First Byte:', metrics.ttfb ? `${metrics.ttfb.toFixed(2)}ms` : 'N/A');
        console.groupEnd();
      }

      // Send to analytics in production
      if (process.env.NODE_ENV === 'production' && window.gtag) {
        Object.entries(metrics).forEach(([key, value]) => {
          if (value !== undefined) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Performance',
              event_label: key.toUpperCase(),
              value: Math.round(value),
              non_interaction: true,
            });
          }
        });
      }
    };

    // Report after a delay to capture all metrics
    setTimeout(reportMetrics, 3000);

    return () => {
      observer.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
};

/**
 * Hook for performance monitoring
 */
export const usePerformanceMonitor = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏱️ Component lifecycle: ${duration.toFixed(2)}ms`);
      }
    };
  }, []);
};

export default PerformanceMonitor;
