"use client";

import { useEffect } from 'react';

interface ImagePreloaderProps {
  images: string[];
  priority?: boolean;
}

/**
 * ImagePreloader Component
 * Preloads critical images for better performance
 * Uses intersection observer for non-critical images
 */
export const ImagePreloader: React.FC<ImagePreloaderProps> = ({ 
  images, 
  priority = false 
}) => {
  useEffect(() => {
    if (priority) {
      // Preload critical images immediately
      images.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    } else {
      // Lazy preload non-critical images
      const preloadImage = (src: string) => {
        const img = new Image();
        img.src = src;
      };

      // Use requestIdleCallback if available, otherwise setTimeout
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          images.forEach(preloadImage);
        });
      } else {
        setTimeout(() => {
          images.forEach(preloadImage);
        }, 100);
      }
    }
  }, [images, priority]);

  return null; // This component doesn't render anything
};

/**
 * Hook for preloading images
 */
export const useImagePreloader = (images: string[], priority = false) => {
  useEffect(() => {
    if (priority) {
      images.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    } else {
      const preloadImage = (src: string) => {
        const img = new Image();
        img.src = src;
      };

      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          images.forEach(preloadImage);
        });
      } else {
        setTimeout(() => {
          images.forEach(preloadImage);
        }, 100);
      }
    }
  }, [images, priority]);
};

export default ImagePreloader;
