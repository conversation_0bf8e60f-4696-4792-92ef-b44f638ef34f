import React from 'react';

interface SkipLinkProps {
        href: string;
        children?: React.ReactNode;
        className?: string;
}

const SkipLink: React.FC<SkipLinkProps> = ({
        href,
        children = 'Skip to main content',
        className = ''
}) => (
        <a
                href={href}
                className={`
      skip-link absolute top-0 left-0 z-[9999] 
      bg-blue-600 text-white px-4 py-2 rounded-br 
      transform -translate-y-full focus:translate-y-0 
      transition-transform duration-300 ease-in-out
      focus:outline-none focus:ring-2 focus:ring-blue-400
      ${className}
    `}
        >
                {children}
        </a>
);

export default SkipLink;