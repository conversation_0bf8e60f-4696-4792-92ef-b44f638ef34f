import React from 'react';

interface PageWrapperProps {
        children: React.ReactNode;
        pageType?: 'home' | 'news' | 'matches' | 'default';
        className?: string;
        skipLinkTarget?: string;
        lang?: string;
}

const PageWrapper: React.FC<PageWrapperProps> = ({
        children,
        pageType = 'default',
        className = '',
        skipLinkTarget = '#main-content',
        lang = 'en'
}) => {
        // Get page-specific classes
        const getPageClasses = () => {
                const baseClasses = 'min-h-screen flex flex-col';
                const pageClasses = {
                        home: 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900',
                        news: 'bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900',
                        matches: 'bg-gradient-to-br from-slate-900 via-green-900 to-slate-900',
                        default: 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900'
                };

                return `${baseClasses} ${pageClasses[pageType]} ${className}`;
        };

        return (
                <div className={getPageClasses()} lang={lang}>
                        <div className="relative min-h-screen overflow-hidden">
                                {/* Dynamic Particle System */}
                                <div className="fixed inset-0 pointer-events-none z-0">
                                        {Array.from({ length: 15 }).map((_, i) => (
                                                <div
                                                        key={i}
                                                        className="absolute w-2 h-2 bg-gradient-to-r from-purple-500/30 to-cyan-500/30 rounded-full animate-pulse float-animation"
                                                        style={{
                                                                left: `${Math.random() * 100}%`,
                                                                top: `${Math.random() * 100}%`,
                                                                animationDelay: `${Math.random() * 6}s`,
                                                                animationDuration: `${6 + Math.random() * 4}s`,
                                                        }}
                                                />
                                        ))}
                                </div>

                                {/* Background Effects */}
                                <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/40 to-black"></div>
                                <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>

                                {/* Floating Orbs for Atmosphere */}
                                <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
                                <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
                                <div className="absolute top-3/4 left-1/3 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>

                                {/* Content */}
                                <div className="relative z-10">
                                        {children}
                                </div>
                        </div>
                </div>
        );
};

export default PageWrapper;