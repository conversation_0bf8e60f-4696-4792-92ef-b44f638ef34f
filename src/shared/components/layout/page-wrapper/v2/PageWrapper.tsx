import React from 'react';

interface PageWrapperProps {
        children: React.ReactNode;
        pageType?: 'home' | 'news' | 'matches' | 'default';
        className?: string;
        skipLinkTarget?: string;
        lang?: string;
}

const PageWrapper: React.FC<PageWrapperProps> = ({
        children,
        pageType = 'default',
        className = '',
        skipLinkTarget = '#main-content',
        lang = 'en'
}) => {
        // Get page-specific classes
        const getPageClasses = () => {
                const baseClasses = 'min-h-screen flex flex-col';
                const pageClasses = {
                        home: 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900',
                        news: 'bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900',
                        matches: 'bg-gradient-to-br from-slate-900 via-green-900 to-slate-900',
                        default: 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900'
                };

                return `${baseClasses} ${pageClasses[pageType]} ${className}`;
        };

        return (
                <div className={getPageClasses()} lang={lang}>
                        {/* Content - Removed duplicate particle system and background effects */}
                        {/* These are now handled by the root layout.tsx */}
                        <div className="relative z-10">
                                {children}
                        </div>
                </div>
        );
};

export default PageWrapper;