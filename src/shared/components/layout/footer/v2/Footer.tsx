"use client";

import React, { useState, useEffect } from 'react';

const Footer = () => {
        const [currentTime, setCurrentTime] = useState(new Date());

        useEffect(() => {
                const timer = setInterval(() => {
                        setCurrentTime(new Date());
                }, 1000);
                return () => clearInterval(timer);
        }, []);

        return (
                <footer
                        className="relative overflow-hidden"
                        role="contentinfo"
                        aria-label="Site footer with company information and navigation"
                >
                        {/* Background Effects */}
                        <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-800/80 to-transparent"></div>
                        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>

                        {/* Animated Background Orbs */}
                        <div className="absolute top-0 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
                        <div className="absolute bottom-0 right-1/4 w-48 h-48 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

                        <div className="relative z-10 container mx-auto px-6 py-16">
                                {/* Main Footer Content */}
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                                        {/* Brand Section */}
                                        <section aria-labelledby="brand-heading">
                                                <header className="flex items-center gap-3 mb-6">
                                                        <div className="w-12 h-12 neomorphism-card flex items-center justify-center neon-glow-purple">
                                                                <span className="text-2xl" aria-hidden="true">⚡</span>
                                                        </div>
                                                        <div>
                                                                <h3 id="brand-heading" className="text-2xl font-bold gradient-text">SPORTS COMMAND</h3>
                                                                <p className="text-xs text-gray-400 uppercase tracking-wider">CENTER</p>
                                                        </div>
                                                </header>
                                                <p className="text-gray-400 mb-6 leading-relaxed">
                                                        Advanced sports management platform with real-time analytics, live match tracking, and comprehensive team statistics.
                                                </p>
                                                <div className="neomorphism-card p-4 rounded-xl">
                                                        <div className="flex items-center gap-2 mb-2">
                                                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" aria-hidden="true"></div>
                                                                <span className="text-green-400 text-sm font-medium">System Status: Online</span>
                                                        </div>
                                                        <p className="text-xs text-gray-500">
                                                                Last updated: <time dateTime={currentTime.toISOString()}>{currentTime.toLocaleTimeString('vi-VN')}</time>
                                                        </p>
                                                </div>
                                        </section>

                                        {/* Navigation Links */}
                                        <section aria-labelledby="nav-heading">
                                                <h4 id="nav-heading" className="text-lg font-bold gradient-text mb-6">Navigation</h4>
                                                <nav aria-label="Footer navigation menu">
                                                        <ul className="space-y-3" role="list">
                                                                {[
                                                                        { label: 'Dashboard', icon: '🏠', href: '/' },
                                                                        { label: 'Live Matches', icon: '⚽', href: '/fixtures' },
                                                                        { label: 'Analytics', icon: '📊', href: '/analytics' },
                                                                        { label: 'Teams', icon: '👥', href: '/teams' },
                                                                        { label: 'News', icon: '📰', href: '/news' }
                                                                ].map((item) => (
                                                                        <li key={item.href}>
                                                                                <a
                                                                                        href={item.href}
                                                                                        className="futuristic-button px-3 py-2 rounded-lg text-sm text-gray-300 hover:text-white transition-all duration-300 flex items-center gap-3 w-full"
                                                                                        aria-label={`Navigate to ${item.label} page`}
                                                                                >
                                                                                        <span aria-hidden="true">{item.icon}</span>
                                                                                        {item.label}
                                                                                </a>
                                                                        </li>
                                                                ))}
                                                        </ul>
                                                </nav>
                                        </section>

                                        {/* Live Statistics */}
                                        <section aria-labelledby="stats-heading">
                                                <h4 id="stats-heading" className="text-lg font-bold gradient-text mb-6">Live Statistics</h4>
                                                <div className="space-y-4" role="region" aria-label="Real-time sports statistics">
                                                        <div className="neomorphism-card p-4 rounded-xl">
                                                                <div className="flex items-center justify-between mb-2">
                                                                        <span className="text-gray-400 text-sm">Active Matches</span>
                                                                        <div className="flex items-center gap-1">
                                                                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" aria-hidden="true"></div>
                                                                                <span className="text-red-400 font-bold" aria-label="8 active matches">8</span>
                                                                        </div>
                                                                </div>
                                                                <div className="w-full bg-slate-700 rounded-full h-1" role="progressbar" aria-valuenow={75} aria-valuemin={0} aria-valuemax={100} aria-label="Match activity level: 75%">
                                                                        <div className="bg-gradient-to-r from-red-500 to-orange-500 h-1 rounded-full w-3/4"></div>
                                                                </div>
                                                        </div>

                                                        <div className="neomorphism-card p-4 rounded-xl">
                                                                <div className="flex items-center justify-between mb-2">
                                                                        <span className="text-gray-400 text-sm">Total Goals Today</span>
                                                                        <span className="text-cyan-400 font-bold" aria-label="47 goals scored today">47</span>
                                                                </div>
                                                                <div className="w-full bg-slate-700 rounded-full h-1" role="progressbar" aria-valuenow={83} aria-valuemin={0} aria-valuemax={100} aria-label="Goals progress: 83%">
                                                                        <div className="bg-gradient-to-r from-cyan-500 to-blue-500 h-1 rounded-full w-5/6"></div>
                                                                </div>
                                                        </div>

                                                        <div className="neomorphism-card p-4 rounded-xl">
                                                                <div className="flex items-center justify-between mb-2">
                                                                        <span className="text-gray-400 text-sm">Active Users</span>
                                                                        <span className="text-purple-400 font-bold" aria-label="12,400 active users">12.4K</span>
                                                                </div>
                                                                <div className="w-full bg-slate-700 rounded-full h-1" role="progressbar" aria-valuenow={80} aria-valuemin={0} aria-valuemax={100} aria-label="User activity: 80%">
                                                                        <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-1 rounded-full w-4/5"></div>
                                                                </div>
                                                        </div>
                                                </div>
                                        </section>

                                        {/* Social & Contact */}
                                        <section aria-labelledby="social-heading">
                                                <h4 id="social-heading" className="text-lg font-bold gradient-text mb-6">Connect With Us</h4>

                                                {/* Social Media Links */}
                                                <nav aria-label="Social media links">
                                                        <ul className="grid grid-cols-2 gap-3 mb-6" role="list">
                                                                {[
                                                                        { name: 'Discord', icon: '💬', color: 'from-purple-500 to-indigo-500', url: '#' },
                                                                        { name: 'Twitter', icon: '🐦', color: 'from-blue-400 to-cyan-500', url: '#' },
                                                                        { name: 'GitHub', icon: '⚡', color: 'from-gray-500 to-slate-600', url: '#' },
                                                                        { name: 'LinkedIn', icon: '💼', color: 'from-blue-600 to-blue-700', url: '#' }
                                                                ].map((social) => (
                                                                        <li key={social.name}>
                                                                                <a
                                                                                        href={social.url}
                                                                                        className={`neomorphism-card p-3 rounded-xl hover:scale-105 transition-all duration-300 text-center bg-gradient-to-br ${social.color} block`}
                                                                                        aria-label={`Visit our ${social.name} page`}
                                                                                >
                                                                                        <div className="text-xl mb-1" aria-hidden="true">{social.icon}</div>
                                                                                        <div className="text-xs text-white font-medium">{social.name}</div>
                                                                                </a>
                                                                        </li>
                                                                ))}
                                                        </ul>
                                                </nav>

                                                {/* Newsletter Signup */}
                                                <div className="neomorphism-card p-4 rounded-xl">
                                                        <h5 className="text-sm font-bold text-gray-200 mb-3">Stay Updated</h5>
                                                        <form aria-label="Newsletter signup" className="flex gap-2">
                                                                <input
                                                                        type="email"
                                                                        placeholder="Enter email"
                                                                        aria-label="Email address for newsletter"
                                                                        className="flex-1 bg-slate-800/50 border border-gray-600 rounded-lg px-3 py-2 text-xs text-gray-300 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                                                                        required
                                                                />
                                                                <button
                                                                        type="submit"
                                                                        className="futuristic-button px-4 py-2 rounded-lg text-xs font-medium neon-glow-purple"
                                                                        aria-label="Subscribe to newsletter"
                                                                >
                                                                        <span aria-hidden="true">⚡</span>
                                                                </button>
                                                        </form>
                                                </div>
                                        </section>
                                </div>

                                {/* Bottom Bar */}
                                <div className="border-t border-gray-700/50 pt-8">
                                        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                                                <div className="flex items-center gap-4">
                                                        <p className="text-sm text-gray-400">
                                                                &copy; <time dateTime={new Date().getFullYear().toString()}>{new Date().getFullYear()}</time> Sports Command Center. All rights reserved.
                                                        </p>
                                                        <div className="hidden md:flex items-center gap-2">
                                                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" aria-hidden="true"></div>
                                                                <span className="text-xs text-green-400">Powered by Next.js</span>
                                                        </div>
                                                </div>

                                                <nav aria-label="Legal and documentation links">
                                                        <ul className="flex items-center gap-6 text-xs text-gray-400" role="list">
                                                                <li><a href="/privacy" className="hover:text-purple-400 transition-colors">Privacy Policy</a></li>
                                                                <li><a href="/terms" className="hover:text-purple-400 transition-colors">Terms of Service</a></li>
                                                                <li><a href="/api" className="hover:text-purple-400 transition-colors">API Docs</a></li>
                                                        </ul>
                                                </nav>
                                        </div>

                                        {/* Performance Indicator */}
                                        <div className="mt-6 flex items-center justify-center">
                                                <div className="neomorphism-card px-6 py-3 rounded-xl flex items-center gap-4" role="status" aria-label="System performance metrics">
                                                        <div className="flex items-center gap-2">
                                                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" aria-hidden="true"></div>
                                                                <span className="text-xs text-green-400 font-medium">Server Status: Optimal</span>
                                                        </div>
                                                        <div className="text-gray-400 text-xs" aria-hidden="true">|</div>
                                                        <div className="text-xs text-gray-400">
                                                                Response Time: <span className="text-cyan-400 font-medium">23ms</span>
                                                        </div>
                                                        <div className="text-gray-400 text-xs" aria-hidden="true">|</div>
                                                        <div className="text-xs text-gray-400">
                                                                Uptime: <span className="text-purple-400 font-medium">99.9%</span>
                                                        </div>
                                                </div>
                                        </div>
                                </div>
                        </div>
                </footer>
        );
};

export default Footer;