/* Header V2 - Football Theme Styles */
.header-v2 {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Enhanced Top Bar with Gradient Animation */
.header-v2-topbar {
        background: linear-gradient(90deg, #059669, #2563eb, #7c3aed, #059669);
        background-size: 300% 100%;
        animation: gradientShift 8s ease infinite;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes gradientShift {

        0%,
        100% {
                background-position: 0% 50%;
        }

        50% {
                background-position: 100% 50%;
        }
}

.header-v2-main {
        background: rgba(15, 23, 42, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(51, 65, 85, 0.5);
        transition: all 0.3s ease;
}

.header-v2-scrolled {
        background: rgba(15, 23, 42, 0.98);
        backdrop-filter: blur(24px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        transform: translateY(0);
}

/* Football Logo Animation - Enhanced */
.football-logo {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
}

.football-logo::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
}

.football-logo:hover {
        transform: rotate(180deg) scale(1.1);
        box-shadow: 0 0 25px rgba(34, 197, 94, 0.5), 0 0 50px rgba(59, 130, 246, 0.3);
}

.football-logo:hover::before {
        opacity: 1;
        transform: rotate(45deg) translate(50%, 50%);
}

/* Navigation Items V2 - Enhanced Hover Effects */
.nav-item-v2 {
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item-v2::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.2), transparent);
        transition: left 0.5s ease;
}

.nav-item-v2:hover::before {
        left: 100%;
}

.nav-item-v2:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

/* Badge Animations */
.badge-v2 {
        position: relative;
        overflow: hidden;
        animation: badgeGlow 2s ease-in-out infinite alternate;
}

@keyframes badgeGlow {
        0% {
                box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        }

        100% {
                box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
        }
}

/* Live Score Ticker - Enhanced Transitions */
.live-scores-ticker {
        overflow: hidden;
        position: relative;
}

.live-score-item {
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: center;
}

.live-score-item.active {
        animation: scoreHighlight 3s ease-in-out;
}

@keyframes scoreHighlight {

        0%,
        100% {
                transform: scale(1);
        }

        50% {
                transform: scale(1.05);
                box-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
        }
}

/* Search Input V2 - Enhanced Focus Effects */
.search-input-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(30, 41, 59, 0.8);
        position: relative;
}

.search-input-v2::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
}

.search-input-v2:focus {
        background: rgba(30, 41, 59, 0.9);
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3), 0 0 40px rgba(34, 197, 94, 0.1);
}

.search-input-v2:focus::before {
        opacity: 1;
}

/* Quick Actions V2 - Enhanced 3D Effects */
.quick-action-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(30, 41, 59, 0.6);
        position: relative;
        transform-style: preserve-3d;
}

.quick-action-v2::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: inherit;
}

.quick-action-v2:hover {
        transform: scale(1.1) rotateY(5deg);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 197, 94, 0.2);
}

.quick-action-v2:hover::before {
        opacity: 1;
}

/* Notification Button V2 - Pulse Animation */
.notification-btn-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(30, 41, 59, 0.6);
        position: relative;
}

.notification-btn-v2::after {
        content: '';
        position: absolute;
        top: -2px;
        right: -2px;
        width: 6px;
        height: 6px;
        background: #ef4444;
        border-radius: 50%;
        animation: notificationPulse 2s ease-in-out infinite;
}

@keyframes notificationPulse {

        0%,
        100% {
                transform: scale(1);
                opacity: 1;
        }

        50% {
                transform: scale(1.3);
                opacity: 0.7;
        }
}

.notification-btn-v2:hover {
        background: rgba(51, 65, 85, 0.6);
        transform: scale(1.05) rotate(15deg);
        box-shadow: 0 0 15px rgba(251, 191, 36, 0.3);
}

/* User Menu V2 - Gradient Border Animation */
.user-menu-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(30, 41, 59, 0.6);
        position: relative;
        overflow: hidden;
}

.user-menu-v2::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
        transition: left 0.5s ease;
}

.user-menu-v2:hover {
        background: rgba(51, 65, 85, 0.6);
        transform: scale(1.02);
        box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2);
}

.user-menu-v2:hover::before {
        left: 100%;
}

/* Mobile Toggle Animation */
.mobile-toggle-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-toggle-v2:hover {
        transform: scale(1.1) rotate(90deg);
        background: rgba(51, 65, 85, 0.8);
}

/* Mobile Navigation - Slide Animation */
.mobile-nav-v2 {
        animation: slideDown 0.3s ease-out forwards;
        transform-origin: top;
}

@keyframes slideDown {
        from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
        }

        to {
                opacity: 1;
                transform: translateY(0) scale(1);
        }
}

.mobile-nav-item-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid transparent;
        position: relative;
        overflow: hidden;
}

.mobile-nav-item-v2::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
        transition: left 0.4s ease;
}

.mobile-nav-item-v2:hover {
        transform: translateY(-2px) scale(1.02);
        border-color: rgba(34, 197, 94, 0.4);
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}

.mobile-nav-item-v2:hover::before {
        left: 100%;
}

.mobile-quick-action-v2 {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid transparent;
        position: relative;
        transform-style: preserve-3d;
}

.mobile-quick-action-v2:hover {
        transform: translateY(-3px) scale(1.05) rotateX(5deg);
        border-color: rgba(34, 197, 94, 0.3);
        box-shadow: 0 6px 20px rgba(34, 197, 94, 0.2);
        background: rgba(51, 65, 85, 0.7);
}

/* Shimmer Effect for Loading States */
.shimmer {
        background: linear-gradient(90deg,
                        rgba(255, 255, 255, 0) 0%,
                        rgba(255, 255, 255, 0.1) 50%,
                        rgba(255, 255, 255, 0) 100%);
        background-size: 200% 100%;
        animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
        0% {
                background-position: -200% 0;
        }

        100% {
                background-position: 200% 0;
        }
}

/* Enhanced Tooltip Animations */
.tooltip {
        transform: translateY(5px);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.group:hover .tooltip {
        transform: translateY(0);
        animation: tooltipFloat 2s ease-in-out infinite;
}

@keyframes tooltipFloat {

        0%,
        100% {
                transform: translateY(0);
        }

        50% {
                transform: translateY(-2px);
        }
}

/* Responsive Enhancements */
@media (max-width: 1280px) {
        .nav-item-v2 {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
        }

        .football-logo:hover {
                transform: rotate(90deg) scale(1.05);
        }
}

@media (max-width: 1024px) {
        .search-input-v2 {
                width: 12rem;
        }

        .quick-action-v2:hover {
                transform: scale(1.05);
        }
}

@media (max-width: 768px) {
        .header-v2-topbar {
                padding: 0.5rem 0;
        }

        .header-v2-main {
                padding: 0.75rem 0;
        }

        .football-logo {
                width: 2.5rem;
                height: 2.5rem;
        }

        .football-logo:hover {
                transform: rotate(45deg) scale(1.02);
        }

        .mobile-nav-v2 {
                max-height: 80vh;
                overflow-y: auto;
        }
}

@media (max-width: 640px) {
        .header-v2-topbar .container {
                padding: 0 1rem;
        }

        .header-v2-topbar span:nth-child(2) {
                display: none;
        }

        .mobile-nav-item-v2:hover {
                transform: translateY(-1px) scale(1.01);
        }

        .mobile-quick-action-v2:hover {
                transform: translateY(-2px) scale(1.03);
        }
}

/* Performance Optimizations */
.header-v2 * {
        will-change: transform;
}

.header-v2-topbar,
.header-v2-main {
        contain: layout style paint;
}

/* Focus Accessibility */
.nav-item-v2:focus,
.quick-action-v2:focus,
.notification-btn-v2:focus,
.user-menu-v2:focus,
.mobile-toggle-v2:focus {
        outline: 2px solid rgba(34, 197, 94, 0.5);
        outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {

        .header-v2 *,
        .header-v2 *::before,
        .header-v2 *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
        }
}