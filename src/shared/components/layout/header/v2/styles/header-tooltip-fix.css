/* 
   Header V2 Tooltip Fix - Fix for tooltips being cut off
   - Ensures tooltips in the header v2 are fully visible
   - Prevents tooltips from being cut off by container boundaries
   - Specifically targets the issue with the "Highlights" tooltip
*/

/* Make header overflow visible to allow tooltips to extend beyond header */
.header-v2 {
        overflow: visible !important;
}

/* Ensure all tooltip parents have visible overflow */
.header-v2 .group {
        overflow: visible !important;
        position: static !important;
        /* Changed to static to allow tooltip to extend beyond its container */
}

/* Ensure main header section allows tooltips to overflow */
.header-v2-main {
        overflow: visible !important;
}

/* Fix tooltip positioning and visibility */
.tooltip {
        /* Force tooltip on top of everything */
        z-index: 10000 !important;
        /* Make sure tooltip is fully visible */
        opacity: 0;
        /* Position tooltip to extend beyond containers */
        position: fixed !important;
        /* Changed to fixed positioning */
        /* Add extra space between menu item and tooltip */
        margin-top: 5px !important;
        /* Ensure tooltip text is visible */
        color: white !important;
        /* Make background opaque enough */
        background-color: rgba(30, 41, 59, 0.95) !important;
        /* Add extra padding for better visibility */
        padding: 4px 8px !important;
        /* Add a border for better visibility */
        border: 1px solid rgba(100, 116, 139, 0.5) !important;
        /* Add box shadow for depth */
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        /* Allow tooltip to extend to its natural width */
        white-space: nowrap !important;
        /* Increase font size slightly for better readability */
        font-size: 0.75rem !important;
        /* Rounded corners */
        border-radius: 4px !important;
}

/* Special fix for Highlights tooltip that's getting cut off */
a[href="/highlights"]~.tooltip {
        z-index: 10001 !important;
        /* Even higher z-index */
}

/* Make tooltip visible on hover with specific timing */
.group:hover .tooltip {
        opacity: 1 !important;
        transition: opacity 0.1s ease-in !important;
}

/* Fix tooltip arrow position and styling */
.tooltip div[class*="border-"] {
        z-index: 10000 !important;
        border-bottom-color: rgba(30, 41, 59, 0.95) !important;
}

/* Override any animation that might cause z-index stacking issues */
.group:hover .tooltip {
        animation: none !important;
        transform: translateY(0) translateX(-50%) !important;
}

/* Override any possible conflicts */
.header-v2-main nav {
        position: relative !important;
        z-index: 1001 !important;
        overflow: visible !important;
}

/* Make nav items position relative for tooltip positioning */
.nav-item-v2 {
        position: relative !important;
        z-index: 1002 !important;
}

/* Ensure sections below header don't cover tooltips */
section:first-of-type {
        z-index: 1 !important;
}

/* Make sure tooltips don't have pointer events, allowing clicks to pass through */
.tooltip {
        pointer-events: none !important;
}