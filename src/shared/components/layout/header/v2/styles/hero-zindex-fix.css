/* Hero Z-Index Fix */

/* Ensure hero sections have lower z-index than header tooltips */
section:first-child,
section:first-of-type,
.hero-v1,
.hero-v2,
.hero-v3,
[class*="hero-"],
.main-content,
main>div:first-child,
main>section:first-child {
        z-index: 1 !important;
        position: relative !important;
}

/* Add space below header to ensure tooltips have room to display */
.header-v2+* {
        margin-top: 5px;
        position: relative;
        z-index: 1;
}

/* Add padding at the top of the page if needed */
body {
        /* Add padding only if the header is fixed */
        padding-top: 0;
}

/* Specific fix for potential overlay issues */
body>div:first-child>section:first-child,
.bg-gradient-to-br {
        z-index: 1 !important;
}