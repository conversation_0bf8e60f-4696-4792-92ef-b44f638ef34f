"use client";

import React, { useState, useEffect } from 'react';
import './styles/header-v2.css';

const navItems = [
        { label: 'Home', href: '/', icon: '🏠', isActive: true },
        { label: 'Live', href: '/live', icon: '🔴', badge: '3', isLive: true },
        { label: 'Fixtures', href: '/fixtures', icon: '📅', description: 'Upcoming matches' },
        { label: 'Results', href: '/results', icon: '⚽', description: 'Latest scores' },
        { label: 'Highlights', href: '/highlights', icon: '🎥', badge: 'NEW', description: 'Video highlights' },
        { label: 'News', href: '/news', icon: '📰', badge: '5', description: 'Latest news' },
        { label: 'Teams', href: '/teams', icon: '🏆', description: 'Team profiles' },
        { label: 'Standings', href: '/standings', icon: '📊', description: 'League tables' },
];

const quickActions = [
        { label: 'Premier League', href: '/leagues/premier-league', icon: '🏴󠁧󠁢󠁥󠁮󠁧󠁿', color: 'from-purple-600 to-blue-600' },
        { label: 'Champions League', href: '/leagues/champions-league', icon: '🏆', color: 'from-blue-600 to-cyan-600' },
        { label: 'World Cup', href: '/leagues/world-cup', icon: '🌍', color: 'from-green-600 to-emerald-600' },
];

const liveScores = [
        { home: 'MAN', away: 'LIV', homeScore: 2, awayScore: 1, time: "75'" },
        { home: 'BAR', away: 'MAD', homeScore: 0, awayScore: 1, time: "45'" },
        { home: 'PSG', away: 'BAY', homeScore: 3, awayScore: 2, time: "90+2'" },
];

function Header() {
        const [open, setOpen] = useState(false);
        const [scrolled, setScrolled] = useState(false);
        const [currentTime, setCurrentTime] = useState(new Date());
        const [liveMatches] = useState(3);
        const [showLiveScores, setShowLiveScores] = useState(false);
        const [currentScoreIndex, setCurrentScoreIndex] = useState(0);

        useEffect(() => {
                const handleScroll = () => {
                        setScrolled(window.scrollY > 20);
                };
                window.addEventListener('scroll', handleScroll);
                return () => window.removeEventListener('scroll', handleScroll);
        }, []);

        useEffect(() => {
                const timer = setInterval(() => {
                        setCurrentTime(new Date());
                }, 1000);
                return () => clearInterval(timer);
        }, []);

        // Auto-rotate live scores
        useEffect(() => {
                const interval = setInterval(() => {
                        setCurrentScoreIndex((prev) => (prev + 1) % liveScores.length);
                }, 3000);
                return () => clearInterval(interval);
        }, []);

        return (
                <header className={`header-v2 component-v2 fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled
                        ? 'header-v2-scrolled'
                        : 'bg-transparent'
                        }`}>
                        {/* Top Bar - Quick Info */}
                        <div className="header-v2-topbar bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 text-white text-sm py-2">
                                <div className="container mx-auto px-4 flex justify-between items-center">
                                        <div className="flex items-center gap-6">
                                                <span className="flex items-center gap-2">
                                                        <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
                                                        <span className="font-semibold">{liveMatches} LIVE</span>
                                                </span>

                                                {/* Live Scores Ticker */}
                                                <div className="hidden md:flex items-center gap-4">
                                                        <span className="text-green-200 shimmer">🏆 LIVE:</span>
                                                        <div className="live-scores-ticker flex items-center gap-3 overflow-hidden">
                                                                {liveScores.map((score, index) => (
                                                                        <div
                                                                                key={index}
                                                                                className={`live-score-item flex items-center gap-2 transition-all duration-700 ease-in-out ${index === currentScoreIndex
                                                                                        ? 'opacity-100 transform-none active scale-105'
                                                                                        : 'opacity-40 transform scale-90'
                                                                                        }`}
                                                                        >
                                                                                <span className="font-semibold text-white">{score.home}</span>
                                                                                <span className="font-bold text-yellow-300 px-2 py-1 bg-black/20 rounded text-sm">
                                                                                        {score.homeScore}-{score.awayScore}
                                                                                </span>
                                                                                <span className="font-semibold text-white">{score.away}</span>
                                                                                <span className="text-green-200 text-xs bg-green-500/20 px-1.5 py-0.5 rounded">
                                                                                        {score.time}
                                                                                </span>
                                                                        </div>
                                                                ))}
                                                        </div>
                                                </div>

                                                <span className="hidden lg:block text-green-200 shimmer">🎯 UEFA Champions League Final Tonight</span>
                                        </div>
                                        <div className="hidden md:flex items-center gap-4">
                                                <span>📅 {currentTime.toLocaleDateString('vi-VN')}</span>
                                                <span className="font-mono bg-black/20 px-2 py-1 rounded">
                                                        {currentTime.toLocaleTimeString('vi-VN', {
                                                                hour: '2-digit',
                                                                minute: '2-digit'
                                                        })}
                                                </span>
                                        </div>
                                </div>
                        </div>

                        {/* Main Header */}
                        <div className="header-v2-main bg-slate-900/95 backdrop-blur-xl border-b border-slate-700/50">
                                <div className="container mx-auto px-4 py-3">
                                        <div className="flex items-center justify-between">
                                                {/* Logo Section */}
                                                <div className="flex items-center gap-3">
                                                        <div className="football-logo w-12 h-12 bg-gradient-to-br from-green-500 via-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                                                                <span className="text-white text-2xl font-bold">⚽</span>
                                                        </div>
                                                        <div className="hidden sm:block">
                                                                <h1 className="text-xl font-black text-white leading-tight">
                                                                        FOOTBALL
                                                                        <span className="block text-lg font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                                                                                TRACKER
                                                                        </span>
                                                                </h1>
                                                        </div>
                                                </div>

                                                {/* Desktop Navigation */}
                                                <nav className="hidden xl:flex items-center gap-1">
                                                        {navItems.map((item) => (
                                                                <div key={item.href} className="relative group">
                                                                        <a
                                                                                href={item.href}
                                                                                className={`nav-item-v2 relative px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${item.isActive
                                                                                        ? 'bg-gradient-to-r from-green-500/20 to-blue-500/20 text-white border border-green-500/30'
                                                                                        : 'text-gray-300 hover:text-white hover:bg-slate-800/50'
                                                                                        }`}
                                                                        >
                                                                                <span className="text-xs">{item.icon}</span>
                                                                                {item.label}
                                                                                {item.badge && (
                                                                                        <span className={`badge-v2 text-white text-xs px-1.5 py-0.5 rounded-full font-bold ${item.isLive ? 'bg-red-500 animate-pulse' :
                                                                                                item.badge === 'NEW' ? 'bg-green-500' : 'bg-blue-500'
                                                                                                }`}>
                                                                                                {item.badge}
                                                                                        </span>
                                                                                )}
                                                                        </a>                                                        {/* Tooltip */}
                                                                        {item.description && (
                                                                                <div className="tooltip absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-slate-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50 border border-slate-600">
                                                                                        {item.description}
                                                                                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-slate-800"></div>
                                                                                </div>
                                                                        )}
                                                                </div>
                                                        ))}
                                                </nav>

                                                {/* Right Side Controls */}
                                                <div className="flex items-center gap-3">
                                                        {/* Search Bar */}
                                                        <div className="hidden lg:block relative">
                                                                <input
                                                                        type="text"
                                                                        placeholder="Search teams, players..."
                                                                        className="search-input-v2 bg-slate-800/80 border border-slate-600/50 text-gray-300 placeholder:text-gray-500 px-4 py-2 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/50 text-sm w-56"
                                                                />
                                                                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
                                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                                        </svg>
                                                                </div>
                                                        </div>

                                                        {/* Quick League Access */}
                                                        <div className="hidden lg:flex items-center gap-2">
                                                                {quickActions.map((league) => (
                                                                        <div key={league.href} className="relative group">
                                                                                <a
                                                                                        href={league.href}
                                                                                        className={`quick-action-v2 w-10 h-10 bg-gradient-to-br ${league.color} hover:scale-110 border border-white/10 rounded-lg flex items-center justify-center transition-all duration-200 shadow-lg`}
                                                                                        title={league.label}
                                                                                >
                                                                                        <span className="text-sm filter drop-shadow-sm">{league.icon}</span>
                                                                                </a>

                                                                                {/* League Tooltip */}
                                                                                <div className="tooltip absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-slate-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50 border border-slate-600">
                                                                                        {league.label}
                                                                                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-slate-800"></div>
                                                                                </div>
                                                                        </div>
                                                                ))}
                                                        </div>

                                                        {/* Notifications */}
                                                        <button className="notification-btn-v2 relative w-10 h-10 bg-slate-800/60 hover:bg-slate-700/60 border border-slate-600/50 rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-105">
                                                                <svg className="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-3.5-3.5a8.5 8.5 0 1 0-3 3l3.5 3.5z" />
                                                                </svg>
                                                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-slate-900"></div>
                                                        </button>

                                                        {/* User Menu */}
                                                        <div className="user-menu-v2 flex items-center gap-2 bg-slate-800/60 hover:bg-slate-700/60 border border-slate-600/50 rounded-lg px-3 py-2 cursor-pointer transition-all duration-200 hover:scale-105">
                                                                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                                                        <span className="text-white text-sm font-bold">⚽</span>
                                                                </div>
                                                                <div className="hidden md:block">
                                                                        <p className="text-sm font-medium text-gray-300">Fan Zone</p>
                                                                </div>
                                                        </div>

                                                        {/* Mobile Menu Toggle */}
                                                        <button
                                                                className="xl:hidden mobile-toggle-v2 w-10 h-10 bg-slate-800/60 hover:bg-slate-700/60 border border-slate-600/50 rounded-lg flex items-center justify-center transition-all duration-200"
                                                                onClick={() => setOpen(!open)}
                                                        >
                                                                <svg className="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        {open ? (
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                        ) : (
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                                                        )}
                                                                </svg>
                                                        </button>
                                                </div>
                                        </div>

                                        {/* Mobile Navigation */}
                                        {open && (
                                                <div className="xl:hidden mobile-nav-v2 mt-4 bg-slate-800/90 backdrop-blur-xl border border-slate-700/50 rounded-lg p-4">
                                                        {/* Mobile Search */}
                                                        <div className="mb-4">
                                                                <div className="relative">
                                                                        <input
                                                                                type="text"
                                                                                placeholder="Search..."
                                                                                className="w-full bg-slate-700/60 border border-slate-600/50 text-gray-300 placeholder:text-gray-500 px-4 py-3 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500/50"
                                                                        />
                                                                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
                                                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                                                </svg>
                                                                        </div>
                                                                </div>
                                                        </div>

                                                        {/* Mobile Navigation Items */}
                                                        <nav className="grid grid-cols-2 gap-2">
                                                                {navItems.map((item) => (
                                                                        <a
                                                                                key={item.href}
                                                                                href={item.href}
                                                                                className={`mobile-nav-item-v2 p-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-3 ${item.isActive
                                                                                        ? 'bg-gradient-to-r from-green-500/20 to-blue-500/20 text-white border border-green-500/30'
                                                                                        : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
                                                                                        }`}
                                                                                onClick={() => setOpen(false)}
                                                                        >
                                                                                <span className="text-lg">{item.icon}</span>
                                                                                <div className="flex-1">
                                                                                        <div className="flex items-center justify-between">
                                                                                                <span>{item.label}</span>
                                                                                                {item.badge && (
                                                                                                        <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold">
                                                                                                                {item.badge}
                                                                                                        </span>
                                                                                                )}
                                                                                        </div>
                                                                                </div>
                                                                        </a>
                                                                ))}
                                                        </nav>

                                                        {/* Mobile Quick Actions */}
                                                        <div className="mt-4 pt-4 border-t border-slate-700">
                                                                <p className="text-xs text-gray-400 uppercase tracking-wide mb-3 font-semibold">Quick Access</p>
                                                                <div className="grid grid-cols-3 gap-2">
                                                                        {quickActions.map((league) => (
                                                                                <a
                                                                                        key={league.href}
                                                                                        href={league.href}
                                                                                        className="mobile-quick-action-v2 p-3 bg-slate-700/50 hover:bg-slate-600/50 rounded-lg text-center transition-all duration-200"
                                                                                        onClick={() => setOpen(false)}
                                                                                >
                                                                                        <div className="text-2xl mb-1">{league.icon}</div>
                                                                                        <div className="text-xs text-gray-300 font-medium">{league.label}</div>
                                                                                </a>
                                                                        ))}
                                                                </div>
                                                        </div>
                                                </div>
                                        )}
                                </div>
                        </div>
                </header>
        );
}

export default Header;
