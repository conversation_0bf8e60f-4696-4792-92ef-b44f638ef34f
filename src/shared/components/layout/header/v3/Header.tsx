"use client";

import React, { useState, useEffect } from 'react';
import './styles/header-v3.css';

// Refined navigation items with clear purpose and hierarchy
const navItems = [
        { label: 'Home', href: '/', icon: '🏠', isActive: true },
        { label: 'Live', href: '/live', icon: '🔴', badge: '3', isLive: true, description: 'Watch live matches' },
        { label: 'Fixtures', href: '/fixtures', icon: '📅', description: 'Upcoming matches' },
        { label: 'Results', href: '/results', icon: '⚽', description: 'Latest scores' },
        { label: 'Highlights', href: '/highlights', icon: '🎥', badge: 'NEW', description: 'Video highlights' },
        { label: 'News', href: '/news', icon: '📰', description: 'Latest news' },
        { label: 'Teams', href: '/teams', icon: '🏆', description: 'Team profiles' },
        { label: 'Standings', href: '/standings', icon: '📊', description: 'League tables' },
];

// Top leagues for quick access
const quickActions = [
        { label: 'Premier League', href: '/leagues/premier-league', icon: '🏴󠁧󠁢󠁥󠁮󠁧󠁿', color: 'from-purple-600 to-blue-600' },
        { label: 'Champions League', href: '/leagues/champions-league', icon: '🏆', color: 'from-blue-600 to-cyan-600' },
        { label: 'World Cup', href: '/leagues/world-cup', icon: '🌍', color: 'from-green-600 to-emerald-600' },
];

// Live match data
const liveScores = [
        { home: 'MAN', away: 'LIV', homeScore: 2, awayScore: 1, time: "75'" },
        { home: 'BAR', away: 'MAD', homeScore: 0, awayScore: 1, time: "45'" },
        { home: 'PSG', away: 'BAY', homeScore: 3, awayScore: 2, time: "90+2'" },
];

function Header() {
        const [open, setOpen] = useState(false);
        const [scrolled, setScrolled] = useState(false);
        const [currentTime, setCurrentTime] = useState(new Date());
        const [liveMatches] = useState(3);
        const [currentScoreIndex, setCurrentScoreIndex] = useState(0);

        // Handle scroll effects
        useEffect(() => {
                const handleScroll = () => {
                        setScrolled(window.scrollY > 20);
                };
                window.addEventListener('scroll', handleScroll);
                return () => window.removeEventListener('scroll', handleScroll);
        }, []);

        // Update clock
        useEffect(() => {
                const timer = setInterval(() => {
                        setCurrentTime(new Date());
                }, 1000);
                return () => clearInterval(timer);
        }, []);

        // Auto-rotate live scores
        useEffect(() => {
                const interval = setInterval(() => {
                        setCurrentScoreIndex((prev) => (prev + 1) % liveScores.length);
                }, 3000);
                return () => clearInterval(interval);
        }, []);

        return (
                <header
                        role="banner"
                        className={`header-v3 fixed top-0 left-0 right-0 transition-all duration-300 ${scrolled ? 'scrolled bg-slate-900/95 backdrop-blur-xl shadow-lg' : 'bg-gradient-to-b from-slate-900/90 to-slate-900/60'
                                }`}
                >
                        {/* Streamlined top bar */}
                        <div className="header-v3-topbar py-1.5">
                                <div className="container mx-auto px-4 flex justify-between items-center">
                                        <div className="flex items-center gap-4 overflow-hidden">
                                                {/* Live indicator */}
                                                <div className="live-indicator flex items-center gap-2 px-2 py-0.5 bg-red-500/20 rounded-full">
                                                        <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
                                                        <span className="font-medium text-xs">{liveMatches} LIVE</span>
                                                </div>

                                                {/* Live scores with cleaner design */}
                                                <div className="hidden md:flex items-center">
                                                        <div className="live-scores-container flex items-center gap-3 overflow-hidden">
                                                                {liveScores.map((score, index) => (
                                                                        <div
                                                                                key={index}
                                                                                className={`live-score-item flex items-center gap-1.5 py-0.5 px-2 rounded-md ${index === currentScoreIndex
                                                                                        ? 'bg-slate-800/50 border border-blue-500/30'
                                                                                        : 'opacity-60'
                                                                                        }`}
                                                                        >
                                                                                <span className="font-medium text-xs">{score.home}</span>
                                                                                <span className="font-bold text-xs px-1.5 py-0.5 bg-slate-800/80 rounded">
                                                                                        {score.homeScore}-{score.awayScore}
                                                                                </span>
                                                                                <span className="font-medium text-xs">{score.away}</span>
                                                                                <span className="text-green-200 text-xs px-1 rounded-sm">
                                                                                        {score.time}
                                                                                </span>
                                                                        </div>
                                                                ))}
                                                        </div>
                                                </div>

                                                {/* Promotion with subtle highlight */}
                                                <span className="hidden lg:block text-xs text-blue-200 font-medium">
                                                        Champions League Final Tonight
                                                </span>
                                        </div>

                                        {/* Time display with cleaner design */}
                                        <div className="hidden md:flex items-center gap-3">
                                                <span className="text-xs text-slate-300">
                                                        {currentTime.toLocaleDateString('vi-VN', {
                                                                weekday: 'short',
                                                                day: '2-digit',
                                                                month: '2-digit'
                                                        })}
                                                </span>
                                                <span className="font-mono text-xs bg-slate-800/80 px-2 py-1 rounded border border-slate-700/50">
                                                        {currentTime.toLocaleTimeString('vi-VN', {
                                                                hour: '2-digit',
                                                                minute: '2-digit'
                                                        })}
                                                </span>
                                        </div>
                                </div>
                        </div>

                        {/* Main header section with cleaner design */}
                        <div className="header-v3-main border-b border-slate-700/30">
                                <div className="container mx-auto px-4 py-2.5">
                                        <div className="flex items-center justify-between">
                                                {/* Logo with simplified design */}
                                                <div className="flex items-center gap-2.5">
                                                        <a href="/" className="logo-link flex items-center gap-2.5" aria-label="Sports Command Center - Go to homepage">
                                                                <div className="logo w-10 h-10 bg-gradient-to-br from-blue-600 to-emerald-500 rounded-lg flex items-center justify-center shadow-md">
                                                                        <span className="text-white text-xl" aria-hidden="true">⚽</span>
                                                                </div>
                                                                <div className="hidden sm:block">
                                                                        <h1 className="text-lg font-bold text-white leading-tight tracking-tight">
                                                                                FOOTBALL
                                                                                <span className="block text-sm font-medium text-blue-400">
                                                                                        TRACKER
                                                                                </span>
                                                                        </h1>
                                                                </div>
                                                        </a>
                                                </div>

                                                {/* Main navigation - cleaner implementation */}
                                                <nav id="main-navigation" role="navigation" aria-label="Main navigation" className="hidden xl:flex items-center">
                                                        <ul role="menubar" className="nav-wrapper flex items-center bg-slate-800/50 rounded-lg p-1">
                                                                {navItems.map((item) => (
                                                                        <li key={item.href} role="none" className="nav-group relative mx-0.5">
                                                                                <a
                                                                                        href={item.href}
                                                                                        role="menuitem"
                                                                                        className={`nav-item-v3 px-3 py-2 rounded-md text-sm font-medium flex items-center gap-1.5 ${item.isActive
                                                                                                ? 'active bg-slate-700 text-white'
                                                                                                : 'text-slate-300 hover:text-white hover:bg-slate-700/70'
                                                                                                }`}
                                                                                        aria-current={item.isActive ? 'page' : undefined}
                                                                                        aria-label={item.description || item.label}
                                                                                >
                                                                                        {item.icon && <span className="text-xs" aria-hidden="true">{item.icon}</span>}
                                                                                        {item.label}
                                                                                        {item.badge && (
                                                                                                <span className={`badge-v3 text-xs px-1 py-0.5 rounded-full ${item.isLive ? 'bg-red-500 animate-pulse' :
                                                                                                        item.badge === 'NEW' ? 'bg-emerald-500' : 'bg-blue-500'
                                                                                                        }`}
                                                                                                        aria-label={item.isLive ? `${item.badge} live matches` : `${item.badge} content`}
                                                                                                >
                                                                                                        {item.badge}
                                                                                                </span>
                                                                                        )}
                                                                                </a>

                                                                                {/* Enhanced tooltip */}
                                                                                {item.description && (
                                                                                        <div className="tooltip-container">
                                                                                                <div className="tooltip-content">
                                                                                                        {item.description}
                                                                                                        <div className="tooltip-arrow"></div>
                                                                                                </div>
                                                                                        </div>
                                                                                )}
                                                                        </li>
                                                                ))}
                                                        </ul>
                                                </nav>

                                                {/* Right side controls - streamlined */}
                                                <div className="flex items-center gap-2">
                                                        {/* Simplified search */}
                                                        <div className="hidden lg:block relative" role="search" aria-label="Site search">
                                                                <input
                                                                        type="search"
                                                                        placeholder="Search..."
                                                                        aria-label="Search sports content, teams, and matches"
                                                                        className="search-input-v3 bg-slate-800/80 border border-slate-700/50 text-sm text-slate-300 placeholder:text-slate-500 px-3 py-1.5 pr-8 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500/50 w-44"
                                                                />
                                                                <button className="search-btn absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-300" aria-label="Submit search">
                                                                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                                        </svg>
                                                                </button>
                                                        </div>

                                                        {/* League quick access - simplified */}
                                                        <div className="hidden lg:flex items-center bg-slate-800/40 p-1 rounded-md">
                                                                {quickActions.map((league) => (
                                                                        <div key={league.href} className="nav-group relative mx-0.5">
                                                                                <a
                                                                                        href={league.href}
                                                                                        className="quick-action-v3 w-8 h-8 bg-gradient-to-br rounded-md flex items-center justify-center shadow-sm hover:shadow-md"
                                                                                        style={{
                                                                                                background: `linear-gradient(135deg, var(--${league.color.split(' ')[0].slice(5)}-color), var(--${league.color.split(' ')[1].slice(3)}-color))`
                                                                                        }}
                                                                                >
                                                                                        <span className="text-xs">{league.icon}</span>
                                                                                </a>

                                                                                {/* League tooltip - enhanced */}
                                                                                <div className="tooltip-container">
                                                                                        <div className="tooltip-content">
                                                                                                {league.label}
                                                                                                <div className="tooltip-arrow"></div>
                                                                                        </div>
                                                                                </div>
                                                                        </div>
                                                                ))}
                                                        </div>

                                                        {/* User controls - simplified */}
                                                        <div className="user-controls flex items-center gap-1.5">
                                                                {/* Notifications */}
                                                                <button
                                                                        className="control-btn w-9 h-9 bg-slate-800/40 hover:bg-slate-700/60 rounded-md flex items-center justify-center transition-colors"
                                                                        aria-label="View notifications"
                                                                        type="button"
                                                                >
                                                                        <svg className="w-4.5 h-4.5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 17h5l-3.5-3.5a8.5 8.5 0 1 0-3 3l3.5 3.5z" />
                                                                        </svg>
                                                                        <span className="notification-indicator absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full border border-slate-900"></span>
                                                                </button>

                                                                {/* User menu */}
                                                                <button
                                                                        className="user-menu flex items-center gap-2 bg-slate-800/40 hover:bg-slate-700/60 rounded-md px-2.5 py-1.5 transition-colors"
                                                                        aria-label="Open user menu"
                                                                        type="button"
                                                                >
                                                                        <div className="w-6 h-6 bg-gradient-to-br from-blue-600 to-emerald-500 rounded-md flex items-center justify-center">
                                                                                <span className="text-white text-xs">⚽</span>
                                                                        </div>
                                                                        <span className="hidden md:block text-xs font-medium text-slate-300">Fan Zone</span>
                                                                </button>

                                                                {/* Mobile menu toggle - simplified */}
                                                                <button
                                                                        className="xl:hidden mobile-toggle-v3 w-9 h-9 bg-slate-800/40 hover:bg-slate-700/60 rounded-md flex items-center justify-center transition-colors"
                                                                        onClick={() => setOpen(!open)}
                                                                        aria-expanded={open}
                                                                        aria-label="Toggle mobile menu"
                                                                >
                                                                        <svg className="w-4.5 h-4.5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                {open ? (
                                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                                                                                ) : (
                                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 6h16M4 12h16M4 18h16" />
                                                                                )}
                                                                        </svg>
                                                                </button>
                                                        </div>
                                                </div>
                                        </div>

                                        {/* Mobile navigation - improved */}
                                        {open && (
                                                <div className="xl:hidden mobile-nav-v3 mt-3 bg-slate-800/90 backdrop-blur-xl rounded-lg overflow-hidden border border-slate-700/50" role="dialog" aria-label="Mobile navigation menu">
                                                        {/* Mobile search */}
                                                        <div className="p-3 border-b border-slate-700/30">
                                                                <div className="relative" role="search" aria-label="Mobile search">
                                                                        <input
                                                                                type="search"
                                                                                placeholder="Search teams, players, leagues..."
                                                                                aria-label="Search sports content"
                                                                                className="w-full bg-slate-700/60 border border-slate-600/30 text-slate-300 placeholder:text-slate-500 px-3 py-2 pr-9 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500/50"
                                                                        />
                                                                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400" aria-label="Submit search">
                                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                                                </svg>
                                                                        </button>
                                                                </div>
                                                        </div>

                                                        {/* Mobile navigation items - grid layout */}
                                                        <div className="p-3">
                                                                <nav className="grid grid-cols-2 gap-2">
                                                                        {navItems.map((item) => (
                                                                                <a
                                                                                        key={item.href}
                                                                                        href={item.href}
                                                                                        className={`mobile-nav-item p-2.5 rounded-md flex items-start gap-3 ${item.isActive
                                                                                                ? 'bg-slate-700/80 text-white'
                                                                                                : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                                                                                                }`}
                                                                                        onClick={() => setOpen(false)}
                                                                                >
                                                                                        <span className="icon-wrapper text-lg mt-0.5">{item.icon}</span>
                                                                                        <div className="flex-1">
                                                                                                <div className="flex items-center justify-between">
                                                                                                        <span className="text-sm font-medium">{item.label}</span>
                                                                                                        {item.badge && (
                                                                                                                <span className={`text-xs px-1.5 py-0.5 rounded-full ${item.isLive ? 'bg-red-500 animate-pulse' :
                                                                                                                        item.badge === 'NEW' ? 'bg-emerald-500' : 'bg-blue-500'
                                                                                                                        }`}>
                                                                                                                        {item.badge}
                                                                                                                </span>
                                                                                                        )}
                                                                                                </div>
                                                                                                {item.description && (
                                                                                                        <span className="text-xs text-slate-400 mt-0.5 block">{item.description}</span>
                                                                                                )}
                                                                                        </div>
                                                                                </a>
                                                                        ))}
                                                                </nav>
                                                        </div>

                                                        {/* Mobile quick access */}
                                                        <div className="p-3 bg-slate-800/60 border-t border-slate-700/30">
                                                                <p className="text-xs text-slate-400 mb-2.5 font-medium">QUICK ACCESS</p>
                                                                <div className="grid grid-cols-3 gap-2">
                                                                        {quickActions.map((league) => (
                                                                                <a
                                                                                        key={league.href}
                                                                                        href={league.href}
                                                                                        className="p-2.5 bg-slate-700/50 hover:bg-slate-700/80 rounded-md text-center transition-colors"
                                                                                        onClick={() => setOpen(false)}
                                                                                >
                                                                                        <div className="text-xl mb-1">{league.icon}</div>
                                                                                        <div className="text-xs text-slate-300 font-medium">{league.label}</div>
                                                                                </a>
                                                                        ))}
                                                                </div>
                                                        </div>
                                                </div>
                                        )}
                                </div>
                        </div>
                </header>
        );
}

export default Header;
