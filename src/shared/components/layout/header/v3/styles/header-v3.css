/* Header V3 - Professional Football Theme Styles with Enhanced Tooltip Support */

/* Base Variables - For consistent theming */
:root {
        --header-bg: rgba(15, 23, 42, 0.95);
        --header-border: rgba(51, 65, 85, 0.3);
        --nav-active: rgba(51, 65, 85, 0.8);
        --nav-hover: rgba(51, 65, 85, 0.6);
        --text-primary: #ffffff;
        --text-secondary: #94a3b8;
        --accent-blue: #3b82f6;
        --accent-green: #10b981;
        --accent-red: #ef4444;
        --tooltip-bg: rgba(15, 23, 42, 0.95);
        --tooltip-border: rgba(51, 65, 85, 0.5);
        --purple-color: #9333ea;
        --blue-color: #3b82f6;
        --cyan-color: #06b6d4;
        --green-color: #10b981;
        --emerald-color: #10b981;
}

/* Base header styles */
.header-v3 {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        z-index: 1000;
        overflow: visible !important;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        transition: all 0.25s ease;
}

.header-v3.scrolled {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Topbar styles - Simplified and professional */
.header-v3-topbar {
        background: linear-gradient(90deg, #0f172a, #1e293b, #0f172a);
        border-bottom: 1px solid rgba(30, 41, 59, 0.5);
        color: var(--text-primary);
        font-size: 0.75rem;
}

/* Live indicator */
.live-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
        background-color: rgba(239, 68, 68, 0.15);
}

.live-indicator span:first-child {
        width: 0.5rem;
        height: 0.5rem;
        background-color: var(--accent-red);
        border-radius: 50%;
        animation: pulse 2s infinite;
}

@keyframes pulse {
        0% {
                transform: scale(0.95);
                opacity: 1;
        }

        50% {
                transform: scale(1.1);
                opacity: 0.7;
        }

        100% {
                transform: scale(0.95);
                opacity: 1;
        }
}

/* Live scores container */
.live-scores-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        overflow: hidden;
}

.live-score-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.125rem 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.3s ease;
}

.live-score-item.active {
        background-color: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Main header section */
.header-v3-main {
        background-color: var(--header-bg);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid var(--header-border);
}

/* Logo styling */
.logo {
        background: linear-gradient(135deg, var(--accent-blue), var(--accent-green));
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
}

.logo-link:hover .logo {
        transform: scale(1.05);
}

/* Navigation wrapper */
.nav-wrapper {
        display: flex;
        align-items: center;
        background-color: rgba(30, 41, 59, 0.3);
        border-radius: 0.5rem;
        padding: 0.25rem;
}

/* Nav items */
.nav-item-v3 {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
        color: var(--text-secondary);
}

.nav-item-v3:hover {
        background-color: var(--nav-hover);
        color: var(--text-primary);
}

.nav-item-v3.active {
        background-color: var(--nav-active);
        color: var(--text-primary);
}

/* Badge styling */
.badge-v3 {
        font-size: 0.65rem;
        padding: 0.125rem 0.375rem;
        border-radius: 9999px;
        color: white;
        font-weight: 500;
}

/* Search input */
.search-input-v3 {
        background-color: rgba(30, 41, 59, 0.4);
        border: 1px solid rgba(51, 65, 85, 0.3);
        color: var(--text-secondary);
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        padding-right: 2rem;
        font-size: 0.875rem;
        width: 11rem;
        transition: all 0.2s ease;
}

.search-input-v3:focus {
        outline: none;
        border-color: var(--accent-blue);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        background-color: rgba(30, 41, 59, 0.6);
}

.search-btn {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        transition: color 0.2s ease;
}

.search-btn:hover {
        color: var(--text-primary);
}

/* Quick action buttons */
.quick-action-v3 {
        width: 2rem;
        height: 2rem;
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-action-v3:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Control buttons */
.control-btn {
        width: 2.25rem;
        height: 2.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(30, 41, 59, 0.4);
        border-radius: 0.375rem;
        transition: background-color 0.2s ease;
        position: relative;
}

.control-btn:hover {
        background-color: var(--nav-hover);
}

.notification-indicator {
        position: absolute;
        top: -0.25rem;
        right: -0.25rem;
        width: 0.625rem;
        height: 0.625rem;
        background-color: var(--accent-red);
        border-radius: 50%;
        border: 1px solid rgba(15, 23, 42, 0.9);
}

/* User menu */
.user-menu {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background-color: rgba(30, 41, 59, 0.4);
        border-radius: 0.375rem;
        padding: 0.375rem 0.625rem;
        transition: background-color 0.2s ease;
}

.user-menu:hover {
        background-color: var(--nav-hover);
}

/* Mobile menu */
.mobile-nav-v3 {
        background-color: rgba(15, 23, 42, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 0.5rem;
        border: 1px solid rgba(51, 65, 85, 0.3);
        overflow: hidden;
        margin-top: 0.75rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.625rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
}

/* ------------------------------
   ENHANCED TOOLTIP SYSTEM - V3
   ------------------------------ */

/* Position the tooltip container correctly */
.tooltip-container {
        position: absolute;
        overflow: visible !important;
        pointer-events: none;
        left: 50%;
        top: 100%;
        transform: translateX(-50%);
        z-index: 10000 !important;
        margin-top: 0.5rem;
        width: max-content;
        opacity: 0;
        transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Show tooltip on nav item hover */
.nav-group:hover .tooltip-container {
        opacity: 1;
        transform: translateX(-50%) translateY(-1px);
}

/* Actual tooltip styling */
.tooltip-content {
        background-color: var(--tooltip-bg);
        color: white;
        border-radius: 0.375rem;
        padding: 0.375rem 0.625rem;
        border: 1px solid var(--tooltip-border);
        font-size: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
        white-space: nowrap;
        text-align: center;
        line-height: 1.4;
}

/* Arrow for the tooltip */
.tooltip-arrow {
        position: absolute;
        top: -5px;
        left: 50%;
        margin-left: -5px;
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid var(--tooltip-bg);
        z-index: 10001;
}

/* Media Queries for Responsive Design */
@media (max-width: 1280px) {
        .header-v3-main nav {
                display: none;
        }
}

@media (max-width: 768px) {
        .header-v3-topbar {
                font-size: 0.7rem;
        }

        .live-indicator {
                padding: 0.125rem 0.375rem;
        }
}

/* Subtle animations */
@keyframes shimmerAnimation {
        0% {
                background-position: -200% 0;
        }

        100% {
                background-position: 200% 0;
        }
}

/* ------------------------------
   ENHANCED TOOLTIP SYSTEM - V3
   ------------------------------ */

/* Position the tooltip container correctly */
.tooltip-container {
        /* Absolute positioning relative to header */
        position: absolute;
        /* Container for tooltip must be visible */
        overflow: visible !important;
        /* Don't block pointer events */
        pointer-events: none;
        /* Position relative to nav item */
        left: 50%;
        /* Move below the nav item */
        top: 100%;
        /* Center horizontally */
        transform: translateX(-50%);
        /* Show above everything */
        z-index: 10000 !important;
        /* Add some spacing from nav item */
        margin-top: 8px;
        /* Fixed width for stability */
        width: max-content;
        /* Ensure it's hidden by default */
        opacity: 0;
        /* Smooth transition */
        transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Show tooltip on nav item hover */
.nav-group:hover .tooltip-container {
        /* Make visible */
        opacity: 1;
        /* Move upward slightly for hover effect */
        transform: translateX(-50%) translateY(-2px);
}

/* Actual tooltip styling */
.tooltip-content {
        /* Clean background */
        background-color: rgba(15, 23, 42, 0.95);
        /* White text for contrast */
        color: white;
        /* Rounded corners */
        border-radius: 6px;
        /* Give it some padding */
        padding: 0.5rem 0.75rem;
        /* Add a subtle border */
        border: 1px solid rgba(100, 116, 139, 0.5);
        /* Text size */
        font-size: 0.75rem;
        /* Add shadow for depth */
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
        /* Never break to multiple lines */
        white-space: nowrap;
        /* Text centered */
        text-align: center;
}

/* Arrow for the tooltip */
.tooltip-arrow {
        position: absolute;
        /* Position at top center */
        top: -5px;
        left: 50%;
        margin-left: -5px;
        /* Create the arrow shape */
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid rgba(15, 23, 42, 0.95);
        z-index: 10001;
}

/* Media Queries for Responsive Design */
@media (max-width: 1024px) {
        .header-v3-main nav {
                display: none;
        }
}