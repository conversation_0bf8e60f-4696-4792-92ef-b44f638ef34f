/* Header V1 - Command Center Theme Styles */
.header-v1 {
        /* V1 specific styles extracted from globals.css */
}

.header-v1 .neomorphism-card {
        background: rgba(30, 41, 59, 0.4);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
                4px 4px 16px rgba(0, 0, 0, 0.3),
                -2px -2px 8px rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        transition: all 0.2s ease;
}

.header-v1 .neomorphism-card:hover {
        transform: translateY(-2px);
        box-shadow:
                6px 6px 20px rgba(0, 0, 0, 0.3),
                -3px -3px 10px rgba(255, 255, 255, 0.1);
}

.header-v1 .futuristic-button {
        position: relative;
        background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(6, 182, 212, 0.1));
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;
        overflow: hidden;
}

.header-v1 .futuristic-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.3s;
}

.header-v1 .futuristic-button:hover::before {
        left: 100%;
}

.header-v1 .futuristic-button:hover {
        transform: translateY(-1px);
        background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(6, 182, 212, 0.2));
        box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
}