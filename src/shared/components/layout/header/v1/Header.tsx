"use client";

import React, { useState, useEffect } from 'react';
import './styles/header-v1.css';
const navItems = [
        { label: 'Dashboard', href: '/', icon: '🏠' },
        { label: 'Live Matches', href: '/fixtures', icon: '⚽' },
        { label: 'Analytics', href: '/analytics', icon: '📊' },
        { label: 'Teams', href: '/teams', icon: '👥' },
        { label: 'News', href: '/news', icon: '📰' },
];

function Header() {
        const [open, setOpen] = useState(false);
        const [scrolled, setScrolled] = useState(false);
        const [currentTime, setCurrentTime] = useState(new Date());

        useEffect(() => {
                const handleScroll = () => {
                        setScrolled(window.scrollY > 20);
                };
                window.addEventListener('scroll', handleScroll);
                return () => window.removeEventListener('scroll', handleScroll);
        }, []);

        useEffect(() => {
                const timer = setInterval(() => {
                        setCurrentTime(new Date());
                }, 1000);
                return () => clearInterval(timer);
        }, []);

        return (
                <header className={`header-v1 component-v1 fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled
                        ? 'glass-morphism backdrop-blur-xl border-b border-white/10'
                        : 'bg-transparent'
                        }`}>
                        <div className="container mx-auto px-4 sm:px-6 py-3">
                                <div className="flex items-center justify-between">
                                        {/* Logo Section */}
                                        <div className="flex items-center gap-3">
                                                <div className="w-10 h-10 neomorphism-card flex items-center justify-center rounded-xl">
                                                        <span className="text-xl">⚡</span>
                                                </div>
                                                <div className="hidden sm:block">
                                                        <h1 className="text-xl font-bold gradient-text">
                                                                SPORTS COMMAND
                                                        </h1>
                                                </div>
                                        </div>

                                        {/* Desktop Navigation */}
                                        <nav className="hidden lg:flex items-center gap-1">
                                                {navItems.map((item, index) => (
                                                        <a
                                                                key={item.href}
                                                                href={item.href}
                                                                className="futuristic-button px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:text-white transition-all duration-300 flex items-center gap-2"
                                                        >
                                                                <span className="text-sm">{item.icon}</span>
                                                                {item.label}
                                                        </a>
                                                ))}
                                        </nav>

                                        {/* Right Side Controls */}
                                        <div className="flex items-center gap-3">
                                                {/* Search Bar */}
                                                <div className="hidden md:block relative">
                                                        <input
                                                                type="text"
                                                                placeholder="Search..."
                                                                className="neomorphism-card bg-slate-800/50 border-0 text-gray-300 placeholder:text-gray-500 px-3 py-2 pr-9 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 text-sm w-48"
                                                        />
                                                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                                </svg>
                                                        </div>
                                                </div>

                                                {/* Time Display */}
                                                <div className="hidden lg:block neomorphism-card px-3 py-2 rounded-lg">
                                                        <div className="text-sm font-mono gradient-text">
                                                                {currentTime.toLocaleTimeString('en-US', {
                                                                        hour12: false,
                                                                        hour: '2-digit',
                                                                        minute: '2-digit'
                                                                })}
                                                        </div>
                                                </div>

                                                {/* Notification Bell */}
                                                <button className="neomorphism-card p-2 rounded-lg hover:scale-105 transition-all duration-300 relative">
                                                        <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-3.5-3.5a8.5 8.5 0 1 0-3 3l3.5 3.5z" />
                                                        </svg>
                                                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                                                </button>

                                                {/* User Profile */}
                                                <div className="neomorphism-card p-2 rounded-lg flex items-center gap-2">
                                                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-lg flex items-center justify-center">
                                                                <span className="text-white text-sm font-bold">LG</span>
                                                        </div>
                                                        <div className="hidden xl:block">
                                                                <p className="text-sm font-medium text-gray-300">Login</p>
                                                        </div>
                                                </div>

                                                {/* Mobile Menu Toggle */}
                                                <button
                                                        className="lg:hidden neomorphism-card p-2 rounded-lg"
                                                        onClick={() => setOpen(!open)}
                                                >
                                                        <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                {open ? (
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                ) : (
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                                                )}
                                                        </svg>
                                                </button>
                                        </div>
                                </div>

                                {/* Mobile Navigation */}
                                {open && (
                                        <div className="lg:hidden mt-3 neomorphism-card p-4 rounded-lg">
                                                <nav className="flex flex-col gap-2">
                                                        {navItems.map((item) => (
                                                                <a
                                                                        key={item.href}
                                                                        href={item.href}
                                                                        className="futuristic-button px-4 py-3 rounded-lg text-sm font-medium text-gray-300 hover:text-white transition-all duration-300 flex items-center gap-3"
                                                                        onClick={() => setOpen(false)}
                                                                >
                                                                        <span className="text-lg">{item.icon}</span>
                                                                        {item.label}
                                                                </a>
                                                        ))}
                                                </nav>

                                                {/* Mobile Search */}
                                                <div className="mt-4 pt-4 border-t border-gray-700">
                                                        <div className="relative">
                                                                <input
                                                                        type="text"
                                                                        placeholder="Search..."
                                                                        className="w-full neomorphism-card bg-slate-800/50 border-0 text-gray-300 placeholder:text-gray-500 px-4 py-3 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                                                                />
                                                                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
                                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                                        </svg>
                                                                </div>
                                                        </div>
                                                </div>
                                        </div>
                                )}
                        </div>
                </header>
        );
}

export default Header;
