import React, { JSX } from 'react';

interface SectionWrapperProps {
        children: React.ReactNode;
        id?: string;
        ariaLabelledBy?: string;
        ariaLabel?: string;
        variant?: 'hero' | 'content' | 'feature' | 'highlight';
        spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
        className?: string;
        element?: 'section' | 'article' | 'aside';
}

const SectionWrapper: React.FC<SectionWrapperProps> = ({
        children,
        id,
        ariaLabelledBy,
        ariaLabel,
        variant = 'content',
        spacing = 'md',
        className = '',
        element = 'section'
}) => {
        const Tag = element as keyof JSX.IntrinsicElements;

        // Variant-specific classes
        const variantClasses = {
                hero: 'hero-section',
                content: 'content-section',
                feature: 'feature-section',
                highlight: 'highlight-section'
        };

        // Spacing classes
        const spacingClasses = {
                none: '',
                sm: 'py-8',
                md: 'py-12',
                lg: 'py-16',
                xl: 'py-24'
        };

        return (
                <Tag
                        id={id}
                        aria-labelledby={ariaLabelledBy}
                        aria-label={ariaLabel}
                        className={`${variantClasses[variant]} ${spacingClasses[spacing]} ${className}`}
                >
                        <div className="container mx-auto px-6">
                                {children}
                        </div>
                </Tag>
        );
};

export default SectionWrapper;