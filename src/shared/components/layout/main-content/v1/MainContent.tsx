import React from 'react';

interface MainContentProps {
        children: React.ReactNode;
        id?: string;
        className?: string;
}

const MainContent: React.FC<MainContentProps> = ({
        children,
        id = 'main-content',
        className = ''
}) => (
        <div
                id={id}
                aria-label="Main content"
                className={`flex-1 relative z-10 ${className}`}
                tabIndex={-1}
        >
                {children}
        </div>
);

export default MainContent;