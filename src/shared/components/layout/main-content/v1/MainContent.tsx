import React from 'react';

interface MainContentProps {
        children: React.ReactNode;
        id?: string;
        className?: string;
}

const MainContent: React.FC<MainContentProps> = ({
        children,
        id = 'main-content',
        className = ''
}) => (
        <main
                id={id}
                role="main"
                className={`flex-1 relative z-10 ${className}`}
                tabIndex={-1}
        >
                {children}
        </main>
);

export default MainContent;