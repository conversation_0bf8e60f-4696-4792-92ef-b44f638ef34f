// Body.tsx (v1) - Generic Layout Body Container
"use client";

import React from "react";

interface BodyProps {
        children: React.ReactNode;
}

const Body: React.FC<BodyProps> = ({ children }) => {
        return (
                <div className="min-h-screen bg-transparent pt-20">
                        <main className="relative z-10">
                                <div className="container mx-auto px-6 py-12 space-y-12">
                                        {children}
                                </div>
                        </main>
                </div>
        );
};

export default Body;
