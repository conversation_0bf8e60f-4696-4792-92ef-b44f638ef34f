"use client";

import React, { ReactNode } from 'react';
import { useDeviceCapabilities } from '@/shared/hooks/useMobileDetection';

interface PerformanceAwareAnimationProps {
  children: ReactNode;
  animationType?: 'subtle' | 'medium' | 'complex';
  fallbackContent?: ReactNode;
  className?: string;
}

/**
 * Component that adapts animations based on device performance and user preferences
 */
export const PerformanceAwareAnimation: React.FC<PerformanceAwareAnimationProps> = ({
  children,
  animationType = 'medium',
  fallbackContent,
  className = ''
}) => {
  const { prefersReducedMotion, connectionSpeed } = useDeviceCapabilities();

  // Determine if we should show animations
  const shouldAnimate = !prefersReducedMotion && connectionSpeed !== 'slow';

  // Get animation classes based on type and device capabilities
  const getAnimationClasses = () => {
    if (!shouldAnimate) return '';

    switch (animationType) {
      case 'subtle':
        return 'transition-opacity duration-300 ease-out';
      case 'medium':
        return connectionSpeed === 'fast' 
          ? 'transition-all duration-300 ease-out hover:scale-105'
          : 'transition-opacity duration-200 ease-out';
      case 'complex':
        return connectionSpeed === 'fast'
          ? 'transition-all duration-500 ease-out transform hover:scale-105 hover:rotate-1'
          : 'transition-opacity duration-300 ease-out';
      default:
        return '';
    }
  };

  // If reduced motion is preferred or slow connection, show fallback or static content
  if (prefersReducedMotion || connectionSpeed === 'slow') {
    return (
      <div className={`${className} ${getAnimationClasses()}`}>
        {fallbackContent || children}
      </div>
    );
  }

  return (
    <div className={`${className} ${getAnimationClasses()}`}>
      {children}
    </div>
  );
};

/**
 * Hook for getting performance-aware CSS classes
 */
export const usePerformanceAwareClasses = (baseClasses: string = '') => {
  const { prefersReducedMotion, connectionSpeed } = useDeviceCapabilities();

  const getClasses = (animationType: 'hover' | 'transition' | 'transform' = 'transition') => {
    let classes = baseClasses;

    if (prefersReducedMotion) {
      // Remove animations for users who prefer reduced motion
      return classes;
    }

    switch (animationType) {
      case 'hover':
        if (connectionSpeed === 'fast') {
          classes += ' hover:scale-105 hover:shadow-lg';
        }
        break;
      case 'transition':
        classes += connectionSpeed === 'fast' 
          ? ' transition-all duration-300 ease-out'
          : ' transition-opacity duration-200 ease-out';
        break;
      case 'transform':
        if (connectionSpeed === 'fast') {
          classes += ' transform transition-transform duration-300 ease-out';
        }
        break;
    }

    return classes;
  };

  return { getClasses, shouldAnimate: !prefersReducedMotion && connectionSpeed !== 'slow' };
};

/**
 * Component for adaptive loading states
 */
interface AdaptiveLoadingProps {
  isLoading: boolean;
  children: ReactNode;
  loadingComponent?: ReactNode;
  className?: string;
}

export const AdaptiveLoading: React.FC<AdaptiveLoadingProps> = ({
  isLoading,
  children,
  loadingComponent,
  className = ''
}) => {
  const { connectionSpeed, prefersReducedMotion } = useDeviceCapabilities();

  const getLoadingAnimation = () => {
    if (prefersReducedMotion) {
      return 'opacity-50';
    }

    if (connectionSpeed === 'slow') {
      return 'animate-pulse';
    }

    return 'animate-spin';
  };

  if (isLoading) {
    if (loadingComponent) {
      return <div className={className}>{loadingComponent}</div>;
    }

    return (
      <div className={`${className} flex items-center justify-center p-8`}>
        <div className={`w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full ${getLoadingAnimation()}`} />
      </div>
    );
  }

  return <div className={className}>{children}</div>;
};

export default PerformanceAwareAnimation;
