"use client";

import React, { useState, useEffect } from 'react';
import { useMobileDetection } from '@/shared/hooks/useMobileDetection';
import { optimizeButtonForTouch } from '@/shared/utils/touchUtils';

interface NavItem {
  label: string;
  href: string;
  icon?: string;
  isActive?: boolean;
}

interface MobileOptimizedNavProps {
  items: NavItem[];
  onItemClick?: (item: NavItem) => void;
  className?: string;
}

/**
 * Mobile-optimized navigation component
 * Adapts layout and interactions based on device capabilities
 */
export const MobileOptimizedNav: React.FC<MobileOptimizedNavProps> = ({
  items,
  onItemClick,
  className = ''
}) => {
  const { isMobile, isTablet, isTouchDevice, orientation } = useMobileDetection();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Close menu on orientation change
  useEffect(() => {
    setIsMenuOpen(false);
  }, [orientation]);

  // Close menu when clicking outside (for mobile)
  useEffect(() => {
    if (!isMenuOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.mobile-nav-menu')) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isMenuOpen]);

  const handleItemClick = (item: NavItem) => {
    onItemClick?.(item);
    if (isMobile) {
      setIsMenuOpen(false);
    }
  };

  // Mobile hamburger menu
  if (isMobile) {
    return (
      <div className={`mobile-nav ${className}`}>
        {/* Hamburger Button */}
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className={optimizeButtonForTouch(`
            relative z-50 p-3 rounded-lg
            bg-gray-800/80 hover:bg-gray-700/80
            border border-gray-600/50
            transition-all duration-200
            ${isMenuOpen ? 'bg-gray-700/90' : ''}
          `)}
          aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
          aria-expanded={isMenuOpen}
        >
          <div className="w-6 h-6 flex flex-col justify-center items-center">
            <span className={`block w-5 h-0.5 bg-white transition-all duration-300 ${
              isMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-1'
            }`} />
            <span className={`block w-5 h-0.5 bg-white transition-all duration-300 ${
              isMenuOpen ? 'opacity-0' : 'opacity-100'
            }`} />
            <span className={`block w-5 h-0.5 bg-white transition-all duration-300 ${
              isMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-1'
            }`} />
          </div>
        </button>

        {/* Mobile Menu Overlay */}
        {isMenuOpen && (
          <div className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm">
            <div className="mobile-nav-menu absolute top-16 right-4 left-4 bg-gray-900/95 backdrop-blur-lg rounded-xl border border-gray-700/50 shadow-2xl">
              <div className="p-4 space-y-2">
                {items.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => handleItemClick(item)}
                    className={optimizeButtonForTouch(`
                      w-full text-left p-4 rounded-lg
                      transition-all duration-200
                      flex items-center space-x-3
                      ${item.isActive 
                        ? 'bg-gradient-to-r from-purple-600/20 to-blue-600/20 text-white border border-purple-500/30' 
                        : 'text-gray-300 hover:text-white hover:bg-gray-800/50'
                      }
                    `)}
                  >
                    {item.icon && <span className="text-lg">{item.icon}</span>}
                    <span className="font-medium">{item.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Tablet horizontal scrollable nav
  if (isTablet) {
    return (
      <div className={`tablet-nav ${className}`}>
        <div className="flex space-x-2 overflow-x-auto scrollbar-hide pb-2">
          {items.map((item, index) => (
            <button
              key={index}
              onClick={() => handleItemClick(item)}
              className={optimizeButtonForTouch(`
                flex items-center space-x-2 px-4 py-3 rounded-lg
                whitespace-nowrap transition-all duration-200
                ${item.isActive
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium'
                  : 'text-gray-300 hover:text-white hover:bg-gray-800/50'
                }
              `)}
            >
              {item.icon && <span className="text-lg">{item.icon}</span>}
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    );
  }

  // Desktop horizontal nav
  return (
    <div className={`desktop-nav ${className}`}>
      <div className="flex space-x-4">
        {items.map((item, index) => (
          <button
            key={index}
            onClick={() => handleItemClick(item)}
            className={`
              flex items-center space-x-2 px-4 py-2 rounded-lg
              transition-all duration-200 font-medium
              ${item.isActive
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-800/50'
              }
              ${isTouchDevice ? optimizeButtonForTouch() : ''}
            `}
          >
            {item.icon && <span className="text-lg">{item.icon}</span>}
            <span>{item.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default MobileOptimizedNav;
