import React from 'react';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
        label: string;
        href: string;
        isActive?: boolean;
}

interface BreadcrumbsProps {
        items: BreadcrumbItem[];
        showHome?: boolean;
        separator?: React.ReactNode;
        className?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
        items,
        showHome = true,
        separator = <ChevronRight className="w-4 h-4" />,
        className = ''
}) => {
        // Generate Schema.org structured data
        const generateBreadcrumbSchema = () => {
                const breadcrumbList = {
                        "@context": "https://schema.org",
                        "@type": "BreadcrumbList",
                        "itemListElement": items.map((item, index) => ({
                                "@type": "ListItem",
                                "position": index + 1,
                                "name": item.label,
                                "item": `${typeof window !== 'undefined' ? window.location.origin : ''}${item.href}`
                        }))
                };
                return breadcrumbList;
        };

        return (
                <>
                        {/* JSON-LD Structured Data */}
                        <script
                                type="application/ld+json"
                                dangerouslySetInnerHTML={{
                                        __html: JSON.stringify(generateBreadcrumbSchema())
                                }}
                        />

                        <nav
                                aria-label="Breadcrumb"
                                className={`breadcrumbs py-4 ${className}`}
                        >
                                <ol className="flex items-center space-x-2 text-sm text-gray-300">
                                        {items.map((item, index) => (
                                                <li key={item.href} className="flex items-center">
                                                        {index > 0 && (
                                                                <span className="mx-2 text-gray-500" aria-hidden="true">
                                                                        {separator}
                                                                </span>
                                                        )}

                                                        {item.isActive ? (
                                                                <span
                                                                        className="text-white font-medium"
                                                                        aria-current="page"
                                                                >
                                                                        {index === 0 && showHome ? (
                                                                                <Home className="w-4 h-4" aria-label={item.label} />
                                                                        ) : (
                                                                                item.label
                                                                        )}
                                                                </span>
                                                        ) : (
                                                                <a
                                                                        href={item.href}
                                                                        className="text-gray-300 hover:text-white transition-colors flex items-center"
                                                                >
                                                                        {index === 0 && showHome ? (
                                                                                <Home className="w-4 h-4" aria-label={item.label} />
                                                                        ) : (
                                                                                item.label
                                                                        )}
                                                                </a>
                                                        )}
                                                </li>
                                        ))}
                                </ol>
                        </nav>
                </>
        );
};

export default Breadcrumbs;