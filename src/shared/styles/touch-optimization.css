/**
 * Touch Optimization CSS
 * Provides better touch interactions and mobile UX
 */

/* Touch manipulation optimization */
.touch-optimized {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Minimum touch target sizes */
.touch-target-small {
  min-height: 32px;
  min-width: 32px;
}

.touch-target-medium {
  min-height: 44px;
  min-width: 44px;
}

.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}

/* Touch feedback animations */
.touch-feedback-subtle {
  transition: all 0.15s ease-out;
}

.touch-feedback-subtle:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.touch-feedback-medium {
  transition: all 0.15s ease-out;
}

.touch-feedback-medium:hover {
  transform: scale(1.02);
}

.touch-feedback-medium:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.touch-feedback-strong {
  transition: all 0.15s ease-out;
}

.touch-feedback-strong:hover {
  transform: scale(1.05);
}

.touch-feedback-strong:active {
  transform: scale(0.92);
  opacity: 0.7;
}

/* Ripple effect animation */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-container {
  position: relative;
  overflow: hidden;
}

/* Touch-friendly button styles */
.btn-touch {
  @apply touch-optimized touch-target-medium touch-feedback-medium;
  @apply flex items-center justify-center;
  @apply rounded-lg px-4 py-2;
  @apply font-medium text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-touch-primary {
  @apply btn-touch;
  @apply bg-gradient-to-r from-purple-600 to-blue-600;
  @apply text-white;
  @apply hover:from-purple-700 hover:to-blue-700;
  @apply focus:ring-purple-500;
}

.btn-touch-secondary {
  @apply btn-touch;
  @apply bg-gray-700 hover:bg-gray-600;
  @apply text-gray-200;
  @apply focus:ring-gray-500;
}

.btn-touch-ghost {
  @apply btn-touch;
  @apply bg-transparent hover:bg-gray-800/50;
  @apply text-gray-300 hover:text-white;
  @apply border border-gray-600 hover:border-gray-500;
  @apply focus:ring-gray-500;
}

/* Touch-friendly card styles */
.card-touch {
  @apply touch-optimized touch-feedback-subtle;
  @apply cursor-pointer;
  @apply rounded-lg p-4;
  @apply transition-all duration-200;
  @apply hover:shadow-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500;
}

/* Touch-friendly link styles */
.link-touch {
  @apply touch-optimized touch-target-medium touch-feedback-subtle;
  @apply inline-flex items-center;
  @apply text-blue-400 hover:text-blue-300;
  @apply underline-offset-4 hover:underline;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  @apply rounded px-1;
}

/* Touch-friendly icon button */
.icon-btn-touch {
  @apply touch-optimized touch-target-medium touch-feedback-medium;
  @apply flex items-center justify-center;
  @apply rounded-full p-2;
  @apply text-gray-400 hover:text-white;
  @apply hover:bg-gray-700/50;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
}

/* Touch-friendly navigation dots */
.nav-dot-touch {
  @apply touch-optimized touch-target-small touch-feedback-medium;
  @apply w-3 h-3 rounded-full;
  @apply transition-all duration-300;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500;
}

.nav-dot-touch.active {
  @apply bg-purple-500 scale-125;
}

.nav-dot-touch:not(.active) {
  @apply bg-gray-500 hover:bg-gray-400;
}

/* Touch-friendly filter buttons */
.filter-btn-touch {
  @apply touch-optimized touch-target-medium touch-feedback-medium;
  @apply px-4 py-2 rounded-lg text-sm;
  @apply transition-all duration-200;
  @apply whitespace-nowrap;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.filter-btn-touch.active {
  @apply bg-gradient-to-r from-purple-600 to-blue-600;
  @apply text-white font-medium;
  @apply focus:ring-purple-500;
}

.filter-btn-touch:not(.active) {
  @apply bg-gray-700/50 hover:bg-gray-600/50;
  @apply text-gray-300 hover:text-white;
  @apply focus:ring-gray-500;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .touch-target-mobile {
    min-height: 48px;
    min-width: 48px;
  }
  
  .btn-touch {
    @apply touch-target-mobile;
    @apply px-6 py-3;
    @apply text-base;
  }
  
  .icon-btn-touch {
    @apply touch-target-mobile;
    @apply p-3;
  }
  
  /* Increase spacing between touch targets */
  .touch-spacing-mobile > * + * {
    margin-left: 0.75rem;
  }
  
  .touch-spacing-mobile-vertical > * + * {
    margin-top: 0.75rem;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .touch-target-tablet {
    min-height: 46px;
    min-width: 46px;
  }
  
  .btn-touch {
    @apply touch-target-tablet;
  }
  
  .icon-btn-touch {
    @apply touch-target-tablet;
  }
}

/* Disable hover effects on touch devices */
@media (hover: none) {
  .touch-feedback-medium:hover,
  .touch-feedback-strong:hover,
  .btn-touch:hover,
  .card-touch:hover,
  .link-touch:hover,
  .icon-btn-touch:hover,
  .filter-btn-touch:hover {
    transform: none;
  }
}

/* Focus visible for keyboard navigation */
.touch-optimized:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* Prevent text selection on touch elements */
.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
