// SEO Structured Data for Schema.org compliance

export interface WebSiteSchema {
        '@context': string;
        '@type': string;
        name: string;
        description: string;
        url: string;
        potentialAction: {
                '@type': string;
                target: {
                        '@type': string;
                        urlTemplate: string;
                };
                'query-input': string;
        };
}

export interface BreadcrumbListSchema {
        '@context': string;
        '@type': string;
        itemListElement: Array<{
                '@type': string;
                position: number;
                name: string;
                item: string;
        }>;
}

export interface OrganizationSchema {
        '@context': string;
        '@type': string;
        name: string;
        description: string;
        url: string;
        logo: string;
        sameAs: string[];
}

export function generateWebSiteSchema(): WebSiteSchema {
        return {
                '@context': 'https://schema.org',
                '@type': 'WebSite',
                name: 'Sports Command Center',
                description: 'Advanced sports management platform with real-time analytics, live match tracking, and comprehensive team statistics',
                url: process.env.NEXT_PUBLIC_SITE_URL || 'https://sportscommand.com',
                potentialAction: {
                        '@type': 'SearchAction',
                        target: {
                                '@type': 'EntryPoint',
                                urlTemplate: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://sportscommand.com'}/search?q={search_term_string}`
                        },
                        'query-input': 'required name=search_term_string'
                }
        };
}

export function generateBreadcrumbSchema(items: Array<{ name: string; href: string }>): BreadcrumbListSchema {
        return {
                '@context': 'https://schema.org',
                '@type': 'BreadcrumbList',
                itemListElement: items.map((item, index) => ({
                        '@type': 'ListItem',
                        position: index + 1,
                        name: item.name,
                        item: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://sportscommand.com'}${item.href}`
                }))
        };
}

export function generateOrganizationSchema(): OrganizationSchema {
        return {
                '@context': 'https://schema.org',
                '@type': 'Organization',
                name: 'Sports Command Center',
                description: 'Leading sports technology platform providing real-time analytics and comprehensive sports management solutions',
                url: process.env.NEXT_PUBLIC_SITE_URL || 'https://sportscommand.com',
                logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://sportscommand.com'}/images/logo.png`,
                sameAs: [
                        'https://twitter.com/sportscommand',
                        'https://facebook.com/sportscommand',
                        'https://linkedin.com/company/sports-command'
                ]
        };
}