import { Metadata } from 'next'

export interface SEOConfig {
        title: string
        description: string
        keywords?: string[]
        image?: string
        url?: string
        type?: 'website' | 'article' | 'product'
        locale?: string
        siteName?: string
        publishedTime?: string
        modifiedTime?: string
        author?: string
        section?: string
        tags?: string[]
}

export function generateMetadata(config: SEOConfig): Metadata {
        const {
                title,
                description,
                keywords = [],
                image,
                url,
                type = 'website',
                locale = 'vi-VN',
                siteName = 'Sports Game API',
                publishedTime,
                modifiedTime,
                author,
                section,
                tags = []
        } = config

        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yourdomain.com'
        const fullUrl = url ? `${baseUrl}${url}` : baseUrl
        const imageUrl = image ? (image.startsWith('http') ? image : `${baseUrl}${image}`) : `${baseUrl}/images/og-default.jpg`

        return {
                title,
                description,
                keywords: keywords.join(', '),
                authors: author ? [{ name: author }] : undefined,
                creator: author,
                publisher: siteName,
                robots: {
                        index: true,
                        follow: true,
                        googleBot: {
                                index: true,
                                follow: true,
                                'max-video-preview': -1,
                                'max-image-preview': 'large',
                                'max-snippet': -1,
                        },
                },
                openGraph: {
                        type: type === 'product' ? 'website' : type,
                        locale,
                        url: fullUrl,
                        siteName,
                        title,
                        description,
                        images: [
                                {
                                        url: imageUrl,
                                        width: 1200,
                                        height: 630,
                                        alt: title,
                                }
                        ],
                        publishedTime,
                        modifiedTime,
                        authors: author ? [author] : undefined,
                        section,
                        tags,
                },
                twitter: {
                        card: 'summary_large_image',
                        site: '@yoursitename',
                        creator: author ? `@${author}` : undefined,
                        title,
                        description,
                        images: [imageUrl],
                },
                alternates: {
                        canonical: fullUrl,
                        languages: {
                                'vi-VN': fullUrl,
                                'en-US': `${fullUrl}?lang=en`,
                        },
                },
                verification: {
                        google: process.env.GOOGLE_SITE_VERIFICATION,
                        yandex: process.env.YANDEX_VERIFICATION,
                        other: {
                                bing: [process.env.BING_SITE_VERIFICATION].filter(Boolean) as string[],
                        },
                },
                category: section,
        }
}

export const defaultSEO: SEOConfig = {
        title: 'Sports Game API - Thông tin thể thao và trò chơi',
        description: 'Nền tảng cung cấp thông tin thể thao, tin tức game và API dữ liệu thể thao chất lượng cao cho developers và người dùng.',
        keywords: [
                'sports api',
                'game api',
                'thể thao',
                'tin tức thể thao',
                'API thể thao',
                'sports data',
                'football api',
                'basketball api',
                'soccer api'
        ],
        type: 'website',
        locale: 'vi-VN',
        siteName: 'Sports Game API',
}

export function generatePageMetadata(pageConfig: Partial<SEOConfig>): Metadata {
        const mergedConfig = { ...defaultSEO, ...pageConfig }
        return generateMetadata(mergedConfig)
}

export function generateArticleMetadata(config: {
        title: string
        description: string
        author?: string
        publishedTime?: string
        modifiedTime?: string
        section?: string
        tags?: string[]
        image?: string
}): Metadata {
        return generateMetadata({
                ...config,
                type: 'article',
                keywords: [...(defaultSEO.keywords || []), ...(config.tags || [])],
        })
}