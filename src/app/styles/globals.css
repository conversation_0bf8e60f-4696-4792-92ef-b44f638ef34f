@import "tailwindcss";

/* Component Versioning System */
@import "./components-versions.css";

/* Touch Optimization */
@import "../../shared/styles/touch-optimization.css";

/* Import Vietnamese-friendly fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300;400;600;700;800;900&display=swap');

/* Modern Dashboard CSS Variables */
:root {
  --background: #0f0f23;
  --foreground: #e2e8f0;
  --card-bg: rgba(30, 41, 59, 0.4);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --neon-purple: #a855f7;
  --neon-cyan: #06b6d4;
  --neon-blue: #3b82f6;
  --shadow-light: rgba(255, 255, 255, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.3);
  --vietnamese-font: 'Inter', 'Nunito Sans', 'Segoe UI', system-ui, -apple-system, sans-serif;
}

/* Dark Theme Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f0f23;
    --foreground: #e2e8f0;
  }
}

/* Base Styles */
html {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--vietnamese-font);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--neon-purple), var(--neon-cyan));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-blue));
}

/* Neumorphism Card Effect - Simplified */
.neomorphism-card {
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    4px 4px 16px var(--shadow-dark),
    -2px -2px 8px var(--shadow-light);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.neomorphism-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(167, 85, 247, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Alternating layout animation */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.news-card-even {
  animation: slideInLeft 0.6s ease-out forwards;
}

.news-card-odd {
  animation: slideInRight 0.6s ease-out forwards;
}

/* Glass Morphism Effect */
.glass-morphism {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Simplified Neon Glow Effects */
.neon-glow-purple {
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
}

.neon-glow-cyan {
  box-shadow: 0 0 15px rgba(6, 182, 212, 0.3);
}

.neon-glow-blue {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

/* Animated Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #a855f7, #06b6d4, #3b82f6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

/* Floating Animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

/* Pulse Glow Animation */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }

  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.8);
  }
}

/* Grid Pattern Background */
.bg-grid-white\/\[0\.02\] {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
}

/* Custom Button Styles - Simplified */
.futuristic-button {
  position: relative;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(6, 182, 212, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  overflow: hidden;
}

.futuristic-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.3s;
}

.futuristic-button:hover::before {
  left: 100%;
}

.futuristic-button:hover {
  transform: translateY(-1px);
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(6, 182, 212, 0.2));
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
}

/* Vietnamese Text Support */
.vietnamese-text {
  font-family: var(--vietnamese-font);
  font-feature-settings: "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.vietnamese-title {
  font-family: var(--vietnamese-font);
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Enhanced priority indicators */
.priority-high::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #a855f7, #06b6d4);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.3;
  animation: priorityPulse 2s ease-in-out infinite;
}

@keyframes priorityPulse {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.6;
  }
}

/* Enhanced Vietnamese Title Styling */
.vietnamese-title {
  font-family: var(--vietnamese-font);
  font-weight: 700;
  letter-spacing: -0.025em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .neomorphism-card {
    border-radius: 8px;
    box-shadow: 2px 2px 8px var(--shadow-dark), -1px -1px 4px var(--shadow-light);
  }

  .futuristic-button {
    border-radius: 8px;
  }
}

@media (max-width: 640px) {
  .gradient-text {
    font-size: 1.25rem;
  }
}

/* Simple League Marquee Animation */
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  animation: marquee 25s linear infinite;
}

/* Wave Animation for League Navigation */
@keyframes wave {

  0%,
  100% {
    transform: translateY(0) scale(1);
  }

  25% {
    transform: translateY(-8px) scale(1.05);
  }

  50% {
    transform: translateY(-15px) scale(1.1);
  }

  75% {
    transform: translateY(-8px) scale(1.05);
  }
}

.wave-1 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 0s;
}

.wave-2 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 0.2s;
}

.wave-3 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 0.4s;
}

.wave-4 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 0.6s;
}

.wave-5 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 0.8s;
}

.wave-6 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 1s;
}

.wave-7 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 1.2s;
}

.wave-8 {
  animation: wave 3s ease-in-out infinite;
  animation-delay: 1.4s;
}

/* Enhanced League Navigation Effects */
.league-item {
  position: relative;
  overflow: hidden;
}

.league-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.league-item:hover::before {
  left: 100%;
}

/* Responsive Wave Animation */
@media (max-width: 768px) {
  @keyframes wave {

    0%,
    100% {
      transform: translateY(0) scale(1);
    }

    50% {
      transform: translateY(-8px) scale(1.05);
    }
  }

  .league-item {
    padding: 12px 16px !important;
  }
}