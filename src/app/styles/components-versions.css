/* 
  Component Versioning CSS System
  This file manages different versions of components to avoid conflicts
  
  IMPORTANT: Individual component CSS files should be imported directly 
  by their respective components, not centrally in this file.
*/

/* Version-specific base styles */
.component-v1 {
        /* V1 Base Theme - Command Center/Futuristic */
        --primary-color: #a855f7;
        --secondary-color: #06b6d4;
        --accent-color: #3b82f6;
        --card-bg: rgba(30, 41, 59, 0.4);
        --glass-bg: rgba(255, 255, 255, 0.1);
}

.component-v2 {
        /* V2 Base Theme - Football/Sports */
        --primary-color: #22c55e;
        --secondary-color: #2563eb;
        --accent-color: #7c3aed;
        --card-bg: rgba(30, 41, 59, 0.6);
        --glass-bg: rgba(15, 23, 42, 0.8);
}

/* Shared utilities that work across versions */
.glass-morphism-shared {
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.transition-shared {
        transition: all 0.2s ease;
}

.hover-scale-shared:hover {
        transform: scale(1.02);
}

/* Responsive utilities for all versions */
@media (max-width: 768px) {

        .component-v1,
        .component-v2 {
                --card-padding: 0.75rem;
                --text-size: 0.875rem;
        }
}

@media (max-width: 640px) {

        .component-v1,
        .component-v2 {
                --card-padding: 0.5rem;
                --text-size: 0.8125rem;
        }
}