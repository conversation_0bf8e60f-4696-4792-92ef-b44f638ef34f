import { NextRequest, NextResponse } from 'next/server';

// GET /api/news
export async function GET(request: NextRequest) {
        try {
                // Mock news data
                const news = [
                        {
                                id: 1,
                                title: 'Championship Final Set for This Weekend',
                                excerpt: 'Two powerhouse teams prepare for the ultimate showdown',
                                category: 'Football',
                                publishDate: '2025-06-07T10:00:00Z',
                                author: 'Sports Desk',
                                featured: true
                        },
                        {
                                id: 2,
                                title: 'Transfer Window Updates',
                                excerpt: 'Latest player movements across major leagues',
                                category: 'Football',
                                publishDate: '2025-06-06T15:30:00Z',
                                author: 'Transfer Analyst',
                                featured: false
                        },
                        {
                                id: 3,
                                title: 'Basketball Season Highlights',
                                excerpt: 'Reviewing the most memorable moments from this season',
                                category: 'Basketball',
                                publishDate: '2025-06-06T12:00:00Z',
                                author: 'Basketball Reporter',
                                featured: true
                        }
                ];

                return NextResponse.json({
                        success: true,
                        data: news
                });
        } catch (error) {
                return NextResponse.json(
                        { success: false, error: 'Failed to fetch news' },
                        { status: 500 }
                );
        }
}