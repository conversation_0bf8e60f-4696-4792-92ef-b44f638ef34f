import { NextRequest, NextResponse } from 'next/server';

// GET /api/news/categories
export async function GET(request: NextRequest) {
        try {
                // Mock news categories data
                const categories = [
                        { id: 1, name: 'Football', slug: 'football', count: 45 },
                        { id: 2, name: 'Basketball', slug: 'basketball', count: 23 },
                        { id: 3, name: 'Tennis', slug: 'tennis', count: 18 },
                        { id: 4, name: 'Baseball', slug: 'baseball', count: 12 },
                        { id: 5, name: 'Soccer', slug: 'soccer', count: 67 }
                ];

                return NextResponse.json({
                        success: true,
                        data: categories
                });
        } catch (error) {
                return NextResponse.json(
                        { success: false, error: 'Failed to fetch categories' },
                        { status: 500 }
                );
        }
}