import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yourdomain.com'

        return {
                rules: [
                        {
                                userAgent: '*',
                                allow: '/',
                                disallow: [
                                        '/admin/',
                                        '/api/',
                                        '/private/',
                                        '/_next/',
                                        '/temp/',
                                        '*.json',
                                        '*.xml'
                                ],
                        },
                        {
                                userAgent: 'Googlebot',
                                allow: '/',
                                disallow: [
                                        '/admin/',
                                        '/api/',
                                        '/private/'
                                ],
                        }
                ],
                sitemap: `${baseUrl}/sitemap.xml`,
                host: baseUrl
        }
}