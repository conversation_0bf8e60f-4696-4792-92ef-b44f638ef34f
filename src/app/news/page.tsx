// News Page - App Router Integration (Versioned Architecture)
import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo/meta-tags';
import { NewsPage } from '@/features/news/pages';

// SEO Metadata
export const metadata: Metadata = generatePageMetadata({
  title: "Sports News - Latest Updates & Analysis",
  description: "Stay updated with the latest sports news, match analysis, transfer updates, and breaking news from football, basketball, tennis and more.",
  keywords: [
    "sports news",
    "football news", 
    "basketball news",
    "tennis news",
    "match analysis",
    "sports updates",
    "breaking sports news",
    "sports journalism"
  ],
  type: "website"
});

// News Page Component
export default function NewsPageRoute() {
  return <NewsPage />;
}
