import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./styles/globals.css";
import { generatePageMetadata } from "@/lib/seo/meta-tags";
import { generateWebSiteSchema } from "@/lib/seo/structured-data";
import Header from "@/shared/components/layout/header";
import Footer from "@/shared/components/layout/footer";
import MainContent from "@/shared/components/layout/main-content";
// Header v3 uses its own tooltip system that doesn't require these fixes
// import "./header-tooltip-fix.css";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = generatePageMetadata({
  title: "Sports Game API - Modern Sports Dashboard & API Platform",
  description: "Advanced Sports Management Dashboard with Real-time Analytics, Sports API, and comprehensive sports data platform for developers and sports enthusiasts.",
  keywords: [
    "sports dashboard",
    "sports api",
    "real-time analytics",
    "sports data",
    "dashboard",
    "sports management",
    "api platform",
    "sports statistics"
  ],
  type: "website"
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Generate structured data
  const websiteSchema = generateWebSiteSchema();

  return (
    <html lang="vi" className="dark">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
        />
        <meta name="theme-color" content="#1e293b" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen overflow-x-hidden`}
      >
        <div className="relative min-h-screen overflow-hidden">
          {/* Dynamic Particle System */}
          <div className="fixed inset-0 pointer-events-none z-0" aria-hidden="true">
            {Array.from({ length: 15 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-gradient-to-r from-purple-500/30 to-cyan-500/30 rounded-full animate-pulse float-animation"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 6}s`,
                  animationDuration: `${6 + Math.random() * 4}s`,
                }}
              />
            ))}
          </div>

          {/* Background Effects */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/40 to-black" aria-hidden="true"></div>
          <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" aria-hidden="true"></div>

          {/* Floating Orbs for Atmosphere */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" aria-hidden="true"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000" aria-hidden="true"></div>
          <div className="absolute top-3/4 left-1/3 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-2000" aria-hidden="true"></div>

          {/* Skip Links for accessibility */}
          <div className="skip-links">
            <a
              href="#main-content"
              className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-black focus:rounded focus:no-underline"
            >
              Skip to main content
            </a>
            <a
              href="#main-navigation"
              className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-40 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-black focus:rounded focus:no-underline"
            >
              Skip to navigation
            </a>
            <a
              href="#footer-content"
              className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-72 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-black focus:rounded focus:no-underline"
            >
              Skip to footer
            </a>
          </div>

          {/* Header */}
          <Header />

          {/* Main Content */}
          <MainContent id="main-content">
            {children}
          </MainContent>

          {/* Footer */}
          <Footer />
        </div>
      </body>
    </html>
  );
}
