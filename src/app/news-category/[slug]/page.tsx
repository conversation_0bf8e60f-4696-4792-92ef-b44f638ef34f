// News Category Page - Dynamic Route (Versioned Architecture)
import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo/meta-tags';
import { NewsCategoryPage } from '@/features/news/pages';

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Dynamic SEO Metadata
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { slug } = resolvedParams;
  const categoryName = slug.charAt(0).toUpperCase() + slug.slice(1);
  
  return generatePageMetadata({
    title: `${categoryName} Sports News - Latest Updates`,
    description: `Latest ${categoryName.toLowerCase()} sports news, analysis, and updates. Stay informed with breaking news and in-depth coverage.`,
    keywords: [
      `${categoryName.toLowerCase()} news`,
      `${categoryName.toLowerCase()} sports`,
      "sports news",
      "sports updates",
      "breaking news",
      "sports analysis"
    ],
    type: "website"
  });
}

// News Category Page Component
export default async function NewsCategoryPageRoute({ params }: PageProps) {
  const resolvedParams = await params;
  return <NewsCategoryPage slug={resolvedParams.slug} />;
}
