import { MetadataRoute } from 'next'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yourdomain.com'

        // Static pages
        const staticPages = [
                {
                        url: baseUrl,
                        lastModified: new Date(),
                        changeFrequency: 'daily' as const,
                        priority: 1,
                },
                {
                        url: `${baseUrl}/about`,
                        lastModified: new Date(),
                        changeFrequency: 'monthly' as const,
                        priority: 0.8,
                },
                {
                        url: `${baseUrl}/contact`,
                        lastModified: new Date(),
                        changeFrequency: 'monthly' as const,
                        priority: 0.7,
                },
                {
                        url: `${baseUrl}/privacy-policy`,
                        lastModified: new Date(),
                        changeFrequency: 'monthly' as const,
                        priority: 0.5,
                },
                {
                        url: `${baseUrl}/terms-of-service`,
                        lastModified: new Date(),
                        changeFrequency: 'monthly' as const,
                        priority: 0.5,
                }
        ]

        // Dynamic news pages (mock implementation)
        try {
                // In a real application, you would fetch from your API or database
                const newsPages = [
                        {
                                url: `${baseUrl}/news/sports-updates`,
                                lastModified: new Date(),
                                changeFrequency: 'weekly' as const,
                                priority: 0.9,
                        },
                        {
                                url: `${baseUrl}/news/game-highlights`,
                                lastModified: new Date(),
                                changeFrequency: 'weekly' as const,
                                priority: 0.9,
                        }
                ]

                return [...staticPages, ...newsPages]
        } catch (error) {
                console.error('Error generating sitemap:', error)
                return staticPages
        }
}