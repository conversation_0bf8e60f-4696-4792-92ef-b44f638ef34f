{"name": "fe-enduser", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e .env.local -- sh -c 'next dev -p $PORT'", "build": "next build", "start": "dotenv -e .env.local -- sh -c 'next dev -p $PORT'", "lint": "next lint", "analyze": "ANALYZE=true npm run build", "analyze:server": "BUNDLE_ANALYZE=server npm run build", "analyze:browser": "BUNDLE_ANALYZE=browser npm run build"}, "dependencies": {"clsx": "^2.1.1", "dotenv-cli": "^8.0.0", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@axe-core/cli": "^4.10.2", "@next/bundle-analyzer": "^15.3.3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "axe-core": "^4.10.3", "html-validate": "^9.5.5", "playwright": "^1.52.0", "puppeteer": "^24.10.0", "tailwindcss": "^4", "typescript": "^5", "web-vitals": "^5.0.2"}}