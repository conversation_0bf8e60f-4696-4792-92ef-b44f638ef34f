# HTML STRUCTURE ANALYSIS & REFACTORING PLAN

## 🔍 **CURRENT STRUCTURE ISSUES**

### **1. Root Layout (layout.tsx)**
```html
<html>
<body>
  <Header />                    <!-- ✅ OK -->
  <MainContent>                 <!-- ❌ Should be <main> -->
    {children}                  <!-- HomePage content -->
  </MainContent>
  <Footer />                    <!-- ✅ OK -->
</body>
</html>
```

### **2. HomePage (Page.tsx)**
```html
<>
  <nav role="navigation">       <!-- ✅ OK -->
    <SectionWrapper>            <!-- ❌ Unnecessary wrapper -->
      <Breadcrumbs />
    </SectionWrapper>
  </nav>
  
  <section role="banner">       <!-- ❌ Wrong: section can't be banner -->
    <h1>...</h1>
    <Hero />                    <!-- Contains nested sections -->
  </section>
  
  <section role="main">         <!-- ❌ Wrong: section can't be main -->
    <h2>...</h2>
    <FeaturedMatches />
  </section>
  
  <section role="complementary"><!-- ❌ Wrong: should be aside -->
    <SectionWrapper>            <!-- ❌ Unnecessary wrapper -->
      <h2>...</h2>
      <LastNews />
    </SectionWrapper>
  </section>
</>
```

### **3. Hero Component (Hero.tsx)**
```html
<section aria-label="Hero section">  <!-- ❌ Nested in another section -->
  <header>                           <!-- ✅ OK -->
    <HeroHeader />
  </header>
  
  <nav>                              <!-- ✅ OK -->
    <LeagueSelector />
  </nav>
  
  <div class="grid">
    <article>                        <!-- ✅ OK -->
      <MatchDisplayCard />
    </article>
    
    <div>
      <section>                      <!-- ❌ Nested section level 3 -->
        <StatisticsPanel />
      </section>
      <section>                      <!-- ❌ Nested section level 3 -->
        <UpcomingMatchesList />
      </section>
    </div>
  </div>
</section>
```

### **4. FeaturedMatches Component**
```html
<section role="region">              <!-- ✅ OK -->
  <header>                           <!-- ✅ OK -->
    <h2>Featured Matches</h2>
  </header>
  
  <div class="grid">
    <article>                        <!-- ✅ OK -->
      <MatchCard />
    </article>
  </div>
</section>
```

### **5. LastNews Component**
```html
<section role="region">              <!-- ✅ OK -->
  <header>                           <!-- ✅ OK -->
    <nav>                            <!-- ✅ OK -->
      <!-- League navigation -->
    </nav>
  </header>
  
  <div>
    <article>                        <!-- ✅ OK -->
      <!-- News content -->
    </article>
  </div>
</section>
```

## 🎯 **TARGET STRUCTURE (HTML5 COMPLIANT)**

### **1. Root Layout (Fixed)**
```html
<html>
<body>
  <header role="banner">             <!-- Site header -->
    <Header />
  </header>
  
  <main role="main">                 <!-- ✅ Changed from div -->
    {children}                       <!-- HomePage content -->
  </main>
  
  <footer role="contentinfo">        <!-- Site footer -->
    <Footer />
  </footer>
</body>
</html>
```

### **2. HomePage (Fixed)**
```html
<!-- No wrapper fragment, direct content -->
<nav aria-label="Breadcrumb">       <!-- ✅ Removed role -->
  <Breadcrumbs />                    <!-- ✅ Removed wrapper -->
</nav>

<!-- ✅ No section wrapper, Hero is the content -->
<Hero />

<section aria-labelledby="matches-heading">  <!-- ✅ Proper section -->
  <h2 id="matches-heading">Featured Matches</h2>
  <FeaturedMatches />
</section>

<aside aria-labelledby="news-heading">       <!-- ✅ Changed to aside -->
  <h2 id="news-heading">Latest News</h2>
  <LastNews />
</aside>
```

### **3. Hero Component (Fixed)**
```html
<!-- ✅ No section wrapper, direct content -->
<div class="hero-container">
  <header>
    <h1>Sports Command Center</h1>    <!-- ✅ Main page title -->
    <HeroHeader />
  </header>
  
  <nav aria-label="League selection">
    <LeagueSelector />
  </nav>
  
  <div class="grid">
    <section aria-labelledby="featured-match">  <!-- ✅ Proper section -->
      <h2 id="featured-match">Featured Match</h2>
      <MatchDisplayCard />
    </section>
    
    <aside aria-label="Live statistics">        <!-- ✅ Changed to aside -->
      <section aria-labelledby="stats">
        <h3 id="stats">Live Stats</h3>
        <StatisticsPanel />
      </section>
      
      <section aria-labelledby="upcoming">
        <h3 id="upcoming">Upcoming Matches</h3>
        <UpcomingMatchesList />
      </section>
    </aside>
  </div>
</div>
```

## 📊 **HEADING HIERARCHY (Fixed)**

```
h1: Sports Command Center (Hero)
├── h2: Featured Match (Hero)
├── h2: Featured Matches (FeaturedMatches)
├── h2: Latest News (LastNews)
└── h2: Live Stats (Hero sidebar)
    ├── h3: Live Stats
    └── h3: Upcoming Matches
```

## 🔧 **REFACTORING PRIORITY**

### **Phase 1: Critical Structure (High Priority)**
1. ✅ Fix MainContent → main element
2. ✅ Remove wrong role attributes
3. ✅ Fix heading hierarchy
4. ✅ Remove unnecessary section wrappers

### **Phase 2: Semantic Improvements (Medium Priority)**
1. ✅ Convert complementary section → aside
2. ✅ Remove SectionWrapper where unnecessary
3. ✅ Optimize ARIA labels

### **Phase 3: Validation (Low Priority)**
1. ✅ HTML5 validator check
2. ✅ Accessibility audit
3. ✅ Screen reader testing
