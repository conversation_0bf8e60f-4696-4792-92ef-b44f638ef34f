/**
 * WCAG 2.1 AA Compliance Testing Script
 * Comprehensive accessibility audit
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class AccessibilityAuditor {
        constructor() {
                this.results = {
                        timestamp: new Date().toISOString(),
                        url: 'http://localhost:5000',
                        wcagLevel: 'AA',
                        compliance: {
                                score: 0,
                                level: '',
                                issues: []
                        },
                        tests: {}
                };
        }

        async audit() {
                console.log('♿ Starting WCAG 2.1 AA Compliance Audit...');

                const browser = await puppeteer.launch({
                        headless: 'new',
                        args: ['--no-sandbox', '--disable-setuid-sandbox']
                });

                try {
                        const page = await browser.newPage();
                        await page.goto('http://localhost:5000', { waitUntil: 'networkidle2' });

                        console.log('🔍 Testing Perceivable Guidelines...');
                        await this.testPerceivable(page);

                        console.log('🔍 Testing Operable Guidelines...');
                        await this.testOperable(page);

                        console.log('🔍 Testing Understandable Guidelines...');
                        await this.testUnderstandable(page);

                        console.log('🔍 Testing Robust Guidelines...');
                        await this.testRobust(page);

                } catch (error) {
                        console.error('Audit error:', error);
                } finally {
                        await browser.close();
                }

                await this.calculateScore();
                await this.generateReport();
        }

        async testPerceivable(page) {
                const perceivableTests = await page.evaluate(() => {
                        const results = {
                                images: { total: 0, withAlt: 0, issues: [] },
                                headings: { structure: [], issues: [] },
                                colors: { issues: [] },
                                multimedia: { issues: [] }
                        };

                        // 1.1.1 Non-text Content (Level A)
                        const images = document.querySelectorAll('img');
                        results.images.total = images.length;

                        images.forEach((img, index) => {
                                if (img.alt !== undefined) {
                                        results.images.withAlt++;
                                } else {
                                        results.images.issues.push(`Image ${index + 1} missing alt attribute: ${img.src}`);
                                }

                                if (img.alt === '') {
                                        // Check if decorative image
                                        const parent = img.closest('[role="presentation"], [aria-hidden="true"]');
                                        if (!parent) {
                                                results.images.issues.push(`Image ${index + 1} has empty alt but may not be decorative: ${img.src}`);
                                        }
                                }
                        });

                        // 1.3.1 Info and Relationships (Level A) - Heading structure
                        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                        let currentLevel = 0;

                        headings.forEach((heading, index) => {
                                const level = parseInt(heading.tagName.charAt(1));
                                results.headings.structure.push({
                                        index: index + 1,
                                        tag: heading.tagName,
                                        level,
                                        text: heading.textContent.trim()
                                });

                                if (index === 0 && level !== 1) {
                                        results.headings.issues.push('First heading should be H1');
                                }

                                if (level > currentLevel + 1) {
                                        results.headings.issues.push(`Heading level skipped: ${heading.tagName} after H${currentLevel}`);
                                }

                                currentLevel = level;
                        });

                        // 1.4.1 Use of Color (Level A) - Check for color-only information
                        const colorOnlyElements = document.querySelectorAll('[style*="color"]:not([aria-label]):not([title])');
                        if (colorOnlyElements.length > 0) {
                                results.colors.issues.push(`${colorOnlyElements.length} elements may rely on color alone for meaning`);
                        }

                        // 1.4.3 Contrast (Level AA) - Basic check for contrast issues
                        const textElements = document.querySelectorAll('p, span, div, a, button, label');
                        let lowContrastCount = 0;

                        textElements.forEach(element => {
                                const styles = window.getComputedStyle(element);
                                const color = styles.color;
                                const backgroundColor = styles.backgroundColor;

                                // Simple heuristic for potential contrast issues
                                if (color === 'rgb(128, 128, 128)' || color.includes('lightgray')) {
                                        lowContrastCount++;
                                }
                        });

                        if (lowContrastCount > 0) {
                                results.colors.issues.push(`${lowContrastCount} elements may have insufficient color contrast`);
                        }

                        return results;
                });

                this.results.tests.perceivable = perceivableTests;
        }

        async testOperable(page) {
                const operableTests = await page.evaluate(() => {
                        const results = {
                                keyboard: { focusable: 0, skipLinks: [], issues: [] },
                                timing: { issues: [] },
                                seizures: { issues: [] },
                                navigation: { issues: [] }
                        };

                        // 2.1.1 Keyboard (Level A)
                        const focusableElements = document.querySelectorAll(
                                'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
                        );
                        results.keyboard.focusable = focusableElements.length;

                        // Check for keyboard traps
                        focusableElements.forEach((element, index) => {
                                const tabIndex = element.getAttribute('tabindex');
                                if (tabIndex && parseInt(tabIndex) > 0) {
                                        results.keyboard.issues.push(`Element ${index + 1} has positive tabindex: ${tabIndex}`);
                                }
                        });

                        // 2.4.1 Bypass Blocks (Level A) - Skip links
                        const skipLinks = document.querySelectorAll('a[href^="#"]');
                        skipLinks.forEach(link => {
                                const text = link.textContent.toLowerCase();
                                if (text.includes('skip') || text.includes('jump')) {
                                        results.keyboard.skipLinks.push({
                                                text: link.textContent.trim(),
                                                href: link.getAttribute('href')
                                        });
                                }
                        });

                        if (results.keyboard.skipLinks.length === 0) {
                                results.keyboard.issues.push('No skip links found for keyboard navigation');
                        }

                        // 2.4.2 Page Titled (Level A)
                        if (!document.title || document.title.length < 10) {
                                results.navigation.issues.push('Page title missing or too short');
                        }

                        // 2.4.3 Focus Order (Level A)
                        const interactiveElements = document.querySelectorAll('button, a[href], input, select, textarea');
                        if (interactiveElements.length > 0) {
                                // Check if elements are in logical order (basic check)
                                let previousTop = -1;
                                interactiveElements.forEach((element, index) => {
                                        const rect = element.getBoundingClientRect();
                                        if (rect.top < previousTop - 50) { // Allow some flexibility
                                                results.keyboard.issues.push(`Focus order may be incorrect at element ${index + 1}`);
                                        }
                                        previousTop = rect.top;
                                });
                        }

                        // 2.4.4 Link Purpose (Level A)
                        const links = document.querySelectorAll('a[href]');
                        links.forEach((link, index) => {
                                const text = link.textContent.trim();
                                const title = link.getAttribute('title');
                                const ariaLabel = link.getAttribute('aria-label');

                                if (!text && !title && !ariaLabel) {
                                        results.navigation.issues.push(`Link ${index + 1} has no accessible text`);
                                }

                                if (text.toLowerCase() === 'click here' || text.toLowerCase() === 'read more') {
                                        results.navigation.issues.push(`Link ${index + 1} has non-descriptive text: "${text}"`);
                                }
                        });

                        return results;
                });

                this.results.tests.operable = operableTests;
        }

        async testUnderstandable(page) {
                const understandableTests = await page.evaluate(() => {
                        const results = {
                                readable: { issues: [] },
                                predictable: { issues: [] },
                                inputAssistance: { issues: [] }
                        };

                        // 3.1.1 Language of Page (Level A)
                        const htmlLang = document.documentElement.getAttribute('lang');
                        if (!htmlLang) {
                                results.readable.issues.push('HTML lang attribute missing');
                        }

                        // 3.2.1 On Focus (Level A) - Check for context changes on focus
                        const focusableElements = document.querySelectorAll('input, select, button, a[href]');
                        focusableElements.forEach((element, index) => {
                                const onFocus = element.getAttribute('onfocus');
                                const onBlur = element.getAttribute('onblur');

                                if (onFocus && (onFocus.includes('submit') || onFocus.includes('location'))) {
                                        results.predictable.issues.push(`Element ${index + 1} may cause unexpected context change on focus`);
                                }
                        });

                        // 3.3.1 Error Identification (Level A)
                        const formElements = document.querySelectorAll('input, select, textarea');
                        formElements.forEach((element, index) => {
                                const required = element.hasAttribute('required');
                                const ariaRequired = element.getAttribute('aria-required') === 'true';
                                const ariaInvalid = element.getAttribute('aria-invalid');

                                if (required || ariaRequired) {
                                        // Check if there's associated error messaging
                                        const describedBy = element.getAttribute('aria-describedby');
                                        if (!describedBy) {
                                                results.inputAssistance.issues.push(`Required field ${index + 1} lacks error description mechanism`);
                                        }
                                }
                        });

                        // 3.3.2 Labels or Instructions (Level A)
                        const inputs = document.querySelectorAll('input, select, textarea');
                        inputs.forEach((input, index) => {
                                const label = document.querySelector(`label[for="${input.id}"]`);
                                const ariaLabel = input.getAttribute('aria-label');
                                const ariaLabelledBy = input.getAttribute('aria-labelledby');

                                if (!label && !ariaLabel && !ariaLabelledBy && input.type !== 'hidden') {
                                        results.inputAssistance.issues.push(`Form control ${index + 1} lacks accessible label`);
                                }
                        });

                        return results;
                });

                this.results.tests.understandable = understandableTests;
        }

        async testRobust(page) {
                const robustTests = await page.evaluate(() => {
                        const results = {
                                compatible: { issues: [] },
                                parsing: { issues: [] }
                        };

                        // 4.1.1 Parsing (Level A) - Basic HTML validation
                        const duplicateIds = [];
                        const allIds = document.querySelectorAll('[id]');
                        const idMap = new Map();

                        allIds.forEach(element => {
                                const id = element.id;
                                if (idMap.has(id)) {
                                        duplicateIds.push(id);
                                } else {
                                        idMap.set(id, true);
                                }
                        });

                        if (duplicateIds.length > 0) {
                                results.parsing.issues.push(`Duplicate IDs found: ${duplicateIds.join(', ')}`);
                        }

                        // 4.1.2 Name, Role, Value (Level A)
                        const interactiveElements = document.querySelectorAll('button, input, select, textarea, a[href]');
                        interactiveElements.forEach((element, index) => {
                                const tagName = element.tagName.toLowerCase();
                                const role = element.getAttribute('role');
                                const ariaLabel = element.getAttribute('aria-label');
                                const ariaLabelledBy = element.getAttribute('aria-labelledby');

                                // Check for custom controls
                                if (role && !['button', 'link', 'textbox', 'combobox', 'checkbox', 'radio'].includes(role)) {
                                        // Custom role should have proper ARIA attributes
                                        if (!ariaLabel && !ariaLabelledBy) {
                                                results.compatible.issues.push(`Custom control ${index + 1} with role="${role}" lacks accessible name`);
                                        }
                                }

                                // Check buttons
                                if (tagName === 'button') {
                                        const text = element.textContent.trim();
                                        if (!text && !ariaLabel && !ariaLabelledBy) {
                                                results.compatible.issues.push(`Button ${index + 1} lacks accessible text`);
                                        }
                                }
                        });

                        // Check for ARIA best practices
                        const ariaElements = document.querySelectorAll('[aria-labelledby], [aria-describedby]');
                        ariaElements.forEach((element, index) => {
                                const labelledBy = element.getAttribute('aria-labelledby');
                                const describedBy = element.getAttribute('aria-describedby');

                                if (labelledBy) {
                                        const ids = labelledBy.split(' ');
                                        ids.forEach(id => {
                                                if (!document.getElementById(id)) {
                                                        results.compatible.issues.push(`aria-labelledby references non-existent ID: ${id}`);
                                                }
                                        });
                                }

                                if (describedBy) {
                                        const ids = describedBy.split(' ');
                                        ids.forEach(id => {
                                                if (!document.getElementById(id)) {
                                                        results.compatible.issues.push(`aria-describedby references non-existent ID: ${id}`);
                                                }
                                        });
                                }
                        });

                        return results;
                });

                this.results.tests.robust = robustTests;
        }

        async calculateScore() {
                let totalIssues = 0;
                let maxPossibleScore = 100;

                // Count all issues across all test categories
                Object.values(this.results.tests).forEach(category => {
                        Object.values(category).forEach(test => {
                                if (test.issues) {
                                        totalIssues += test.issues.length;
                                }
                        });
                });

                // Calculate score (each issue reduces score)
                const score = Math.max(0, maxPossibleScore - (totalIssues * 2));

                let level = 'Non-compliant';
                if (score >= 95) level = 'AAA';
                else if (score >= 85) level = 'AA';
                else if (score >= 70) level = 'A';

                this.results.compliance = {
                        score,
                        level,
                        totalIssues,
                        issues: this.getAllIssues()
                };
        }

        getAllIssues() {
                const allIssues = [];

                Object.entries(this.results.tests).forEach(([category, tests]) => {
                        Object.entries(tests).forEach(([testName, testResult]) => {
                                if (testResult.issues) {
                                        testResult.issues.forEach(issue => {
                                                allIssues.push({
                                                        category,
                                                        test: testName,
                                                        issue,
                                                        severity: this.getIssueSeverity(issue)
                                                });
                                        });
                                }
                        });
                });

                return allIssues;
        }

        getIssueSeverity(issue) {
                const criticalKeywords = ['missing alt', 'no skip links', 'duplicate ids', 'missing label'];
                const warningKeywords = ['may have', 'potential', 'might'];

                const issueLower = issue.toLowerCase();

                if (criticalKeywords.some(keyword => issueLower.includes(keyword))) {
                        return 'critical';
                } else if (warningKeywords.some(keyword => issueLower.includes(keyword))) {
                        return 'warning';
                } else {
                        return 'moderate';
                }
        }

        async generateReport() {
                const reportPath = path.join(__dirname, '..', 'reports', 'accessibility-audit.json');
                const reportDir = path.dirname(reportPath);

                if (!fs.existsSync(reportDir)) {
                        fs.mkdirSync(reportDir, { recursive: true });
                }

                fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));

                console.log(`\n♿ WCAG 2.1 AA Compliance Audit Complete!`);
                console.log(`📄 Report saved to: ${reportPath}`);
                console.log(`\n🎯 Compliance Summary:`);
                console.log(`   Overall Score: ${this.results.compliance.score}/100`);
                console.log(`   Compliance Level: ${this.results.compliance.level}`);
                console.log(`   Total Issues: ${this.results.compliance.totalIssues}`);

                // Categorize issues by severity
                const criticalIssues = this.results.compliance.issues.filter(i => i.severity === 'critical');
                const moderateIssues = this.results.compliance.issues.filter(i => i.severity === 'moderate');
                const warningIssues = this.results.compliance.issues.filter(i => i.severity === 'warning');

                if (criticalIssues.length > 0) {
                        console.log(`\n🚨 Critical Issues (${criticalIssues.length}):`);
                        criticalIssues.slice(0, 5).forEach(issue => {
                                console.log(`   - ${issue.issue}`);
                        });
                }

                if (moderateIssues.length > 0) {
                        console.log(`\n⚠️  Moderate Issues (${moderateIssues.length}):`);
                        moderateIssues.slice(0, 5).forEach(issue => {
                                console.log(`   - ${issue.issue}`);
                        });
                }

                if (warningIssues.length > 0) {
                        console.log(`\n💡 Warnings (${warningIssues.length}):`);
                        warningIssues.slice(0, 3).forEach(issue => {
                                console.log(`   - ${issue.issue}`);
                        });
                }

                // Recommendations
                console.log(`\n📋 Priority Recommendations:`);
                if (criticalIssues.length > 0) {
                        console.log(`   1. Fix ${criticalIssues.length} critical accessibility issues`);
                }
                if (this.results.tests.operable.keyboard.skipLinks.length === 0) {
                        console.log(`   2. Add skip navigation links`);
                }
                if (this.results.tests.perceivable.images.withAlt < this.results.tests.perceivable.images.total) {
                        console.log(`   3. Add alt text to all images`);
                }
                console.log(`   4. Validate with screen reader testing`);
                console.log(`   5. Conduct user testing with disabled users`);
        }
}

// Run the audit
const auditor = new AccessibilityAuditor();
auditor.audit().catch(console.error);
