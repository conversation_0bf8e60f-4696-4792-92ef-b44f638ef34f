/**
 * Performance Audit Script
 * Alternative to Lighthouse CLI for comprehensive performance testing
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class PerformanceAuditor {
        constructor() {
                this.results = {
                        timestamp: new Date().toISOString(),
                        url: 'http://localhost:5000',
                        metrics: {},
                        seo: {},
                        accessibility: {},
                        errors: []
                };
        }

        async audit() {
                console.log('🚀 Starting Performance Audit...');

                const browser = await puppeteer.launch({
                        headless: 'new',
                        args: ['--no-sandbox', '--disable-setuid-sandbox']
                });

                try {
                        const page = await browser.newPage();

                        // Enable performance monitoring
                        await page.setCacheEnabled(false);

                        console.log('📊 Measuring Core Web Vitals...');
                        await this.measureCoreWebVitals(page);

                        console.log('🔍 Checking SEO Elements...');
                        await this.checkSEOElements(page);

                        console.log('♿ Validating Accessibility...');
                        await this.checkAccessibility(page);

                        console.log('📋 Checking Schema.org Structured Data...');
                        await this.validateStructuredData(page);

                        console.log('🖼️ Analyzing Images and Resources...');
                        await this.analyzeResources(page);

                } catch (error) {
                        this.results.errors.push({
                                type: 'AUDIT_ERROR',
                                message: error.message,
                                stack: error.stack
                        });
                } finally {
                        await browser.close();
                }

                await this.generateReport();
        }

        async measureCoreWebVitals(page) {
                await page.goto('http://localhost:5000', { waitUntil: 'networkidle2' });

                // Measure performance metrics
                const metrics = await page.evaluate(() => {
                        return new Promise((resolve) => {
                                // Core Web Vitals measurement
                                const observer = new PerformanceObserver((list) => {
                                        const entries = list.getEntries();
                                        const results = {};

                                        entries.forEach((entry) => {
                                                if (entry.entryType === 'navigation') {
                                                        results.domContentLoaded = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
                                                        results.loadComplete = entry.loadEventEnd - entry.loadEventStart;
                                                        results.firstByte = entry.responseStart - entry.requestStart;
                                                }
                                        });

                                        resolve(results);
                                });

                                observer.observe({ entryTypes: ['navigation'] });

                                // Fallback timeout
                                setTimeout(() => {
                                        resolve({
                                                domContentLoaded: performance.timing?.domContentLoadedEventEnd - performance.timing?.domContentLoadedEventStart || 0,
                                                loadComplete: performance.timing?.loadEventEnd - performance.timing?.loadEventStart || 0,
                                                firstByte: performance.timing?.responseStart - performance.timing?.requestStart || 0
                                        });
                                }, 3000);
                        });
                });

                // Additional performance metrics
                const paintMetrics = await page.evaluate(() => {
                        const paintEntries = performance.getEntriesByType('paint');
                        const result = {};

                        paintEntries.forEach(entry => {
                                if (entry.name === 'first-contentful-paint') {
                                        result.firstContentfulPaint = entry.startTime;
                                }
                                if (entry.name === 'first-paint') {
                                        result.firstPaint = entry.startTime;
                                }
                        });

                        return result;
                });

                this.results.metrics = {
                        ...metrics,
                        ...paintMetrics,
                        pageLoadTime: Date.now()
                };
        }

        async checkSEOElements(page) {
                const seoData = await page.evaluate(() => {
                        const result = {
                                title: document.title || '',
                                metaDescription: '',
                                headings: [],
                                images: [],
                                links: [],
                                structuredData: []
                        };

                        // Meta description
                        const metaDesc = document.querySelector('meta[name="description"]');
                        result.metaDescription = metaDesc ? metaDesc.getAttribute('content') : '';

                        // Headings structure
                        ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].forEach(tag => {
                                const headings = document.querySelectorAll(tag);
                                headings.forEach(heading => {
                                        result.headings.push({
                                                tag: tag.toUpperCase(),
                                                text: heading.textContent.trim(),
                                                id: heading.id || ''
                                        });
                                });
                        });

                        // Images alt text check
                        const images = document.querySelectorAll('img');
                        images.forEach(img => {
                                result.images.push({
                                        src: img.src,
                                        alt: img.alt || '',
                                        hasAlt: !!img.alt
                                });
                        });

                        // Links analysis
                        const links = document.querySelectorAll('a[href]');
                        links.forEach(link => {
                                result.links.push({
                                        href: link.href,
                                        text: link.textContent.trim(),
                                        hasTitle: !!link.title,
                                        external: link.href.startsWith('http') && !link.href.includes('localhost')
                                });
                        });

                        // Structured data
                        const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
                        jsonLdScripts.forEach(script => {
                                try {
                                        const data = JSON.parse(script.textContent);
                                        result.structuredData.push(data);
                                } catch (e) {
                                        // Invalid JSON-LD
                                }
                        });

                        return result;
                });

                // SEO Score Calculation
                let seoScore = 100;
                const issues = [];

                if (!seoData.title || seoData.title.length < 30) {
                        seoScore -= 10;
                        issues.push('Title tag missing or too short');
                }

                if (!seoData.metaDescription || seoData.metaDescription.length < 120) {
                        seoScore -= 10;
                        issues.push('Meta description missing or too short');
                }

                const h1Count = seoData.headings.filter(h => h.tag === 'H1').length;
                if (h1Count !== 1) {
                        seoScore -= 5;
                        issues.push(`Found ${h1Count} H1 tags, should be exactly 1`);
                }

                const imagesWithoutAlt = seoData.images.filter(img => !img.hasAlt).length;
                if (imagesWithoutAlt > 0) {
                        seoScore -= Math.min(imagesWithoutAlt * 2, 15);
                        issues.push(`${imagesWithoutAlt} images without alt text`);
                }

                if (seoData.structuredData.length === 0) {
                        seoScore -= 10;
                        issues.push('No structured data found');
                }

                this.results.seo = {
                        ...seoData,
                        score: Math.max(seoScore, 0),
                        issues
                };
        }

        async checkAccessibility(page) {
                const accessibilityData = await page.evaluate(() => {
                        const result = {
                                skipLinks: [],
                                ariaLabels: 0,
                                landmarks: [],
                                focusableElements: 0,
                                issues: []
                        };

                        // Skip links
                        const skipLinks = document.querySelectorAll('a[href^="#"]');
                        skipLinks.forEach(link => {
                                if (link.textContent.toLowerCase().includes('skip')) {
                                        result.skipLinks.push({
                                                href: link.href,
                                                text: link.textContent.trim()
                                        });
                                }
                        });

                        // ARIA labels count
                        result.ariaLabels = document.querySelectorAll('[aria-label], [aria-labelledby]').length;

                        // Landmarks
                        const landmarks = document.querySelectorAll('main, nav, header, footer, aside, section[aria-label], section[aria-labelledby]');
                        landmarks.forEach(landmark => {
                                result.landmarks.push({
                                        tag: landmark.tagName.toLowerCase(),
                                        role: landmark.getAttribute('role') || '',
                                        label: landmark.getAttribute('aria-label') || landmark.getAttribute('aria-labelledby') || ''
                                });
                        });

                        // Focusable elements
                        const focusable = document.querySelectorAll('button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])');
                        result.focusableElements = focusable.length;

                        // Basic accessibility issues
                        const imagesWithoutAlt = document.querySelectorAll('img:not([alt])').length;
                        if (imagesWithoutAlt > 0) {
                                result.issues.push(`${imagesWithoutAlt} images without alt attributes`);
                        }

                        const buttonsWithoutLabels = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])').length;
                        if (buttonsWithoutLabels > 0) {
                                result.issues.push(`${buttonsWithoutLabels} buttons without accessible labels`);
                        }

                        return result;
                });

                // Accessibility Score
                let accessibilityScore = 100;

                if (accessibilityData.skipLinks.length === 0) {
                        accessibilityScore -= 5;
                        accessibilityData.issues.push('No skip links found');
                }

                if (accessibilityData.landmarks.length < 3) {
                        accessibilityScore -= 5;
                        accessibilityData.issues.push('Insufficient landmark elements');
                }

                accessibilityScore -= accessibilityData.issues.length * 3;

                this.results.accessibility = {
                        ...accessibilityData,
                        score: Math.max(accessibilityScore, 0)
                };
        }

        async validateStructuredData(page) {
                const structuredDataValidation = await page.evaluate(() => {
                        const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
                        const results = [];

                        jsonLdScripts.forEach((script, index) => {
                                try {
                                        const data = JSON.parse(script.textContent);
                                        const validation = {
                                                index,
                                                valid: true,
                                                type: data['@type'] || 'Unknown',
                                                context: data['@context'] || '',
                                                errors: []
                                        };

                                        // Basic validation
                                        if (!data['@context']) {
                                                validation.valid = false;
                                                validation.errors.push('Missing @context');
                                        }

                                        if (!data['@type']) {
                                                validation.valid = false;
                                                validation.errors.push('Missing @type');
                                        }

                                        // Type-specific validation
                                        if (data['@type'] === 'SportsEvent') {
                                                if (!data.name) validation.errors.push('SportsEvent missing name');
                                                if (!data.startDate) validation.errors.push('SportsEvent missing startDate');
                                                if (!data.homeTeam || !data.awayTeam) validation.errors.push('SportsEvent missing teams');
                                        }

                                        if (data['@type'] === 'Organization') {
                                                if (!data.name) validation.errors.push('Organization missing name');
                                                if (!data.url) validation.errors.push('Organization missing url');
                                        }

                                        results.push(validation);
                                } catch (error) {
                                        results.push({
                                                index,
                                                valid: false,
                                                type: 'Invalid JSON',
                                                errors: ['JSON parsing failed: ' + error.message]
                                        });
                                }
                        });

                        return results;
                });

                this.results.structuredData = structuredDataValidation;
        }

        async analyzeResources(page) {
                // Get resource loading information
                const resources = await page.evaluate(() => {
                        const resources = performance.getEntriesByType('resource');
                        return resources.map(resource => ({
                                name: resource.name,
                                size: resource.transferSize || 0,
                                duration: resource.duration,
                                type: resource.initiatorType
                        }));
                });

                const totalSize = resources.reduce((sum, resource) => sum + resource.size, 0);
                const imageResources = resources.filter(r => r.type === 'img');
                const scriptResources = resources.filter(r => r.type === 'script');
                const styleResources = resources.filter(r => r.type === 'link' || r.name.includes('.css'));

                this.results.resources = {
                        total: resources.length,
                        totalSize: Math.round(totalSize / 1024), // KB
                        images: imageResources.length,
                        scripts: scriptResources.length,
                        styles: styleResources.length,
                        largestResources: resources
                                .sort((a, b) => b.size - a.size)
                                .slice(0, 10)
                                .map(r => ({
                                        name: r.name.split('/').pop(),
                                        size: Math.round(r.size / 1024) + ' KB'
                                }))
                };
        }

        async generateReport() {
                const reportPath = path.join(__dirname, '..', 'reports', 'performance-audit.json');
                const reportDir = path.dirname(reportPath);

                if (!fs.existsSync(reportDir)) {
                        fs.mkdirSync(reportDir, { recursive: true });
                }

                // Calculate overall score
                const overallScore = Math.round(
                        (this.results.seo.score * 0.4) +
                        (this.results.accessibility.score * 0.4) +
                        (this.results.metrics.firstContentfulPaint < 2000 ? 20 : 10)
                );

                const report = {
                        ...this.results,
                        overallScore,
                        summary: {
                                seoScore: this.results.seo.score,
                                accessibilityScore: this.results.accessibility.score,
                                performanceGrade: this.results.metrics.firstContentfulPaint < 2000 ? 'Good' : 'Needs Improvement',
                                structuredDataValid: this.results.structuredData.every(sd => sd.valid)
                        }
                };

                fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
                console.log(`\n📊 Performance Audit Complete!`);
                console.log(`📄 Report saved to: ${reportPath}`);
                console.log(`\n🎯 Results Summary:`);
                console.log(`   Overall Score: ${overallScore}/100`);
                console.log(`   SEO Score: ${this.results.seo.score}/100`);
                console.log(`   Accessibility Score: ${this.results.accessibility.score}/100`);
                console.log(`   Structured Data: ${this.results.structuredData.length} schemas found`);
                console.log(`   Performance: ${report.summary.performanceGrade}`);

                if (this.results.seo.issues.length > 0) {
                        console.log(`\n⚠️  SEO Issues:`);
                        this.results.seo.issues.forEach(issue => console.log(`   - ${issue}`));
                }

                if (this.results.accessibility.issues.length > 0) {
                        console.log(`\n♿ Accessibility Issues:`);
                        this.results.accessibility.issues.forEach(issue => console.log(`   - ${issue}`));
                }
        }
}

// Run the audit
const auditor = new PerformanceAuditor();
auditor.audit().catch(console.error);
