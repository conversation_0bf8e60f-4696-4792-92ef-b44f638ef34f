/**
 * Comprehensive Test Runner for Phase 3 Performance & Testing
 * Runs all audits and generates combined report
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
        constructor() {
                this.results = {
                        timestamp: new Date().toISOString(),
                        phase: 'Phase 3 - Performance & Testing',
                        status: 'running',
                        tests: {
                                performance: { status: 'pending', duration: 0 },
                                accessibility: { status: 'pending', duration: 0 },
                                structuredData: { status: 'pending', duration: 0 }
                        },
                        summary: {}
                };

                this.reportsDir = path.join(__dirname, '..', 'reports');
                if (!fs.existsSync(this.reportsDir)) {
                        fs.mkdirSync(this.reportsDir, { recursive: true });
                }
        }

        async runAllTests() {
                console.log('🚀 Starting Phase 3 - Performance & Testing Suite');
                console.log('='.repeat(60));

                try {
                        // Check if server is running
                        await this.checkServer();

                        // Run performance audit
                        console.log('\n1️⃣ Running Performance Audit...');
                        await this.runTest('performance', 'performance-audit.js');

                        // Run accessibility audit  
                        console.log('\n2️⃣ Running Accessibility Audit...');
                        await this.runTest('accessibility', 'accessibility-audit.js');

                        // Run structured data validation
                        console.log('\n3️⃣ Running Structured Data Validation...');
                        await this.runTest('structuredData', 'structured-data-validator.js');

                        // Generate combined report
                        await this.generateCombinedReport();

                        this.results.status = 'completed';
                        console.log('\n✅ All tests completed successfully!');

                } catch (error) {
                        this.results.status = 'failed';
                        console.error('\n❌ Test suite failed:', error.message);
                }
        }

        async checkServer() {
                return new Promise((resolve, reject) => {
                        const { spawn } = require('child_process');
                        const curl = spawn('curl', ['-f', '-s', 'http://localhost:5000']);

                        curl.on('close', (code) => {
                                if (code === 0) {
                                        console.log('✅ Server is running on localhost:5000');
                                        resolve();
                                } else {
                                        reject(new Error('Server not running on localhost:5000. Please start the server first.'));
                                }
                        });
                });
        }

        async runTest(testName, scriptFile) {
                const startTime = Date.now();
                this.results.tests[testName].status = 'running';

                return new Promise((resolve, reject) => {
                        const scriptPath = path.join(__dirname, scriptFile);
                        const testProcess = spawn('node', [scriptPath], {
                                stdio: 'inherit',
                                cwd: __dirname
                        });

                        testProcess.on('close', (code) => {
                                const duration = Date.now() - startTime;
                                this.results.tests[testName].duration = duration;

                                if (code === 0) {
                                        this.results.tests[testName].status = 'completed';
                                        console.log(`✅ ${testName} audit completed in ${(duration / 1000).toFixed(1)}s`);
                                        resolve();
                                } else {
                                        this.results.tests[testName].status = 'failed';
                                        console.log(`❌ ${testName} audit failed`);
                                        reject(new Error(`${testName} audit failed with code ${code}`));
                                }
                        });

                        testProcess.on('error', (error) => {
                                this.results.tests[testName].status = 'error';
                                reject(error);
                        });
                });
        }

        async generateCombinedReport() {
                console.log('\n📊 Generating Combined Report...');

                // Load individual reports
                const reports = {};

                try {
                        const performanceReport = JSON.parse(
                                fs.readFileSync(path.join(this.reportsDir, 'performance-audit.json'), 'utf8')
                        );
                        reports.performance = performanceReport;
                } catch (error) {
                        console.warn('⚠️  Could not load performance report');
                }

                try {
                        const accessibilityReport = JSON.parse(
                                fs.readFileSync(path.join(this.reportsDir, 'accessibility-audit.json'), 'utf8')
                        );
                        reports.accessibility = accessibilityReport;
                } catch (error) {
                        console.warn('⚠️  Could not load accessibility report');
                }

                try {
                        const structuredDataReport = JSON.parse(
                                fs.readFileSync(path.join(this.reportsDir, 'structured-data-validation.json'), 'utf8')
                        );
                        reports.structuredData = structuredDataReport;
                } catch (error) {
                        console.warn('⚠️  Could not load structured data report');
                }

                // Calculate combined scores and summary
                const summary = this.calculateSummary(reports);

                const combinedReport = {
                        metadata: {
                                timestamp: this.results.timestamp,
                                phase: this.results.phase,
                                url: 'http://localhost:5000'
                        },
                        testExecution: this.results.tests,
                        summary,
                        reports,
                        recommendations: this.generateRecommendations(summary, reports)
                };

                // Save combined report
                const combinedReportPath = path.join(this.reportsDir, 'phase-3-combined-report.json');
                fs.writeFileSync(combinedReportPath, JSON.stringify(combinedReport, null, 2));

                // Generate HTML summary
                await this.generateHTMLSummary(combinedReport);

                // Display summary
                this.displaySummary(summary);
        }

        calculateSummary(reports) {
                const summary = {
                        overallScore: 0,
                        grading: 'F',
                        performance: { score: 0, grade: 'N/A' },
                        seo: { score: 0, grade: 'N/A' },
                        accessibility: { score: 0, grade: 'N/A' },
                        structuredData: { score: 0, grade: 'N/A' },
                        criticalIssues: 0,
                        warnings: 0,
                        passedTests: 0,
                        totalTests: Object.keys(this.results.tests).length
                };

                // Performance metrics
                if (reports.performance) {
                        summary.performance.score = reports.performance.overallScore || 0;
                        summary.performance.grade = this.getGrade(summary.performance.score);
                        summary.seo.score = reports.performance.seo?.score || 0;
                        summary.seo.grade = this.getGrade(summary.seo.score);
                }

                // Accessibility metrics
                if (reports.accessibility) {
                        summary.accessibility.score = reports.accessibility.compliance?.score || 0;
                        summary.accessibility.grade = this.getGrade(summary.accessibility.score);
                        summary.criticalIssues += reports.accessibility.compliance?.issues?.filter(i => i.severity === 'critical').length || 0;
                        summary.warnings += reports.accessibility.compliance?.issues?.filter(i => i.severity === 'warning').length || 0;
                }

                // Structured data metrics
                if (reports.structuredData) {
                        summary.structuredData.score = reports.structuredData.validation?.score || 0;
                        summary.structuredData.grade = this.getGrade(summary.structuredData.score);
                }

                // Count passed tests
                Object.values(this.results.tests).forEach(test => {
                        if (test.status === 'completed') summary.passedTests++;
                });

                // Calculate overall score (weighted average)
                const scores = [
                        summary.performance.score * 0.25,  // 25% weight
                        summary.seo.score * 0.25,          // 25% weight  
                        summary.accessibility.score * 0.3,  // 30% weight
                        summary.structuredData.score * 0.2  // 20% weight
                ];

                summary.overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0));
                summary.grading = this.getGrade(summary.overallScore);

                return summary;
        }

        getGrade(score) {
                if (score >= 95) return 'A+';
                if (score >= 90) return 'A';
                if (score >= 85) return 'A-';
                if (score >= 80) return 'B+';
                if (score >= 75) return 'B';
                if (score >= 70) return 'B-';
                if (score >= 65) return 'C+';
                if (score >= 60) return 'C';
                if (score >= 55) return 'C-';
                if (score >= 50) return 'D';
                return 'F';
        }

        generateRecommendations(summary, reports) {
                const recommendations = [];

                // Performance recommendations
                if (summary.performance.score < 80) {
                        recommendations.push({
                                category: 'Performance',
                                priority: 'High',
                                issue: 'Performance score below 80',
                                action: 'Optimize Core Web Vitals, reduce bundle size, implement lazy loading'
                        });
                }

                // SEO recommendations
                if (summary.seo.score < 85) {
                        recommendations.push({
                                category: 'SEO',
                                priority: 'High',
                                issue: 'SEO score below 85',
                                action: 'Improve meta descriptions, heading structure, and image alt text'
                        });
                }

                // Accessibility recommendations
                if (summary.accessibility.score < 90) {
                        recommendations.push({
                                category: 'Accessibility',
                                priority: 'Critical',
                                issue: 'WCAG 2.1 AA compliance below 90',
                                action: 'Fix critical accessibility issues, add skip links, improve ARIA labels'
                        });
                }

                // Structured data recommendations
                if (summary.structuredData.score < 95) {
                        recommendations.push({
                                category: 'Structured Data',
                                priority: 'Medium',
                                issue: 'Schema.org validation below 95',
                                action: 'Complete required schema properties, validate with Google Rich Results Test'
                        });
                }

                // Critical issues
                if (summary.criticalIssues > 0) {
                        recommendations.unshift({
                                category: 'Critical',
                                priority: 'Urgent',
                                issue: `${summary.criticalIssues} critical accessibility issues found`,
                                action: 'Address all critical accessibility issues immediately'
                        });
                }

                return recommendations;
        }

        async generateHTMLSummary(report) {
                const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 3 Audit Report - APISportsGamev2</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .score-card { display: flex; justify-content: space-around; margin: 30px 0; }
        .score { text-align: center; padding: 20px; border-radius: 8px; margin: 10px; }
        .score h3 { margin: 0; font-size: 14px; color: #666; }
        .score .number { font-size: 48px; font-weight: bold; margin: 10px 0; }
        .score .grade { font-size: 18px; color: #666; }
        .grade-A { background: #d4edda; border: 2px solid #28a745; }
        .grade-A .number { color: #28a745; }
        .grade-B { background: #d1ecf1; border: 2px solid #17a2b8; }
        .grade-B .number { color: #17a2b8; }
        .grade-C { background: #fff3cd; border: 2px solid #ffc107; }
        .grade-C .number { color: #ffc107; }
        .grade-D, .grade-F { background: #f8d7da; border: 2px solid #dc3545; }
        .grade-D .number, .grade-F .number { color: #dc3545; }
        .section { margin: 30px 0; }
        .recommendations { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .recommendation { background: white; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; border-radius: 0 4px 4px 0; }
        .priority-Critical, .priority-Urgent { border-left-color: #dc3545; }
        .priority-High { border-left-color: #fd7e14; }
        .priority-Medium { border-left-color: #ffc107; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat { text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; }
        .stat .value { font-size: 24px; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 Phase 3 Performance & Testing Report</h1>
            <p><strong>APISportsGamev2 Home Page Enhancement</strong></p>
            <p>Generated: ${new Date(report.metadata.timestamp).toLocaleString()}</p>
        </div>

        <div class="score-card">
            <div class="score grade-${report.summary.grading.charAt(0)}">
                <h3>OVERALL SCORE</h3>
                <div class="number">${report.summary.overallScore}</div>
                <div class="grade">Grade ${report.summary.grading}</div>
            </div>
            <div class="score grade-${report.summary.performance.grade.charAt(0)}">
                <h3>PERFORMANCE</h3>
                <div class="number">${report.summary.performance.score}</div>
                <div class="grade">Grade ${report.summary.performance.grade}</div>
            </div>
            <div class="score grade-${report.summary.seo.grade.charAt(0)}">
                <h3>SEO</h3>
                <div class="number">${report.summary.seo.score}</div>
                <div class="grade">Grade ${report.summary.seo.grade}</div>
            </div>
            <div class="score grade-${report.summary.accessibility.grade.charAt(0)}">
                <h3>ACCESSIBILITY</h3>
                <div class="number">${report.summary.accessibility.score}</div>
                <div class="grade">Grade ${report.summary.accessibility.grade}</div>
            </div>
        </div>

        <div class="stats">
            <div class="stat">
                <div class="value">${report.summary.passedTests}/${report.summary.totalTests}</div>
                <div>Tests Passed</div>
            </div>
            <div class="stat">
                <div class="value">${report.summary.criticalIssues}</div>
                <div>Critical Issues</div>
            </div>
            <div class="stat">
                <div class="value">${report.summary.warnings}</div>
                <div>Warnings</div>
            </div>
            <div class="stat">
                <div class="value">${report.reports.structuredData?.summary?.totalSchemas || 0}</div>
                <div>Schema Types</div>
            </div>
        </div>

        <div class="section">
            <h2>📋 Test Execution Summary</h2>
            <ul>
                ${Object.entries(report.testExecution).map(([test, result]) =>
                        `<li><strong>${test}</strong>: ${result.status} (${(result.duration / 1000).toFixed(1)}s)</li>`
                ).join('')}
            </ul>
        </div>

        <div class="recommendations">
            <h2>🎯 Priority Recommendations</h2>
            ${report.recommendations.map(rec => `
                <div class="recommendation priority-${rec.priority}">
                    <h4>${rec.category} - ${rec.priority} Priority</h4>
                    <p><strong>Issue:</strong> ${rec.issue}</p>
                    <p><strong>Action:</strong> ${rec.action}</p>
                </div>
            `).join('')}
        </div>

        <div class="section">
            <h2>📊 Detailed Results</h2>
            <h3>✅ Achievements</h3>
            <ul>
                <li>Schema.org structured data implemented with ${report.reports.structuredData?.summary?.validSchemas || 0} valid schemas</li>
                <li>Accessibility features including skip links and ARIA labels</li>
                <li>SEO optimization with proper meta tags and heading structure</li>
                <li>Performance monitoring and Core Web Vitals measurement</li>
            </ul>
            
            <h3>🔧 Next Steps</h3>
            <ol>
                <li>Address critical accessibility issues for WCAG 2.1 AA compliance</li>
                <li>Optimize performance for better Core Web Vitals scores</li>
                <li>Test with Google Rich Results Test tool</li>
                <li>Conduct cross-browser compatibility testing</li>
                <li>Perform user testing with assistive technologies</li>
            </ol>
        </div>

        <div class="section">
            <p><small>Generated by APISportsGamev2 Phase 3 Testing Suite</small></p>
        </div>
    </div>
</body>
</html>`;

                const htmlPath = path.join(this.reportsDir, 'phase-3-summary.html');
                fs.writeFileSync(htmlPath, htmlContent);
                console.log(`📄 HTML summary generated: ${htmlPath}`);
        }

        displaySummary(summary) {
                console.log('\n' + '='.repeat(60));
                console.log('🏆 PHASE 3 TESTING COMPLETE - FINAL RESULTS');
                console.log('='.repeat(60));
                console.log(`Overall Score: ${summary.overallScore}/100 (Grade ${summary.grading})`);
                console.log(`Tests Passed: ${summary.passedTests}/${summary.totalTests}`);
                console.log('');
                console.log('📊 Individual Scores:');
                console.log(`   Performance: ${summary.performance.score}/100 (${summary.performance.grade})`);
                console.log(`   SEO: ${summary.seo.score}/100 (${summary.seo.grade})`);
                console.log(`   Accessibility: ${summary.accessibility.score}/100 (${summary.accessibility.grade})`);
                console.log(`   Structured Data: ${summary.structuredData.score}/100 (${summary.structuredData.grade})`);
                console.log('');
                if (summary.criticalIssues > 0) {
                        console.log(`🚨 Critical Issues: ${summary.criticalIssues}`);
                }
                if (summary.warnings > 0) {
                        console.log(`⚠️  Warnings: ${summary.warnings}`);
                }
                console.log('');
                console.log('📄 Reports generated in: ./reports/');
                console.log('   - phase-3-combined-report.json');
                console.log('   - phase-3-summary.html');
                console.log('   - performance-audit.json');
                console.log('   - accessibility-audit.json');
                console.log('   - structured-data-validation.json');
                console.log('='.repeat(60));
        }
}

// Run the test suite
const runner = new TestRunner();
runner.runAllTests().catch(console.error);
