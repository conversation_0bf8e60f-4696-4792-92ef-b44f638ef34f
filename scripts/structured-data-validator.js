/**
 * Schema.org Structured Data Validation Script
 * Validates JSON-LD structured data against Schema.org specifications
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class StructuredDataValidator {
        constructor() {
                this.results = {
                        timestamp: new Date().toISOString(),
                        url: 'http://localhost:5000',
                        schemas: [],
                        validation: {
                                valid: 0,
                                invalid: 0,
                                warnings: 0,
                                score: 0
                        },
                        googleRichResults: {
                                eligible: [],
                                issues: []
                        }
                };
        }

        async validate() {
                console.log('🔍 Starting Schema.org Structured Data Validation...');

                const browser = await puppeteer.launch({
                        headless: 'new',
                        args: ['--no-sandbox', '--disable-setuid-sandbox']
                });

                try {
                        const page = await browser.newPage();
                        await page.goto('http://localhost:5000', { waitUntil: 'networkidle2' });

                        console.log('📋 Extracting JSON-LD structured data...');
                        await this.extractStructuredData(page);

                        console.log('✅ Validating schema compliance...');
                        await this.validateSchemas();

                        console.log('🔍 Checking Google Rich Results eligibility...');
                        await this.checkGoogleRichResults();

                } catch (error) {
                        console.error('Validation error:', error);
                } finally {
                        await browser.close();
                }

                await this.generateReport();
        }

        async extractStructuredData(page) {
                const structuredData = await page.evaluate(() => {
                        const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
                        const schemas = [];

                        jsonLdScripts.forEach((script, index) => {
                                try {
                                        const data = JSON.parse(script.textContent);
                                        schemas.push({
                                                index,
                                                raw: script.textContent,
                                                parsed: data,
                                                context: data['@context'] || '',
                                                type: data['@type'] || 'Unknown'
                                        });
                                } catch (error) {
                                        schemas.push({
                                                index,
                                                raw: script.textContent,
                                                parsed: null,
                                                error: error.message,
                                                type: 'Invalid JSON'
                                        });
                                }
                        });

                        return schemas;
                });

                this.results.schemas = structuredData;
        }

        async validateSchemas() {
                for (const schema of this.results.schemas) {
                        if (schema.parsed) {
                                const validation = this.validateSchema(schema.parsed, schema.type);
                                schema.validation = validation;

                                if (validation.valid) {
                                        this.results.validation.valid++;
                                } else {
                                        this.results.validation.invalid++;
                                }

                                this.results.validation.warnings += validation.warnings.length;
                        } else {
                                this.results.validation.invalid++;
                        }
                }

                // Calculate overall score
                const total = this.results.validation.valid + this.results.validation.invalid;
                this.results.validation.score = total > 0 ? Math.round((this.results.validation.valid / total) * 100) : 0;
        }

        validateSchema(data, type) {
                const validation = {
                        valid: true,
                        errors: [],
                        warnings: [],
                        recommendations: []
                };

                // Common required fields
                if (!data['@context']) {
                        validation.errors.push('Missing required @context property');
                        validation.valid = false;
                } else if (data['@context'] !== 'https://schema.org') {
                        validation.warnings.push('Context should be "https://schema.org"');
                }

                if (!data['@type']) {
                        validation.errors.push('Missing required @type property');
                        validation.valid = false;
                }

                // Type-specific validation
                switch (type) {
                        case 'SportsEvent':
                                validation.merge = this.validateSportsEvent(data);
                                break;
                        case 'Organization':
                                validation.merge = this.validateOrganization(data);
                                break;
                        case 'WebSite':
                                validation.merge = this.validateWebSite(data);
                                break;
                        case 'BreadcrumbList':
                                validation.merge = this.validateBreadcrumbList(data);
                                break;
                        case 'NewsArticle':
                                validation.merge = this.validateNewsArticle(data);
                                break;
                        case 'Dataset':
                                validation.merge = this.validateDataset(data);
                                break;
                        default:
                                validation.warnings.push(`Unknown schema type: ${type}`);
                }

                // Merge type-specific validation
                if (validation.merge) {
                        validation.errors.push(...validation.merge.errors);
                        validation.warnings.push(...validation.merge.warnings);
                        validation.recommendations.push(...validation.merge.recommendations);
                        if (!validation.merge.valid) validation.valid = false;
                }

                return validation;
        }

        validateSportsEvent(data) {
                const validation = { valid: true, errors: [], warnings: [], recommendations: [] };

                // Required properties for SportsEvent
                const required = ['name', 'startDate'];
                required.forEach(prop => {
                        if (!data[prop]) {
                                validation.errors.push(`SportsEvent missing required property: ${prop}`);
                                validation.valid = false;
                        }
                });

                // Recommended properties
                const recommended = ['location', 'homeTeam', 'awayTeam', 'sport'];
                recommended.forEach(prop => {
                        if (!data[prop]) {
                                validation.recommendations.push(`SportsEvent should include: ${prop}`);
                        }
                });

                // Validate startDate format
                if (data.startDate) {
                        const dateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
                        if (!dateRegex.test(data.startDate)) {
                                validation.warnings.push('startDate should be in ISO 8601 format');
                        }
                }

                // Validate location structure
                if (data.location) {
                        if (typeof data.location === 'object') {
                                if (!data.location['@type'] || data.location['@type'] !== 'Place') {
                                        validation.warnings.push('location should have @type: "Place"');
                                }
                                if (!data.location.name) {
                                        validation.warnings.push('location should have a name');
                                }
                        }
                }

                // Validate teams structure
                ['homeTeam', 'awayTeam'].forEach(team => {
                        if (data[team]) {
                                if (typeof data[team] === 'object') {
                                        if (!data[team]['@type'] || data[team]['@type'] !== 'SportsTeam') {
                                                validation.warnings.push(`${team} should have @type: "SportsTeam"`);
                                        }
                                        if (!data[team].name) {
                                                validation.warnings.push(`${team} should have a name`);
                                        }
                                }
                        }
                });

                // Check for eventStatus (useful for live events)
                if (!data.eventStatus) {
                        validation.recommendations.push('Consider adding eventStatus (e.g., "EventScheduled", "EventPostponed")');
                }

                return validation;
        }

        validateOrganization(data) {
                const validation = { valid: true, errors: [], warnings: [], recommendations: [] };

                // Required properties
                const required = ['name'];
                required.forEach(prop => {
                        if (!data[prop]) {
                                validation.errors.push(`Organization missing required property: ${prop}`);
                                validation.valid = false;
                        }
                });

                // Recommended properties
                const recommended = ['url', 'logo', 'description', 'sameAs'];
                recommended.forEach(prop => {
                        if (!data[prop]) {
                                validation.recommendations.push(`Organization should include: ${prop}`);
                        }
                });

                // Validate URL format
                if (data.url) {
                        try {
                                new URL(data.url);
                        } catch {
                                validation.warnings.push('Organization URL should be a valid URL');
                        }
                }

                // Validate logo structure
                if (data.logo) {
                        if (typeof data.logo === 'object') {
                                if (!data.logo['@type'] || data.logo['@type'] !== 'ImageObject') {
                                        validation.warnings.push('logo should have @type: "ImageObject"');
                                }
                                if (!data.logo.url) {
                                        validation.warnings.push('logo should have a url property');
                                }
                        }
                }

                return validation;
        }

        validateWebSite(data) {
                const validation = { valid: true, errors: [], warnings: [], recommendations: [] };

                // Required properties
                if (!data.name) {
                        validation.errors.push('WebSite missing required property: name');
                        validation.valid = false;
                }

                if (!data.url) {
                        validation.errors.push('WebSite missing required property: url');
                        validation.valid = false;
                }

                // Validate search action
                if (data.potentialAction) {
                        const searchAction = Array.isArray(data.potentialAction)
                                ? data.potentialAction.find(action => action['@type'] === 'SearchAction')
                                : data.potentialAction['@type'] === 'SearchAction' ? data.potentialAction : null;

                        if (searchAction) {
                                if (!searchAction.target) {
                                        validation.warnings.push('SearchAction should include target property');
                                }
                                if (!searchAction['query-input']) {
                                        validation.warnings.push('SearchAction should include query-input property');
                                }
                        }
                } else {
                        validation.recommendations.push('WebSite should include potentialAction for search');
                }

                return validation;
        }

        validateBreadcrumbList(data) {
                const validation = { valid: true, errors: [], warnings: [], recommendations: [] };

                // Required properties
                if (!data.itemListElement || !Array.isArray(data.itemListElement)) {
                        validation.errors.push('BreadcrumbList missing required itemListElement array');
                        validation.valid = false;
                        return validation;
                }

                // Validate each breadcrumb item
                data.itemListElement.forEach((item, index) => {
                        if (!item['@type'] || item['@type'] !== 'ListItem') {
                                validation.warnings.push(`Breadcrumb item ${index + 1} should have @type: "ListItem"`);
                        }

                        if (!item.position) {
                                validation.errors.push(`Breadcrumb item ${index + 1} missing position`);
                                validation.valid = false;
                        }

                        if (!item.item) {
                                validation.errors.push(`Breadcrumb item ${index + 1} missing item property`);
                                validation.valid = false;
                        } else {
                                if (typeof item.item === 'object') {
                                        if (!item.item['@id'] && !item.item.url) {
                                                validation.warnings.push(`Breadcrumb item ${index + 1} should have @id or url`);
                                        }
                                        if (!item.item.name) {
                                                validation.warnings.push(`Breadcrumb item ${index + 1} should have name`);
                                        }
                                }
                        }
                });

                return validation;
        }

        validateNewsArticle(data) {
                const validation = { valid: true, errors: [], warnings: [], recommendations: [] };

                // Required properties
                const required = ['headline', 'author', 'datePublished'];
                required.forEach(prop => {
                        if (!data[prop]) {
                                validation.errors.push(`NewsArticle missing required property: ${prop}`);
                                validation.valid = false;
                        }
                });

                // Validate author structure
                if (data.author) {
                        if (typeof data.author === 'object') {
                                if (!data.author['@type'] || !['Person', 'Organization'].includes(data.author['@type'])) {
                                        validation.warnings.push('author should have @type: "Person" or "Organization"');
                                }
                                if (!data.author.name) {
                                        validation.warnings.push('author should have a name');
                                }
                        }
                }

                // Validate date format
                if (data.datePublished) {
                        const dateRegex = /^\d{4}-\d{2}-\d{2}/;
                        if (!dateRegex.test(data.datePublished)) {
                                validation.warnings.push('datePublished should be in ISO 8601 format');
                        }
                }

                // Recommended properties
                const recommended = ['image', 'description', 'publisher'];
                recommended.forEach(prop => {
                        if (!data[prop]) {
                                validation.recommendations.push(`NewsArticle should include: ${prop}`);
                        }
                });

                return validation;
        }

        validateDataset(data) {
                const validation = { valid: true, errors: [], warnings: [], recommendations: [] };

                // Required properties
                const required = ['name', 'description'];
                required.forEach(prop => {
                        if (!data[prop]) {
                                validation.errors.push(`Dataset missing required property: ${prop}`);
                                validation.valid = false;
                        }
                });

                // Recommended properties
                const recommended = ['creator', 'dateCreated', 'keywords', 'license'];
                recommended.forEach(prop => {
                        if (!data[prop]) {
                                validation.recommendations.push(`Dataset should include: ${prop}`);
                        }
                });

                return validation;
        }

        async checkGoogleRichResults() {
                // Check eligibility for Google Rich Results
                for (const schema of this.results.schemas) {
                        if (!schema.parsed) continue;

                        const type = schema.type;
                        const data = schema.parsed;

                        switch (type) {
                                case 'SportsEvent':
                                        if (this.isSportsEventEligible(data)) {
                                                this.results.googleRichResults.eligible.push({
                                                        type: 'Sports Event',
                                                        features: ['Event rich snippet', 'Knowledge panel']
                                                });
                                        } else {
                                                this.results.googleRichResults.issues.push('SportsEvent needs more complete data for rich results');
                                        }
                                        break;

                                case 'Organization':
                                        if (this.isOrganizationEligible(data)) {
                                                this.results.googleRichResults.eligible.push({
                                                        type: 'Organization',
                                                        features: ['Knowledge panel', 'Logo in search']
                                                });
                                        }
                                        break;

                                case 'WebSite':
                                        if (data.potentialAction) {
                                                this.results.googleRichResults.eligible.push({
                                                        type: 'Sitelinks Search Box',
                                                        features: ['Search box in results']
                                                });
                                        }
                                        break;

                                case 'BreadcrumbList':
                                        this.results.googleRichResults.eligible.push({
                                                type: 'Breadcrumbs',
                                                features: ['Breadcrumb trail in search results']
                                        });
                                        break;

                                case 'NewsArticle':
                                        if (this.isNewsArticleEligible(data)) {
                                                this.results.googleRichResults.eligible.push({
                                                        type: 'Article',
                                                        features: ['Article rich snippet', 'Top stories carousel']
                                                });
                                        }
                                        break;
                        }
                }
        }

        isSportsEventEligible(data) {
                return data.name && data.startDate && data.location && (data.homeTeam || data.awayTeam);
        }

        isOrganizationEligible(data) {
                return data.name && data.url;
        }

        isNewsArticleEligible(data) {
                return data.headline && data.author && data.datePublished && data.image;
        }

        async generateReport() {
                const reportPath = path.join(__dirname, '..', 'reports', 'structured-data-validation.json');
                const reportDir = path.dirname(reportPath);

                if (!fs.existsSync(reportDir)) {
                        fs.mkdirSync(reportDir, { recursive: true });
                }

                // Generate summary
                const summary = {
                        totalSchemas: this.results.schemas.length,
                        validSchemas: this.results.validation.valid,
                        invalidSchemas: this.results.validation.invalid,
                        warnings: this.results.validation.warnings,
                        score: this.results.validation.score,
                        richResultsEligible: this.results.googleRichResults.eligible.length,
                        schemaTypes: [...new Set(this.results.schemas.map(s => s.type))]
                };

                const fullReport = {
                        ...this.results,
                        summary
                };

                fs.writeFileSync(reportPath, JSON.stringify(fullReport, null, 2));

                console.log(`\n🔍 Schema.org Structured Data Validation Complete!`);
                console.log(`📄 Report saved to: ${reportPath}`);
                console.log(`\n📊 Validation Summary:`);
                console.log(`   Total Schemas: ${summary.totalSchemas}`);
                console.log(`   Valid Schemas: ${summary.validSchemas}`);
                console.log(`   Invalid Schemas: ${summary.invalidSchemas}`);
                console.log(`   Validation Score: ${summary.score}/100`);
                console.log(`   Schema Types: ${summary.schemaTypes.join(', ')}`);
                console.log(`   Rich Results Eligible: ${summary.richResultsEligible}`);

                if (summary.warnings > 0) {
                        console.log(`   Warnings: ${summary.warnings}`);
                }

                // Show Google Rich Results eligibility
                if (this.results.googleRichResults.eligible.length > 0) {
                        console.log(`\n🎯 Google Rich Results Eligible:`);
                        this.results.googleRichResults.eligible.forEach(result => {
                                console.log(`   - ${result.type}: ${result.features.join(', ')}`);
                        });
                }

                // Show critical issues
                const criticalIssues = this.results.schemas
                        .filter(s => s.validation && !s.validation.valid)
                        .map(s => s.validation.errors)
                        .flat();

                if (criticalIssues.length > 0) {
                        console.log(`\n🚨 Critical Issues:`);
                        criticalIssues.slice(0, 5).forEach(issue => {
                                console.log(`   - ${issue}`);
                        });
                }

                // Recommendations
                console.log(`\n💡 Recommendations:`);
                if (summary.score < 100) {
                        console.log(`   1. Fix validation errors to improve score`);
                }
                if (this.results.googleRichResults.eligible.length < this.results.schemas.length) {
                        console.log(`   2. Enhance schema data for better rich results eligibility`);
                }
                console.log(`   3. Test with Google's Rich Results Test tool`);
                console.log(`   4. Monitor Google Search Console for structured data errors`);
        }
}

// Run the validation
const validator = new StructuredDataValidator();
validator.validate().catch(console.error);
