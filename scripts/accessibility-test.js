/**
 * Accessibility Audit Script using <PERSON>wright and axe-core
 * WCAG 2.1 AA Compliance Testing
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function runAccessibilityAudit() {
        console.log('🚀 Starting Accessibility Audit with Playwright + axe-core...');

        const browser = await chromium.launch({
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();

        try {
                // Navigate to the home page
                console.log('📱 Loading home page...');
                await page.goto('http://localhost:5000', { waitUntil: 'networkidle' });

                // Inject axe-core
                console.log('🔧 Injecting axe-core...');
                await page.addScriptTag({
                        url: 'https://unpkg.com/axe-core@4.8.2/axe.min.js'
                });

                // Run axe accessibility scan
                console.log('🔍 Running accessibility scan...');
                const results = await page.evaluate(() => {
                        return new Promise((resolve) => {
                                window.axe.run({
                                        tags: ['wcag2a', 'wcag2aa', 'wcag21aa']
                                }, (err, results) => {
                                        if (err) throw err;
                                        resolve(results);
                                });
                        });
                });

                // Generate report
                const report = {
                        timestamp: new Date().toISOString(),
                        url: 'http://localhost:5000',
                        summary: {
                                violations: results.violations.length,
                                passes: results.passes.length,
                                incomplete: results.incomplete.length,
                                inapplicable: results.inapplicable.length
                        },
                        violations: results.violations,
                        passes: results.passes.slice(0, 10), // Limit passes for readability
                        incomplete: results.incomplete
                };

                // Save detailed report
                const reportPath = path.join(__dirname, '..', 'reports', 'accessibility-audit.json');
                fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

                // Console output
                console.log('\n📊 ACCESSIBILITY AUDIT RESULTS');
                console.log('===============================');
                console.log(`✅ Passes: ${report.summary.passes}`);
                console.log(`❌ Violations: ${report.summary.violations}`);
                console.log(`⚠️  Incomplete: ${report.summary.incomplete}`);
                console.log(`ℹ️  Not Applicable: ${report.summary.inapplicable}`);

                if (report.violations.length > 0) {
                        console.log('\n🚨 VIOLATIONS FOUND:');
                        console.log('===================');
                        report.violations.forEach((violation, index) => {
                                console.log(`\n${index + 1}. ${violation.description}`);
                                console.log(`   Impact: ${violation.impact}`);
                                console.log(`   Tags: ${violation.tags.join(', ')}`);
                                console.log(`   Help: ${violation.helpUrl}`);
                                console.log(`   Elements affected: ${violation.nodes.length}`);
                        });
                } else {
                        console.log('\n🎉 NO ACCESSIBILITY VIOLATIONS FOUND!');
                }

                console.log(`\n📄 Detailed report saved to: ${reportPath}`);

                // Test keyboard navigation
                console.log('\n⌨️  Testing keyboard navigation...');
                await testKeyboardNavigation(page);

                // Test screen reader compatibility
                console.log('\n🗣️  Testing screen reader compatibility...');
                await testScreenReaderCompatibility(page);

        } catch (error) {
                console.error('❌ Audit failed:', error);
        } finally {
                await browser.close();
        }
}

async function testKeyboardNavigation(page) {
        const tabStops = await page.evaluate(() => {
                const tabbable = Array.from(document.querySelectorAll(
                        'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
                ));
                return tabbable.length;
        });

        console.log(`   Found ${tabStops} tabbable elements`);

        // Test tab navigation
        let tabCount = 0;
        try {
                for (let i = 0; i < Math.min(10, tabStops); i++) {
                        await page.keyboard.press('Tab');
                        tabCount++;
                }
                console.log(`   ✅ Successfully navigated ${tabCount} tab stops`);
        } catch (error) {
                console.log(`   ⚠️  Tab navigation issue after ${tabCount} stops`);
        }
}

async function testScreenReaderCompatibility(page) {
        const ariaLabels = await page.evaluate(() => {
                const elementsWithAria = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby]');
                return elementsWithAria.length;
        });

        const semanticElements = await page.evaluate(() => {
                const semantic = document.querySelectorAll('main, nav, section, article, aside, header, footer');
                return semantic.length;
        });

        const headingStructure = await page.evaluate(() => {
                const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
                return headings.map(h => ({
                        level: parseInt(h.tagName.charAt(1)),
                        text: h.textContent.trim().substring(0, 50)
                }));
        });

        console.log(`   ✅ ${ariaLabels} elements with ARIA labels`);
        console.log(`   ✅ ${semanticElements} semantic HTML elements`);
        console.log(`   ✅ ${headingStructure.length} headings found`);

        if (headingStructure.length > 0) {
                console.log('   📝 Heading structure:');
                headingStructure.forEach(heading => {
                        console.log(`      H${heading.level}: ${heading.text}`);
                });
        }
}

// Run the audit
runAccessibilityAudit().catch(console.error);
